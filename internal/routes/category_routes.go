package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/gin-gonic/gin"
)

// RegisterCategoryRoutes 注册分类相关路由
func RegisterCategoryRoutes(
	apiGroup *gin.RouterGroup,
	categoryController *controllers.CategoryController,
) {
	// 分类路由组（无需认证）
	categoryGroup := apiGroup.Group("/categories")
	{
		// 获取所有分类数据
		categoryGroup.GET("", categoryController.GetAllCategories)
		// 根据slug获取分类详情
		categoryGroup.GET("/:slug", categoryController.GetCategoryBySlug)
		// 根据slug获取分类TDK信息
		categoryGroup.GET("/tdk/:slug", func(ctx *gin.Context) {
			req := &dto.GetCategoryTdkBySlugRequest{
				Slug: ctx.Param("slug"),
			}
			categoryController.GetCategoryTdkBySlug(ctx, req)
		})
		// 根据slug获取示例列表
		categoryGroup.GET("/:slug/examples", categoryController.GetExamplesBySlug)
	}
}
