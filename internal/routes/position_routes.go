package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/gin-gonic/gin"
)

// RegisterPositionRoutes 注册职位相关路由
func RegisterPositionRoutes(
	apiGroup *gin.RouterGroup,
	positionController *controllers.PositionController,
) {
	// 职位路由组（无需认证）
	positionGroup := apiGroup.Group("/positions")
	{
		// 获取所有职位数据（层级结构）
		positionGroup.GET("", positionController.GetAllPositions)
		// 根据一级分类ID获取下面的二级三级分类
		positionGroup.GET("/:parent_id/children", middleware.WithValidation(positionController.GetPositionsByParentID))
		// 搜索职位关键字
		positionGroup.GET("/search/:keyword", middleware.WithValidation(positionController.SearchPositions))
	}
}
