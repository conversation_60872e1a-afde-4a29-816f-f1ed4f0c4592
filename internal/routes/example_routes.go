package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterExampleRoutes 注册示例相关路由
func RegisterExampleRoutes(
	apiGroup *gin.RouterGroup,
	exampleController *controllers.ExampleController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 示例路由组（无需认证）
	exampleGroup := apiGroup.Group("/examples")
	{
		// 获取首页示例列表
		exampleGroup.GET("/homepage", exampleController.GetHomePageExamples)

		// 根据ID获取示例详情
		exampleGroup.GET("/id/:id", middleware.WithValidation(exampleController.GetExampleById))

		// 根据ID获取示例TDK信息
		exampleGroup.GET("/tdk/:id", middleware.WithValidation(exampleController.GetExampleTdkById))

		// 获取模板推荐列表（带永久缓存）
		exampleGroup.GET("/recommendations", exampleController.GetTemplateRecommendationsWithCache)

		// 获取模板推荐列表（不带缓存）
		exampleGroup.GET("/recommendations/fresh", exampleController.GetTemplateRecommendations)

		// 使用例子创建简历（使用游客中间件，允许游客和登录用户访问）
		exampleGroup.POST("/use", middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient), middleware.WithValidation(exampleController.UseExample))
	}
}
