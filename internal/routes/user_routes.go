package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterUserRoutes 注册用户相关路由
func RegisterUserRoutes(
	router *gin.RouterGroup,
	userController *controllers.UserController,
	authController *controllers.AuthController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 用户路由组
	userGroup := router.Group("/users")

	// 游客路由组，使用游客中间件
	guestGroup := userGroup.Group("/guest")
	guestGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 获取游客用户信息
		guestGroup.GET("/info", middleware.WithoutValidation(userController.GetGuestInfo))
	}

	// 登录用户路由组，使用JWT用户认证中间件
	userGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 获取当前登录用户信息
		userGroup.GET("/info", middleware.WithoutValidation(userController.GetUserInfo))
		// 修改用户名
		userGroup.PUT("/username", middleware.WithValidation(userController.UpdateUsername))
		// 修改用户头像
		userGroup.PUT("/avatar", middleware.WithValidation(userController.UpdateAvatar))
		// 发送绑定手机号验证码
		userGroup.POST("/bind-phone-code", middleware.WithValidation(authController.SendBindPhoneCode))
		// 绑定手机号
		userGroup.POST("/bind-phone", middleware.WithValidation(userController.BindPhone))
		// 发送绑定邮箱验证码
		userGroup.POST("/bind-email-code", middleware.WithValidation(authController.SendBindEmailCode))
		// 绑定邮箱
		userGroup.POST("/bind-email", middleware.WithValidation(userController.BindEmail))
		// 获取绑定微信二维码
		userGroup.GET("/bind-wechat/qrcode", middleware.WithoutValidation(userController.GetBindWechatQrCode))
		// 检查绑定微信二维码状态
		userGroup.POST("/bind-wechat/qrcode/status", middleware.WithValidation(userController.CheckBindWechatQrCodeStatus))
		// 获取用户可用的下载券数量
		userGroup.GET("/download-coupons/count", middleware.WithoutValidation(userController.GetAvailableCouponsCount))
		// 退出登录
		userGroup.POST("/logout", middleware.WithoutValidation(userController.Logout))
	}
}
