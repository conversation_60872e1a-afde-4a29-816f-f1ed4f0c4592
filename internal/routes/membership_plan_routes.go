package routes

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/gin-gonic/gin"
)

// RegisterMembershipPlanRoutes 注册会员套餐相关路由
func RegisterMembershipPlanRoutes(
	router *gin.RouterGroup,
	planController *api.MembershipPlanController,
) {
	// 会员套餐相关路由组
	planGroup := router.Group("/membership-plans")
	{
		// 获取所有可见的会员套餐（无需认证）
		planGroup.GET("", planController.GetVisiblePlans)
	}

	// 下载券套餐相关路由组
	downloadCouponGroup := router.Group("/download-coupon-plans")
	{
		// 获取所有可见的下载券套餐（无需认证）
		downloadCouponGroup.GET("", planController.GetVisibleDownloadCouponPlans)
	}
}
