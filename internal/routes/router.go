package routes

import (
	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RouteInitializer 路由初始化接口
type RouteInitializer interface {
	// Initialize 初始化路由
	Initialize(engine *gin.Engine)
}

// Router 路由结构体
type Router struct {
	cfg                      *config.Config
	userController           *controllers.UserController
	authController           *controllers.AuthController
	wechatController         *controllers.WechatController
	commonController         *controllers.CommonController
	uploadController         *controllers.UploadController
	planController           *controllers.MembershipPlanController
	orderController          *controllers.OrderController
	openAPIController        *controllers.OpenAPIController
	categoryController       *controllers.CategoryController
	positionController       *controllers.PositionController
	resumeController         *controllers.ResumeController
	aiController             *controllers.AIController
	exampleController        *controllers.ExampleController
	templateController       *controllers.TemplateController
	targetPositionController *controllers.TargetPositionController
	jwtService               pkg.JWTService
	userService              services.UserService
	userRepo                 repository.UserRepository
	redisClient              *redis.Client
}

// NewRouter 创建路由结构体
func NewRouter(
	cfg *config.Config,
	userController *controllers.UserController,
	authController *controllers.AuthController,
	wechatController *controllers.WechatController,
	commonController *controllers.CommonController,
	uploadController *controllers.UploadController,
	planController *controllers.MembershipPlanController,
	orderController *controllers.OrderController,
	openAPIController *controllers.OpenAPIController,
	categoryController *controllers.CategoryController,
	positionController *controllers.PositionController,
	resumeController *controllers.ResumeController,
	aiController *controllers.AIController,
	exampleController *controllers.ExampleController,
	templateController *controllers.TemplateController,
	targetPositionController *controllers.TargetPositionController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) *Router {
	return &Router{
		cfg:                      cfg,
		userController:           userController,
		authController:           authController,
		wechatController:         wechatController,
		commonController:         commonController,
		uploadController:         uploadController,
		planController:           planController,
		orderController:          orderController,
		openAPIController:        openAPIController,
		categoryController:       categoryController,
		positionController:       positionController,
		resumeController:         resumeController,
		aiController:             aiController,
		exampleController:        exampleController,
		templateController:       templateController,
		targetPositionController: targetPositionController,
		jwtService:               jwtService,
		userService:              userService,
		userRepo:                 userRepo,
		redisClient:              redisClient,
	}
}

// Initialize 实现RouteInitializer接口
func (r *Router) Initialize(engine *gin.Engine) {
	InitRoutes(engine, r.cfg, r.userController, r.authController, r.wechatController, r.commonController, r.uploadController, r.planController, r.orderController, r.openAPIController, r.categoryController, r.positionController, r.resumeController, r.aiController, r.exampleController, r.templateController, r.targetPositionController, r.jwtService, r.userService, r.userRepo, r.redisClient)
}
