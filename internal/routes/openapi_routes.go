package routes

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/gin-gonic/gin"
)

// RegisterOpenAPIRoutes 注册OpenAPI相关路由
func RegisterOpenAPIRoutes(
	router *gin.RouterGroup,
	openAPIController *api.OpenAPIController,
) {
	// OpenAPI路由组
	openAPIGroup := router.Group("/openapi")
	{
		// 支付宝支付回调
		openAPIGroup.POST("/alipay", openAPIController.AlipayNotify)
		// 微信支付回调
		openAPIGroup.POST("/wechatpay", openAPIController.WechatPayNotify)
	}
}
