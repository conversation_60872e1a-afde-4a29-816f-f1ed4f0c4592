package routes

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterUploadRoutes 注册上传相关路由
func RegisterUploadRoutes(
	router *gin.RouterGroup,
	uploadController *api.UploadController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 上传路由组
	uploadGroup := router.Group("/upload")

	// 使用游客中间件，允许游客和登录用户上传
	uploadGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 上传头像
		uploadGroup.POST("/avatar", middleware.WithValidation(uploadController.UploadAvatar))
		// 上传附件
		uploadGroup.POST("/attachment", middleware.WithValidation(uploadController.UploadAttachment))
	}
}
