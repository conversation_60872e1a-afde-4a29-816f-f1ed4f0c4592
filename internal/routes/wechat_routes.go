package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/gin-gonic/gin"
)

// RegisterWechatRoutes 注册微信相关路由
func RegisterWechatRoutes(router *gin.RouterGroup, wechatController *controllers.WechatController) {
	// 微信接口路由组
	wechatGroup := router.Group("/wechat")
	{
		// 接收微信服务器推送的消息，支持GET(服务器验证)和POST(接收消息)请求
		wechatGroup.GET("/serve", wechatController.HandleServerVerification)
		wechatGroup.POST("/serve", wechatController.HandleMessagePush)
	}
}
