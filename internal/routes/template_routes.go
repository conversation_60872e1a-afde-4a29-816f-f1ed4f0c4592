package routes

import (
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterTemplateRoutes 注册模板相关路由
func RegisterTemplateRoutes(
	apiGroup *gin.RouterGroup,
	templateController *controllers.TemplateController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 模板路由组（无需认证）
	templateGroup := apiGroup.Group("/templates")
	{
		// 获取模板列表（支持分页）
		templateGroup.GET("", middleware.WithValidation(templateController.GetTemplateList))

		// 使用模板（游客认证）
		templateGroup.POST("/use", middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient), middleware.WithValidation(templateController.UseTemplate))
	}
}
