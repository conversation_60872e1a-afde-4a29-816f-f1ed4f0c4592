package routes

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterTargetPositionRoutes 注册目标岗位相关路由
func RegisterTargetPositionRoutes(
	apiGroup *gin.RouterGroup,
	targetPositionController *api.TargetPositionController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 目标岗位路由组（需要JWT认证）
	targetPositionGroup := apiGroup.Group("/target-positions")
	// 使用JWT认证中间件，只有登录用户才能访问
	targetPositionGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 创建目标岗位
		targetPositionGroup.POST("", middleware.WithValidation(targetPositionController.CreateTargetPosition))

		// 获取用户所有目标岗位
		targetPositionGroup.GET("", middleware.WithoutValidation(targetPositionController.GetAllTargetPositions))

		// 根据ID获取目标岗位详情
		targetPositionGroup.GET("/:id", middleware.WithValidation(targetPositionController.GetTargetPositionById))

		// 更新目标岗位
		targetPositionGroup.PUT("/:id", middleware.WithValidation(targetPositionController.UpdateTargetPosition))

		// 删除目标岗位
		targetPositionGroup.DELETE("/:id", middleware.WithValidation(targetPositionController.DeleteTargetPosition))
	}

	// 简历评分路由组（支持游客访问）
	resumeScoreGroup := apiGroup.Group("/resume-scores")
	// 使用游客认证中间件，支持登录用户和游客访问
	resumeScoreGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 获取我的所有简历评分记录
		resumeScoreGroup.GET("", middleware.WithValidation(targetPositionController.GetMyResumeScores))

		// 根据ID获取简历评分详情
		resumeScoreGroup.GET("/:id", middleware.WithValidation(targetPositionController.GetResumeScoreDetail))

		// 删除简历评分记录
		resumeScoreGroup.DELETE("/:id", middleware.WithValidation(targetPositionController.DeleteResumeScore))
	}
}
