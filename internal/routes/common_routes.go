package routes

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterCommonRoutes 注册通用路由
func RegisterCommonRoutes(router *gin.RouterGroup, commonController *api.CommonController, jwtService pkg.JWTService, userService services.UserService, redisClient *redis.Client) {
	// 创建通用路由组
	commonGroup := router.Group("/common")
	{
		// 获取枚举列表 - 不需要认证
		commonGroup.GET("/enums", middleware.WithoutValidation(commonController.GetEnums))
	}
}
