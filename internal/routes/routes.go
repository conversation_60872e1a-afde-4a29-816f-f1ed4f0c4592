package routes

import (
	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/middleware"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// InitRoutes 初始化所有路由
func InitRoutes(
	router *gin.Engine,
	cfg *config.Config,
	userController *controllers.UserController,
	authController *controllers.AuthController,
	wechatController *controllers.WechatController,
	commonController *controllers.CommonController,
	uploadController *controllers.UploadController,
	planController *controllers.MembershipPlanController,
	orderController *controllers.OrderController,
	openAPIController *controllers.OpenAP<PERSON>ontroller,
	categoryController *controllers.<PERSON><PERSON><PERSON><PERSON>er,
	positionController *controllers.PositionController,
	resumeController *controllers.ResumeController,
	aiController *controllers.AIController,
	exampleController *controllers.ExampleController,
	templateController *controllers.TemplateController,
	targetPositionController *controllers.TargetPositionController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 注册全局中间件
	router.Use(middleware.ErrorHandler())   // 错误处理中间件必须放在最前面
	router.Use(middleware.CORSMiddleware()) // 跨域中间件
	router.Use(gin.Recovery())
	router.Use(gin.Logger())

	// API路由组
	apiGroup := router.Group("/api")

	// 注册认证路由
	RegisterAuthRoutes(apiGroup, authController, jwtService, userService, userRepo, redisClient)

	// 注册用户路由
	RegisterUserRoutes(apiGroup, userController, authController, jwtService, userService, userRepo, redisClient)

	// 注册微信路由
	RegisterWechatRoutes(apiGroup, wechatController)

	// 注册通用路由
	RegisterCommonRoutes(apiGroup, commonController, jwtService, userService, redisClient)

	// 注册上传路由
	RegisterUploadRoutes(apiGroup, uploadController, jwtService, userService, userRepo, redisClient)

	// 注册会员套餐路由（无需认证）
	RegisterMembershipPlanRoutes(apiGroup, planController)

	// 注册订单路由
	RegisterOrderRoutes(apiGroup, orderController, jwtService, userService, redisClient)

	// 注册OpenAPI路由
	RegisterOpenAPIRoutes(apiGroup, openAPIController)

	// 注册分类路由（无需认证）
	RegisterCategoryRoutes(apiGroup, categoryController)

	// 注册职位路由（无需认证）
	RegisterPositionRoutes(apiGroup, positionController)

	// 注册简历路由（需要认证）
	RegisterResumeRoutes(apiGroup, resumeController, jwtService, userService, userRepo, redisClient)

	// 注册AI路由（需要认证）
	RegisterAIRoutes(apiGroup, aiController, jwtService, userService, userRepo, redisClient)

	// 注册示例路由（无需认证）
	RegisterExampleRoutes(apiGroup, exampleController, jwtService, userService, userRepo, redisClient)

	// 注册模板路由
	RegisterTemplateRoutes(apiGroup, templateController, jwtService, userService, userRepo, redisClient)

	// 注册目标岗位路由（需要认证）
	RegisterTargetPositionRoutes(apiGroup, targetPositionController, jwtService, userService, userRepo, redisClient)
}
