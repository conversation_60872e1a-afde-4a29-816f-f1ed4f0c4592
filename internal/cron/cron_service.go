package cron

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// CronService 定时任务服务接口
type CronService interface {
	// Start 启动定时任务
	Start(ctx context.Context) error
	// Stop 停止定时任务
	Stop() error
	// IsRunning 检查是否正在运行
	IsRunning() bool
	// AddJob 添加定时任务
	AddJob(spec string, cmd func()) (cron.EntryID, error)
	// RemoveJob 移除定时任务
	RemoveJob(id cron.EntryID)
}

// cronService 定时任务服务实现
type cronService struct {
	config         *config.Config
	logger         *zap.Logger
	cron           *cron.Cron
	orderRepo      repository.OrderRepository
	membershipRepo repository.UserMembershipRepository
	userRepo       repository.UserRepository
	redisClient    *redis.Client
	running        bool
	mu             sync.RWMutex
	ctx            context.Context
	cancel         context.CancelFunc
}

// NewCronService 创建定时任务服务
func NewCronService(config *config.Config, logger *zap.Logger, orderRepo repository.OrderRepository, membershipRepo repository.UserMembershipRepository, userRepo repository.UserRepository, redisClient *redis.Client) CronService {
	// 创建带有秒级精度的 cron 实例
	c := cron.New(cron.WithSeconds())

	return &cronService{
		config:         config,
		logger:         logger,
		cron:           c,
		orderRepo:      orderRepo,
		membershipRepo: membershipRepo,
		userRepo:       userRepo,
		redisClient:    redisClient,
	}
}

// Start 启动定时任务
func (c *cronService) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		c.logger.Warn("定时任务已经在运行中")
		return nil
	}

	c.logger.Info("启动定时任务服务")

	// 创建可取消的上下文
	c.ctx, c.cancel = context.WithCancel(ctx)

	// 添加每1分钟执行一次的订单超时处理任务
	_, err := c.cron.AddFunc("0 * * * * *", func() {
		c.handleTimeoutOrders()
	})
	if err != nil {
		c.logger.Error("添加订单超时处理任务失败", zap.Error(err))
		return err
	}

	// 添加每1小时执行一次的会员状态检查任务
	_, err = c.cron.AddFunc("0 0 * * * *", func() {
		c.handleExpiredMemberships()
	})
	if err != nil {
		c.logger.Error("添加会员状态检查任务失败", zap.Error(err))
		return err
	}

	// 添加每1小时执行一次的在线用户数据清理任务
	_, err = c.cron.AddFunc("0 0 * * * *", func() {
		c.cleanupOnlineUsers()
	})
	if err != nil {
		c.logger.Error("添加在线用户数据清理任务失败", zap.Error(err))
		return err
	}

	// 启动 cron 调度器
	c.cron.Start()
	c.running = true

	// 启动监听上下文取消的 goroutine
	go func() {
		<-c.ctx.Done()
		c.logger.Info("收到上下文取消信号，停止定时任务")
		c.Stop()
	}()

	c.logger.Info("定时任务服务启动成功")
	return nil
}

// Stop 停止定时任务
func (c *cronService) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		c.logger.Warn("定时任务未在运行")
		return nil
	}

	c.logger.Info("正在停止定时任务服务")

	// 停止 cron 调度器
	cronCtx := c.cron.Stop()

	// 等待所有任务完成，最多等待5秒
	select {
	case <-cronCtx.Done():
		c.logger.Info("所有定时任务已完成")
	case <-time.After(5 * time.Second):
		c.logger.Warn("等待定时任务完成超时")
	}

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	c.running = false
	c.logger.Info("定时任务服务已停止")
	return nil
}

// IsRunning 检查是否正在运行
func (c *cronService) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

// AddJob 添加定时任务
func (c *cronService) AddJob(spec string, cmd func()) (cron.EntryID, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return 0, nil
	}

	return c.cron.AddFunc(spec, cmd)
}

// RemoveJob 移除定时任务
func (c *cronService) RemoveJob(id cron.EntryID) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return
	}

	c.cron.Remove(id)
}

// handleTimeoutOrders 处理超时订单
func (c *cronService) handleTimeoutOrders() {
	ctx := context.Background()

	c.logger.Info("开始处理超时订单")

	// 查询超过15分钟的待支付订单
	timeoutOrders, err := c.orderRepo.GetTimeoutPendingOrders(ctx, 15)
	if err != nil {
		c.logger.Error("查询超时订单失败", zap.Error(err))
		return
	}

	if len(timeoutOrders) == 0 {
		c.logger.Info("没有发现超时订单")
		return
	}

	// 提取订单ID
	orderIDs := make([]uint, len(timeoutOrders))
	for i, order := range timeoutOrders {
		orderIDs[i] = order.ID
	}

	// 批量更新订单状态为支付超时
	err = c.orderRepo.BatchUpdatePaymentStatus(ctx, orderIDs, enum.PaymentStatusTimeout, "订单支付超时")
	if err != nil {
		c.logger.Error("批量更新超时订单状态失败", zap.Error(err), zap.Uints("orderIDs", orderIDs))
		return
	}

	c.logger.Info("成功处理超时订单",
		zap.Int("count", len(timeoutOrders)),
		zap.Uints("orderIDs", orderIDs))
}

// handleExpiredMemberships 处理过期会员
func (c *cronService) handleExpiredMemberships() {
	ctx := context.Background()

	c.logger.Info("开始检查过期会员状态")

	// 查询已过期但状态仍为生效中的会员关系
	expiredMemberships, err := c.membershipRepo.GetExpiredActiveMemberships(ctx)
	if err != nil {
		c.logger.Error("查询过期会员失败", zap.Error(err))
		return
	}

	if len(expiredMemberships) == 0 {
		c.logger.Info("没有发现过期会员")
		return
	}

	// 提取会员关系ID和用户ID
	membershipIDs := make([]uint, len(expiredMemberships))
	userIDs := make([]uint, len(expiredMemberships))
	for i, membership := range expiredMemberships {
		membershipIDs[i] = membership.ID
		userIDs[i] = membership.UserID
	}

	// 批量更新会员状态为已过期
	err = c.membershipRepo.BatchUpdateMembershipStatus(ctx, membershipIDs, enum.MembershipStatusExpired)
	if err != nil {
		c.logger.Error("批量更新过期会员状态失败", zap.Error(err), zap.Uints("membershipIDs", membershipIDs))
		return
	}

	// 批量更新用户类型为普通用户
	err = c.userRepo.BatchUpdateUserType(userIDs, enum.UserTypeRegular)
	if err != nil {
		c.logger.Error("批量更新用户类型失败", zap.Error(err), zap.Uints("userIDs", userIDs))
		// 即使用户类型更新失败，会员状态已经更新，所以不返回错误
	}

	c.logger.Info("成功处理过期会员",
		zap.Int("count", len(expiredMemberships)),
		zap.Uints("membershipIDs", membershipIDs),
		zap.Uints("userIDs", userIDs))
}

// cleanupOnlineUsers 清理过期的在线用户数据
func (c *cronService) cleanupOnlineUsers() {
	ctx := context.Background()
	const onlineUsersKey = "resume:online_users"

	c.logger.Info("开始清理过期在线用户数据")

	// 当前时间戳
	now := time.Now().Unix()

	// 清理5分钟前的数据
	fiveMinutesAgo := now - 300 // 5分钟 = 300秒

	// 删除过期的在线用户记录
	removedCount, err := c.redisClient.ZRemRangeByScore(ctx, onlineUsersKey, "0", strconv.FormatInt(fiveMinutesAgo, 10)).Result()
	if err != nil {
		c.logger.Error("清理过期在线用户数据失败", zap.Error(err))
		return
	}

	// 获取清理后的在线用户数
	remainingCount, err := c.redisClient.ZCard(ctx, onlineUsersKey).Result()
	if err != nil {
		c.logger.Warn("获取清理后在线用户数失败", zap.Error(err))
		remainingCount = -1 // 设置为-1表示获取失败
	}

	c.logger.Info("成功清理过期在线用户数据",
		zap.Int64("removed_count", removedCount),
		zap.Int64("remaining_count", remainingCount),
		zap.Int64("cleanup_before_timestamp", fiveMinutesAgo))
}
