package services

import (
	"context"
	"fmt"
	"strconv"
	"time"

	appconfig "github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ResumeService 简历服务接口
type ResumeService interface {
	// GetResumeDetailByID 根据简历ID获取简历详情
	GetResumeDetailByID(ctx context.Context, resumeIDStr string, userID uint) (*vo.ResumeDetailResponse, error)

	// GetResumeDraftDetailByID 根据简历草稿ID获取简历草稿详情
	GetResumeDraftDetailByID(ctx context.Context, draftIDStr string, userID uint) (*vo.ResumeDetailResponse, error)

	// SaveResumeDetail 保存简历详情
	SaveResumeDetail(ctx context.Context, resumeIDStr string, userID uint, resumeData *dto.SaveResumeRequest, jwtToken string, fingerprint string) (*vo.SaveResumeResponse, error)

	// UpdateResumeName 修改简历名称
	UpdateResumeName(ctx context.Context, resumeIDStr string, userID uint, resumeName string) (*vo.UpdateResumeNameResponse, error)

	// RecordOnlineUser 记录在线用户并返回在线人数
	RecordOnlineUser(ctx context.Context, userID uint) (int64, error)

	// DownloadResumePDF 下载简历PDF
	DownloadResumePDF(ctx context.Context, resumeIDStr string, userID uint, jwtToken string, fingerprint string) ([]byte, string, error)

	// GenerateResumePreviewImage 生成简历预览图（异步）
	GenerateResumePreviewImage(resumeID uint, userID uint, jwtToken string, fingerprint string)

	// ShareResumeByEmail 邮件分享简历
	ShareResumeByEmail(ctx context.Context, resumeIDStr string, userID uint, email string, fileName string, jwtToken string, fingerprint string) (*vo.ShareResumeByEmailResponse, error)

	// GetResumeBasicInfo 获取简历基本信息
	GetResumeBasicInfo(ctx context.Context, resumeIDStr string, userID uint) (*vo.GetResumeBasicInfoResponse, error)

	// GetAllMyResumes 获取所有自己的简历
	GetAllMyResumes(ctx context.Context, userID uint) (*vo.GetAllMyResumesResponse, error)

	// GetDeletedResumes 获取回收站中的简历
	GetDeletedResumes(ctx context.Context, userID uint) (*vo.GetDeletedResumesResponse, error)

	// DeleteResume 删除简历
	DeleteResume(ctx context.Context, resumeIDStr string, userID uint) error

	// PermanentlyDeleteResume 物理删除简历
	PermanentlyDeleteResume(ctx context.Context, resumeIDStr string, userID uint) error

	// RestoreResume 恢复简历
	RestoreResume(ctx context.Context, resumeIDStr string, userID uint) error

	// CopyResume 复制简历
	CopyResume(ctx context.Context, resumeIDStr string, userID uint) error

	// CreateResume 创建新简历
	CreateResume(ctx context.Context, resume *models.Resume) error

	// CreateResumeDetail 创建新简历详情
	CreateResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error

	// ApplyDraft 应用草稿到简历
	ApplyDraft(ctx context.Context, resumeIDStr string, draftID uint, userID uint) (*vo.ApplyDraftResponse, error)
}

// resumeService 简历服务实现
type resumeService struct {
	resumeRepo                repository.ResumeRepository
	resumeDraftRepo           repository.ResumeDraftRepository
	templateRepo              repository.TemplateRepository
	chromeService             pkg.ChromeService
	ossService                pkg.OSSService
	emailService              pkg.EmailService
	userService               UserService
	userDownloadCouponService UserDownloadCouponService
	redisClient               *redis.Client
	logger                    *zap.Logger
	frontendDomain            string
}

// NewResumeService 创建简历服务
func NewResumeService(
	resumeRepo repository.ResumeRepository,
	resumeDraftRepo repository.ResumeDraftRepository,
	templateRepo repository.TemplateRepository,
	chromeService pkg.ChromeService,
	ossService pkg.OSSService,
	emailService pkg.EmailService,
	userService UserService,
	userDownloadCouponService UserDownloadCouponService,
	redisClient *redis.Client,
	logger *zap.Logger,
	cfg *appconfig.Config,
) ResumeService {
	return &resumeService{
		resumeRepo:                resumeRepo,
		resumeDraftRepo:           resumeDraftRepo,
		templateRepo:              templateRepo,
		chromeService:             chromeService,
		ossService:                ossService,
		emailService:              emailService,
		userService:               userService,
		userDownloadCouponService: userDownloadCouponService,
		redisClient:               redisClient,
		logger:                    logger,
		frontendDomain:            cfg.FrontendDomain,
	}
}

// GetResumeDetailByID 根据简历ID获取简历详情
func (s *resumeService) GetResumeDetailByID(ctx context.Context, resumeIDStr string, userID uint) (*vo.ResumeDetailResponse, error) {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试访问非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess
	}

	// 获取简历及详情
	resume, resumeDetail, template, err := s.resumeRepo.GetResumeWithDetailByID(ctx, uint(resumeID))

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return nil, exception.ErrResumeNotFound
		}
		s.logger.Error("获取简历详情失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed
	}

	// 构造响应
	response := &vo.ResumeDetailResponse{
		ID:              resume.ID,
		UserID:          resume.UserID,
		TemplateID:      resume.TemplateID,
		ComponentName:   template.ComponentName,
		ResumeName:      resume.ResumeName,
		PreviewImageUrl: resume.PreviewImageUrl,
		CompletionRate:  resume.CompletionRate,
		CreatedAt:       resume.CreatedAt,
		UpdatedAt:       resume.UpdatedAt,
		BasicInfo:       resumeDetail.BasicInfo,
		Education:       resumeDetail.Education,
		Work:            resumeDetail.Work,
		Project:         resumeDetail.Project,
		Research:        resumeDetail.Research,
		Team:            resumeDetail.Team,
		Portfolio:       resumeDetail.Portfolio,
		Other:           resumeDetail.Other,
		PersonalSummary: resumeDetail.PersonalSummary,
		Honors:          resumeDetail.Honors,
		Skills:          resumeDetail.Skills,
		CustomModules:   resumeDetail.CustomModules,
		Slogan:          resumeDetail.Slogan,
		ResumeStyle:     resumeDetail.ResumeStyle,
	}

	s.logger.Info("成功获取简历详情", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
	return response, nil
}

// SaveResumeDetail 保存简历详情
func (s *resumeService) SaveResumeDetail(ctx context.Context, resumeIDStr string, userID uint, resumeData *dto.SaveResumeRequest, jwtToken string, fingerprint string) (*vo.SaveResumeResponse, error) {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试保存非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess
	}

	// 获取现有的简历详情记录
	existingDetail, err := s.resumeRepo.GetResumeDetailByResumeID(ctx, uint(resumeID))
	if err != nil && err != gorm.ErrRecordNotFound {
		s.logger.Error("获取现有简历详情失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed
	}

	// 构造要保存的简历详情
	var resumeDetail *models.ResumeDetail
	if existingDetail != nil {
		// 更新现有记录
		resumeDetail = existingDetail
	} else {
		// 创建新记录
		resumeDetail = &models.ResumeDetail{
			UserID:   userID,
			ResumeID: uint(resumeID),
		}
	}

	// 更新所有字段
	resumeDetail.BasicInfo = resumeData.BasicInfo
	resumeDetail.Education = resumeData.Education
	resumeDetail.Work = resumeData.Work
	resumeDetail.Project = resumeData.Project
	resumeDetail.Research = resumeData.Research
	resumeDetail.Team = resumeData.Team
	resumeDetail.Portfolio = resumeData.Portfolio
	resumeDetail.Other = resumeData.Other
	resumeDetail.PersonalSummary = resumeData.PersonalSummary
	resumeDetail.Honors = resumeData.Honors
	resumeDetail.Skills = resumeData.Skills
	resumeDetail.CustomModules = resumeData.CustomModules
	resumeDetail.Slogan = resumeData.Slogan
	resumeDetail.ResumeStyle = resumeData.ResumeStyle

	// 保存简历详情
	if err := s.resumeRepo.SaveResumeDetail(ctx, resumeDetail); err != nil {
		s.logger.Error("保存简历详情失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("保存简历详情失败")
	}

	// 更新简历主表的更新时间
	if err := s.resumeRepo.UpdateResumeUpdatedAt(ctx, uint(resumeID)); err != nil {
		s.logger.Error("更新简历时间失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	// 更新简历完成度
	if err := s.resumeRepo.UpdateResumeCompletionRate(ctx, uint(resumeID), resumeData.CompletionRate); err != nil {
		s.logger.Error("更新简历完成度失败", zap.Uint("resume_id", uint(resumeID)), zap.String("completion_rate", resumeData.CompletionRate), zap.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	// 构造响应
	response := &vo.SaveResumeResponse{
		UpdatedAt: time.Now(),
	}

	s.logger.Info("成功保存简历详情", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))

	// 异步生成简历预览图
	s.GenerateResumePreviewImage(uint(resumeID), userID, jwtToken, fingerprint)

	return response, nil
}

// UpdateResumeName 修改简历名称
func (s *resumeService) UpdateResumeName(ctx context.Context, resumeIDStr string, userID uint, resumeName string) (*vo.UpdateResumeNameResponse, error) {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试修改非自己的简历名称", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess
	}

	// 更新简历名称
	if err := s.resumeRepo.UpdateResumeName(ctx, uint(resumeID), resumeName); err != nil {
		s.logger.Error("更新简历名称失败", zap.Uint("resume_id", uint(resumeID)), zap.String("resume_name", resumeName), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("更新简历名称失败")
	}

	// 构造响应
	response := &vo.UpdateResumeNameResponse{
		ResumeName: resumeName,
		UpdatedAt:  time.Now(),
	}

	s.logger.Info("成功修改简历名称", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.String("new_name", resumeName))
	return response, nil
}

// RecordOnlineUser 记录在线用户并返回在线人数
func (s *resumeService) RecordOnlineUser(ctx context.Context, userID uint) (int64, error) {
	const onlineUsersKey = "resume:online_users"

	// 当前时间戳
	now := time.Now().Unix()

	// 使用sorted set保存用户ID和当前时间戳
	err := s.redisClient.ZAdd(ctx, onlineUsersKey, redis.Z{
		Score:  float64(now),
		Member: strconv.FormatUint(uint64(userID), 10),
	}).Err()

	if err != nil {
		s.logger.Error("记录在线用户失败", zap.Uint("user_id", userID), zap.Error(err))
		return 0, exception.ErrInternalServer.WithMessage("记录在线用户失败")
	}

	// 清理5分钟前的数据
	fiveMinutesAgo := now - 300 // 5分钟 = 300秒
	err = s.redisClient.ZRemRangeByScore(ctx, onlineUsersKey, "0", strconv.FormatInt(fiveMinutesAgo, 10)).Err()
	if err != nil {
		s.logger.Warn("清理过期在线用户数据失败", zap.Error(err))
		// 清理失败不影响主要功能，继续执行
	}

	// 统计5分钟内的在线用户数
	count, err := s.redisClient.ZCard(ctx, onlineUsersKey).Result()
	if err != nil {
		s.logger.Error("统计在线用户数失败", zap.Error(err))
		return 0, exception.ErrInternalServer.WithMessage("统计在线用户数失败")
	}

	// 在实际在线人数基础上加200
	finalCount := count + 200

	s.logger.Info("记录在线用户成功", zap.Uint("user_id", userID), zap.Int64("real_count", count), zap.Int64("final_count", finalCount))
	return finalCount, nil
}

// DownloadResumePDF 下载简历PDF
func (s *resumeService) DownloadResumePDF(ctx context.Context, resumeIDStr string, userID uint, jwtToken string, fingerprint string) ([]byte, string, error) {
	// 检查会员权限
	if err := s.checkMembershipPermission(ctx, userID, "下载简历PDF"); err != nil {
		return nil, "", err
	}

	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, "", exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, "", exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试下载非自己的简历PDF", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, "", exception.ErrResumeAccess
	}

	// 构建简历打印页面URL
	printURL := fmt.Sprintf("%s/make/print/%s/", s.frontendDomain, resumeIDStr)

	// 调用Chrome服务生成PDF
	pdfBytes, err := s.chromeService.GeneratePDF(printURL, jwtToken, fingerprint)
	if err != nil {
		s.logger.Error("生成PDF失败",
			zap.String("resume_id", resumeIDStr),
			zap.Uint("user_id", userID),
			zap.String("print_url", printURL),
			zap.Error(err))
		return nil, "", exception.ErrInternalServer.WithMessage("生成PDF失败")
	}

	// 生成PDF文件名
	timestamp := time.Now().Unix()
	fileName := fmt.Sprintf("resume_%s_%d.pdf", resumeIDStr, timestamp)

	s.logger.Info("简历PDF生成成功",
		zap.String("resume_id", resumeIDStr),
		zap.Uint("user_id", userID),
		zap.String("file_name", fileName),
		zap.Int("pdf_size", len(pdfBytes)))

	// PDF生成成功后，如果用户不是会员则消费下载券
	if err := s.consumeCouponIfNeeded(ctx, userID, "下载简历PDF"); err != nil {
		s.logger.Error("消费下载券失败",
			zap.String("resume_id", resumeIDStr),
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, "", err
	}

	return pdfBytes, fileName, nil
}

// GenerateResumePreviewImage 生成简历预览图（异步）
func (s *resumeService) GenerateResumePreviewImage(resumeID uint, userID uint, jwtToken string, fingerprint string) {
	// 在新的协程中执行，避免阻塞主流程
	go func() {
		// 创建新的上下文，避免使用可能已取消的上下文
		ctx := context.Background()

		s.logger.Info("开始生成简历预览图",
			zap.Uint("resume_id", resumeID),
			zap.Uint("user_id", userID))

		// 构建简历预览页面URL
		previewURL := fmt.Sprintf("%s/make/print/%d/", s.frontendDomain, resumeID)

		// 调用Chrome服务生成指定元素的图片（使用JPEG压缩）
		imageOptions := &pkg.ImageOptions{
			Format:  "jpeg",
			Quality: 40, // 高质量JPEG
			Scale:   1.0,
		}
		imageBytes, err := s.chromeService.GenerateElementImageWithOptions(previewURL, "print", jwtToken, fingerprint, imageOptions)
		if err != nil {
			s.logger.Error("生成简历预览图失败",
				zap.Uint("resume_id", resumeID),
				zap.Uint("user_id", userID),
				zap.String("preview_url", previewURL),
				zap.Error(err))
			return
		}

		// 生成图片文件名 - 同一个用户的同一个简历使用相同的文件名（JPEG格式）
		fileName := fmt.Sprintf("resume_preview_%d_%d.jpg", userID, resumeID)
		objectName := fmt.Sprintf("preview/%s", fileName)

		// 上传图片到OSS
		imageURL, err := s.ossService.UploadBytes(ctx, imageBytes, objectName, "image/jpeg")
		if err != nil {
			s.logger.Error("上传简历预览图到OSS失败",
				zap.Uint("resume_id", resumeID),
				zap.Uint("user_id", userID),
				zap.String("object_name", objectName),
				zap.Error(err))
			return
		}

		// 更新简历表的预览图URL
		if err := s.resumeRepo.UpdateResumePreviewImage(ctx, resumeID, imageURL); err != nil {
			s.logger.Error("更新简历预览图URL失败",
				zap.Uint("resume_id", resumeID),
				zap.Uint("user_id", userID),
				zap.String("image_url", imageURL),
				zap.Error(err))
			return
		}

		s.logger.Info("简历预览图生成成功",
			zap.Uint("resume_id", resumeID),
			zap.Uint("user_id", userID),
			zap.String("image_url", imageURL))
	}()
}

// ShareResumeByEmail 邮件分享简历
func (s *resumeService) ShareResumeByEmail(ctx context.Context, resumeIDStr string, userID uint, email string, fileName string, jwtToken string, fingerprint string) (*vo.ShareResumeByEmailResponse, error) {
	// 检查会员权限
	if err := s.checkMembershipPermission(ctx, userID, "邮件分享简历"); err != nil {
		return nil, err
	}

	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试分享非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess
	}

	// 如果用户不是会员，先消费下载券（在启动邮件发送任务前）
	if err := s.consumeCouponIfNeeded(ctx, userID, "邮件分享简历"); err != nil {
		s.logger.Error("消费下载券失败",
			zap.String("resume_id", resumeIDStr),
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, err
	}

	// 在协程中执行PDF生成和邮件发送任务
	go func() {
		s.logger.Info("开始生成简历PDF并发送邮件",
			zap.Uint("resume_id", uint(resumeID)),
			zap.Uint("user_id", userID),
			zap.String("email", email),
			zap.String("file_name", fileName))

		// 构建简历打印页面URL
		printURL := fmt.Sprintf("%s/make/print/%s/", s.frontendDomain, resumeIDStr)

		// 调用Chrome服务生成PDF
		pdfBytes, err := s.chromeService.GeneratePDF(printURL, jwtToken, fingerprint)
		if err != nil {
			s.logger.Error("生成PDF失败",
				zap.String("resume_id", resumeIDStr),
				zap.Uint("user_id", userID),
				zap.String("print_url", printURL),
				zap.String("email", email),
				zap.Error(err))
			return
		}

		// 调用邮件服务发送PDF附件
		if err := s.emailService.SendResumeAttachment(email, pdfBytes, fileName); err != nil {
			s.logger.Error("发送简历邮件失败",
				zap.String("resume_id", resumeIDStr),
				zap.Uint("user_id", userID),
				zap.String("email", email),
				zap.String("file_name", fileName),
				zap.Error(err))
			return
		}

		s.logger.Info("简历邮件分享成功",
			zap.String("resume_id", resumeIDStr),
			zap.Uint("user_id", userID),
			zap.String("email", email),
			zap.String("file_name", fileName),
			zap.Int("pdf_size", len(pdfBytes)))
	}()

	// 立即返回成功响应，不等待协程完成
	response := &vo.ShareResumeByEmailResponse{
		Message: "简历邮件正在发送中，请稍后查收",
	}

	s.logger.Info("简历邮件分享任务已启动",
		zap.String("resume_id", resumeIDStr),
		zap.Uint("user_id", userID),
		zap.String("email", email))

	return response, nil
}

// checkMembershipPermission 检查用户会员权限，如果不是会员则检查是否有可用下载券
func (s *resumeService) checkMembershipPermission(ctx context.Context, userID uint, action string) error {
	// 获取用户信息
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		s.logger.Error("获取用户信息失败", zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("获取用户信息失败")
	}

	// 检查用户是否为会员
	if user.UserType.IsMember() {
		// 会员用户直接通过
		return nil
	}

	// 非会员用户，检查是否有可用的下载券
	s.logger.Info("非会员用户尝试使用会员功能，检查下载券",
		zap.Uint("user_id", userID),
		zap.String("user_type", user.UserType.String()),
		zap.String("action", action))

	// 检查是否有可用的下载券
	hasAvailableCoupons, err := s.userDownloadCouponService.HasAvailableCoupons(ctx, userID)
	if err != nil {
		s.logger.Error("检查用户下载券失败", zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("检查下载券失败")
	}

	if !hasAvailableCoupons {
		s.logger.Warn("用户没有可用的下载券",
			zap.Uint("user_id", userID),
			zap.String("action", action))

		// 根据不同的操作返回不同的错误
		switch action {
		case "下载简历PDF":
			return exception.ErrResumeDownloadMemberRequired
		case "邮件分享简历":
			return exception.ErrResumeShareMemberRequired
		default:
			return exception.ErrResumeMembershipRequired
		}
	}

	s.logger.Info("用户有可用的下载券，权限检查通过",
		zap.Uint("user_id", userID),
		zap.String("action", action))

	return nil
}

// consumeCouponIfNeeded 如果用户不是会员，则消费一张下载券
func (s *resumeService) consumeCouponIfNeeded(ctx context.Context, userID uint, action string) error {
	// 获取用户信息
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		s.logger.Error("获取用户信息失败", zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("获取用户信息失败")
	}

	// 如果是会员用户，不需要消费下载券
	if user.UserType.IsMember() {
		return nil
	}

	// 非会员用户，消费一张下载券
	err = s.userDownloadCouponService.ConsumeOneCoupon(ctx, userID)
	if err != nil {
		s.logger.Error("消费下载券失败", zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("消费下载券失败")
	}

	s.logger.Info("成功消费下载券",
		zap.Uint("user_id", userID),
		zap.String("action", action))

	return nil
}

// GetResumeBasicInfo 获取简历基本信息
func (s *resumeService) GetResumeBasicInfo(ctx context.Context, resumeIDStr string, userID uint) (*vo.GetResumeBasicInfoResponse, error) {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试访问非自己的简历基本信息", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess
	}

	// 获取简历基本信息
	resume, err := s.resumeRepo.GetResumeByID(ctx, uint(resumeID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return nil, exception.ErrResumeNotFound
		}
		s.logger.Error("获取简历基本信息失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrResumeQueryFailed
	}

	// 构造响应
	response := &vo.GetResumeBasicInfoResponse{
		ID:              resume.ID,
		ResumeName:      resume.ResumeName,
		PreviewImageUrl: resume.PreviewImageUrl,
		CompletionRate:  resume.CompletionRate,
		CreatedAt:       resume.CreatedAt,
	}

	s.logger.Info("成功获取简历基本信息", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
	return response, nil
}

// GetAllMyResumes 获取所有自己的简历
func (s *resumeService) GetAllMyResumes(ctx context.Context, userID uint) (*vo.GetAllMyResumesResponse, error) {
	// 获取用户的所有简历
	resumes, err := s.resumeRepo.GetResumesByUserID(ctx, userID)
	if err != nil {
		s.logger.Error("获取用户简历列表失败", zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeQueryFailed
	}

	// 构造响应数据
	resumeItems := make([]vo.ResumeBasicItem, 0, len(resumes))
	for _, resume := range resumes {
		resumeItems = append(resumeItems, vo.ResumeBasicItem{
			ID:              resume.ID,
			TemplateID:      resume.TemplateID,
			ResumeName:      resume.ResumeName,
			PreviewImageUrl: resume.PreviewImageUrl,
			CompletionRate:  resume.CompletionRate,
			CreatedAt:       resume.CreatedAt,
			UpdatedAt:       resume.UpdatedAt,
		})
	}

	response := &vo.GetAllMyResumesResponse{
		Resumes: resumeItems,
	}

	s.logger.Info("成功获取用户简历列表", zap.Uint("user_id", userID), zap.Int("count", len(resumes)))
	return response, nil
}

// GetDeletedResumes 获取回收站中的简历
func (s *resumeService) GetDeletedResumes(ctx context.Context, userID uint) (*vo.GetDeletedResumesResponse, error) {
	// 调用仓库层获取已删除的简历
	resumes, err := s.resumeRepo.GetDeletedResumesByUserID(ctx, userID)
	if err != nil {
		s.logger.Error("获取用户回收站简历失败", zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrResumeQueryFailed.WithMessage("获取回收站简历失败")
	}

	// 转换为响应VO
	var resumeItems []vo.DeletedResumeItem
	for _, resume := range resumes {
		resumeItems = append(resumeItems, vo.DeletedResumeItem{
			ID:              resume.ID,
			TemplateID:      resume.TemplateID,
			ResumeName:      resume.ResumeName,
			PreviewImageUrl: resume.PreviewImageUrl,
			CompletionRate:  resume.CompletionRate,
			CreatedAt:       resume.CreatedAt,
			UpdatedAt:       resume.UpdatedAt,
			DeletedAt:       resume.DeletedAt.Time,
		})
	}

	response := &vo.GetDeletedResumesResponse{
		Resumes: resumeItems,
	}

	s.logger.Info("成功获取用户回收站简历列表", zap.Uint("user_id", userID), zap.Int("count", len(resumes)))
	return response, nil
}

// DeleteResume 删除简历
func (s *resumeService) DeleteResume(ctx context.Context, resumeIDStr string, userID uint) error {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试删除非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return exception.ErrResumeAccess
	}

	// 检查简历是否存在
	_, err = s.resumeRepo.GetResumeByID(ctx, uint(resumeID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return exception.ErrResumeNotFound
		}
		s.logger.Error("获取简历信息失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return exception.ErrResumeQueryFailed
	}

	// 删除简历
	err = s.resumeRepo.DeleteResumeByID(ctx, uint(resumeID))
	if err != nil {
		s.logger.Error("删除简历失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeDeleteFailed
	}

	s.logger.Info("简历删除成功", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
	return nil
}

// PermanentlyDeleteResume 物理删除简历
func (s *resumeService) PermanentlyDeleteResume(ctx context.Context, resumeIDStr string, userID uint) error {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查已删除简历的所有权
	resume, err := s.resumeRepo.CheckDeletedResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return exception.ErrResumeNotFound
		}
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeOwnershipFailed
	}

	// 检查简历是否在回收站中（deleted_at不为空）
	if !resume.DeletedAt.Valid {
		s.logger.Warn("尝试物理删除未在回收站的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return exception.ErrResumeAccess.WithMessage("只能彻底删除回收站中的简历")
	}

	// 物理删除简历
	err = s.resumeRepo.PermanentlyDeleteResumeByID(ctx, uint(resumeID))
	if err != nil {
		s.logger.Error("物理删除简历失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("彻底删除简历失败")
	}

	s.logger.Info("简历物理删除成功", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
	return nil
}

// RestoreResume 恢复简历
func (s *resumeService) RestoreResume(ctx context.Context, resumeIDStr string, userID uint) error {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查已删除简历的所有权
	resume, err := s.resumeRepo.CheckDeletedResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return exception.ErrResumeNotFound
		}
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeOwnershipFailed
	}

	// 检查简历是否在回收站中（deleted_at不为空）
	if !resume.DeletedAt.Valid {
		s.logger.Warn("尝试恢复未在回收站的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return exception.ErrResumeAccess.WithMessage("只能恢复回收站中的简历")
	}

	// 恢复简历
	err = s.resumeRepo.RestoreResumeByID(ctx, uint(resumeID))
	if err != nil {
		s.logger.Error("恢复简历失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("恢复简历失败")
	}

	s.logger.Info("简历恢复成功", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
	return nil
}

// CopyResume 复制简历
func (s *resumeService) CopyResume(ctx context.Context, resumeIDStr string, userID uint) error {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, uint(resumeID), userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试复制非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return exception.ErrResumeAccess
	}

	// 获取原简历及其详情
	originalResume, originalDetail, _, err := s.resumeRepo.GetResumeWithDetailByID(ctx, uint(resumeID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return exception.ErrResumeNotFound
		}
		s.logger.Error("获取原简历信息失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return exception.ErrResumeQueryFailed
	}

	// 创建新简历（复制原简历信息）
	newResume := &models.Resume{
		UserID:          userID,
		TemplateID:      originalResume.TemplateID,
		ResumeName:      originalResume.ResumeName + "-复制",
		PreviewImageUrl: "", // 预览图需要重新生成
		CompletionRate:  originalResume.CompletionRate,
	}

	// 创建新简历记录
	if err := s.resumeRepo.CreateResume(ctx, newResume); err != nil {
		s.logger.Error("创建新简历失败", zap.Uint("original_resume_id", uint(resumeID)), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("复制简历失败")
	}

	// 创建新简历详情（复制原简历详情）
	newResumeDetail := &models.ResumeDetail{
		UserID:          userID,
		ResumeID:        newResume.ID,
		BasicInfo:       originalDetail.BasicInfo,
		Education:       originalDetail.Education,
		Work:            originalDetail.Work,
		Project:         originalDetail.Project,
		Research:        originalDetail.Research,
		Team:            originalDetail.Team,
		Portfolio:       originalDetail.Portfolio,
		Other:           originalDetail.Other,
		PersonalSummary: originalDetail.PersonalSummary,
		Honors:          originalDetail.Honors,
		Skills:          originalDetail.Skills,
		CustomModules:   originalDetail.CustomModules,
		Slogan:          originalDetail.Slogan,
		ResumeStyle:     originalDetail.ResumeStyle,
	}

	// 创建新简历详情记录
	if err := s.resumeRepo.CreateResumeDetail(ctx, newResumeDetail); err != nil {
		s.logger.Error("创建新简历详情失败", zap.Uint("new_resume_id", newResume.ID), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("复制简历详情失败")
	}

	s.logger.Info("简历复制成功",
		zap.Uint("original_resume_id", uint(resumeID)),
		zap.Uint("new_resume_id", newResume.ID),
		zap.Uint("user_id", userID),
		zap.String("new_resume_name", newResume.ResumeName))
	return nil
}

// CreateResume 创建新简历
func (s *resumeService) CreateResume(ctx context.Context, resume *models.Resume) error {
	// 调用仓储层创建简历
	if err := s.resumeRepo.CreateResume(ctx, resume); err != nil {
		s.logger.Error("创建简历失败",
			zap.Uint("user_id", resume.UserID),
			zap.Uint("template_id", resume.TemplateID),
			zap.String("resume_name", resume.ResumeName),
			zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("创建简历失败")
	}

	s.logger.Info("创建简历成功",
		zap.Uint("resume_id", resume.ID),
		zap.Uint("user_id", resume.UserID),
		zap.String("resume_name", resume.ResumeName))

	return nil
}

// CreateResumeDetail 创建新简历详情
func (s *resumeService) CreateResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error {
	// 调用仓储层创建简历详情
	if err := s.resumeRepo.CreateResumeDetail(ctx, resumeDetail); err != nil {
		s.logger.Error("创建简历详情失败",
			zap.Uint("user_id", resumeDetail.UserID),
			zap.Uint("resume_id", resumeDetail.ResumeID),
			zap.Error(err))
		return exception.ErrResumeDeleteFailed.WithMessage("创建简历详情失败")
	}

	s.logger.Info("创建简历详情成功",
		zap.Uint("resume_detail_id", resumeDetail.ID),
		zap.Uint("user_id", resumeDetail.UserID),
		zap.Uint("resume_id", resumeDetail.ResumeID))

	return nil
}

// GetResumeDraftDetailByID 根据简历草稿ID获取简历草稿详情
func (s *resumeService) GetResumeDraftDetailByID(ctx context.Context, draftIDStr string, userID uint) (*vo.ResumeDetailResponse, error) {
	// 转换草稿ID
	draftID, err := strconv.ParseUint(draftIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历草稿ID失败", zap.String("draft_id", draftIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历草稿ID格式错误")
	}

	// 获取简历草稿
	resumeDraft, err := s.resumeDraftRepo.GetByID(ctx, uint(draftID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历草稿不存在", zap.Uint("draft_id", uint(draftID)))
			return nil, exception.ErrResumeNotFound.WithMessage("简历草稿不存在")
		}
		s.logger.Error("获取简历草稿失败", zap.Uint("draft_id", uint(draftID)), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("获取简历草稿失败")
	}

	// 检查草稿所有权
	if resumeDraft.UserID != userID {
		s.logger.Warn("用户尝试访问非自己的简历草稿", zap.Uint("draft_id", uint(draftID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess.WithMessage("无权访问该简历草稿")
	}

	// 获取模板信息
	template, err := s.templateRepo.GetByID(ctx, resumeDraft.TemplateID)
	if err != nil {
		s.logger.Error("获取模板信息失败", zap.Uint("template_id", resumeDraft.TemplateID), zap.Error(err))
		// 使用默认模板信息
		template = &models.Template{
			ID:            resumeDraft.TemplateID,
			ComponentName: "DefaultTemplate",
		}
	}

	// 构造响应
	response := &vo.ResumeDetailResponse{
		ID:              resumeDraft.ID,
		UserID:          resumeDraft.UserID,
		TemplateID:      resumeDraft.TemplateID,
		ComponentName:   template.ComponentName,
		ResumeName:      "简历草稿", // 草稿默认名称
		PreviewImageUrl: "",     // 草稿没有预览图
		CompletionRate:  "100%", // 草稿默认完成度
		CreatedAt:       resumeDraft.CreatedAt,
		UpdatedAt:       resumeDraft.UpdatedAt,
		Diff:            resumeDraft.Diff, // 简历草稿的对比文案

		// 简历详情内容
		BasicInfo:       resumeDraft.BasicInfo,
		Education:       resumeDraft.Education,
		Work:            resumeDraft.Work,
		Project:         resumeDraft.Project,
		Research:        resumeDraft.Research,
		Team:            resumeDraft.Team,
		Portfolio:       resumeDraft.Portfolio,
		Other:           resumeDraft.Other,
		PersonalSummary: resumeDraft.PersonalSummary,
		Honors:          resumeDraft.Honors,
		Skills:          resumeDraft.Skills,
		CustomModules:   resumeDraft.CustomModules,
		Slogan:          resumeDraft.Slogan,
		ResumeStyle:     resumeDraft.ResumeStyle,
	}

	s.logger.Info("获取简历草稿详情成功",
		zap.Uint("draft_id", uint(draftID)),
		zap.Uint("user_id", userID))

	return response, nil
}

// ApplyDraft 应用草稿到简历
func (s *resumeService) ApplyDraft(ctx context.Context, resumeIDStr string, draftID uint, userID uint) (*vo.ApplyDraftResponse, error) {
	// 转换简历ID
	resumeID, err := strconv.ParseUint(resumeIDStr, 10, 32)
	if err != nil {
		s.logger.Error("解析简历ID失败", zap.String("resume_id", resumeIDStr), zap.Error(err))
		return nil, exception.ErrResumeIDInvalid.WithMessage("简历ID格式错误")
	}

	// 获取简历信息
	resume, err := s.resumeRepo.GetResumeByID(ctx, uint(resumeID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历不存在", zap.Uint("resume_id", uint(resumeID)))
			return nil, exception.ErrResumeNotFound.WithMessage("简历不存在")
		}
		s.logger.Error("获取简历失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("获取简历失败")
	}

	// 检查简历所有权
	if resume.UserID != userID {
		s.logger.Warn("用户尝试访问非自己的简历", zap.Uint("resume_id", uint(resumeID)), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess.WithMessage("无权访问该简历")
	}

	// 获取简历草稿
	resumeDraft, err := s.resumeDraftRepo.GetByID(ctx, draftID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历草稿不存在", zap.Uint("draft_id", draftID))
			return nil, exception.ErrResumeNotFound.WithMessage("简历草稿不存在")
		}
		s.logger.Error("获取简历草稿失败", zap.Uint("draft_id", draftID), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("获取简历草稿失败")
	}

	// 检查草稿所有权
	if resumeDraft.UserID != userID {
		s.logger.Warn("用户尝试访问非自己的简历草稿", zap.Uint("draft_id", draftID), zap.Uint("user_id", userID))
		return nil, exception.ErrResumeAccess.WithMessage("无权访问该简历草稿")
	}

	// 获取简历详情
	resumeDetail, err := s.resumeRepo.GetResumeDetailByResumeID(ctx, uint(resumeID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("简历详情不存在", zap.Uint("resume_id", uint(resumeID)))
			return nil, exception.ErrResumeNotFound.WithMessage("简历详情不存在")
		}
		s.logger.Error("获取简历详情失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrResumeDetailQueryFailed.WithMessage("获取简历详情失败")
	}

	// 用草稿数据覆盖简历数据
	resumeDetail.BasicInfo = resumeDraft.BasicInfo
	resumeDetail.Education = resumeDraft.Education
	resumeDetail.Work = resumeDraft.Work
	resumeDetail.Project = resumeDraft.Project
	resumeDetail.Research = resumeDraft.Research
	resumeDetail.Team = resumeDraft.Team
	resumeDetail.Portfolio = resumeDraft.Portfolio
	resumeDetail.Other = resumeDraft.Other
	resumeDetail.PersonalSummary = resumeDraft.PersonalSummary
	resumeDetail.Honors = resumeDraft.Honors
	resumeDetail.Skills = resumeDraft.Skills
	resumeDetail.CustomModules = resumeDraft.CustomModules
	resumeDetail.Slogan = resumeDraft.Slogan
	resumeDetail.ResumeStyle = resumeDraft.ResumeStyle

	// 更新简历详情
	err = s.resumeRepo.SaveResumeDetail(ctx, resumeDetail)
	if err != nil {
		s.logger.Error("更新简历详情失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("应用草稿失败")
	}

	// 更新简历的更新时间
	err = s.resumeRepo.UpdateResumeUpdatedAt(ctx, uint(resumeID))
	if err != nil {
		s.logger.Error("更新简历时间失败", zap.Uint("resume_id", uint(resumeID)), zap.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	s.logger.Info("应用草稿到简历成功",
		zap.Uint("resume_id", uint(resumeID)),
		zap.Uint("draft_id", draftID),
		zap.Uint("user_id", userID))

	return &vo.ApplyDraftResponse{
		ResumeID: uint(resumeID),
	}, nil
}
