package services

import (
	"context"

	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/vo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TargetPositionService 目标岗位服务接口
type TargetPositionService interface {
	CreateTargetPosition(ctx context.Context, userID uint, req *dto.CreateTargetPositionRequest) (*models.TargetPosition, error)
	UpdateTargetPosition(ctx context.Context, userID, positionID uint, req *dto.UpdateTargetPositionRequest) error
	DeleteTargetPosition(ctx context.Context, userID, positionID uint) error
	GetTargetPositionById(ctx context.Context, userID, positionID uint) (*vo.TargetPositionResponse, error)
	GetAllTargetPositions(ctx context.Context, userID uint) ([]vo.TargetPositionResponse, int64, error)
}

// targetPositionService 目标岗位服务实现
type targetPositionService struct {
	targetPositionRepo repository.TargetPositionRepository
	logger             *zap.Logger
}

// NewTargetPositionService 创建目标岗位服务
func NewTargetPositionService(
	targetPositionRepo repository.TargetPositionRepository,
	logger *zap.Logger,
) TargetPositionService {
	return &targetPositionService{
		targetPositionRepo: targetPositionRepo,
		logger:             logger,
	}
}

// CreateTargetPosition 创建目标岗位
func (s *targetPositionService) CreateTargetPosition(ctx context.Context, userID uint, req *dto.CreateTargetPositionRequest) (*models.TargetPosition, error) {
	// 创建目标岗位模型
	targetPosition := &models.TargetPosition{
		UserID:         userID,
		PositionName:   req.PositionName,
		CompanyName:    req.CompanyName,
		JobSource:      req.JobSource,
		JobDescription: req.JobDescription,
	}

	// 保存到数据库
	err := s.targetPositionRepo.Create(ctx, targetPosition)
	if err != nil {
		s.logger.Error("创建目标岗位失败",
			zap.Uint("user_id", userID),
			zap.String("position_name", req.PositionName),
			zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	s.logger.Info("创建目标岗位成功",
		zap.Uint("user_id", userID),
		zap.Uint("position_id", targetPosition.ID),
		zap.String("position_name", req.PositionName))

	return targetPosition, nil
}

// UpdateTargetPosition 更新目标岗位
func (s *targetPositionService) UpdateTargetPosition(ctx context.Context, userID, positionID uint, req *dto.UpdateTargetPositionRequest) error {
	// 检查目标岗位是否存在且属于当前用户
	targetPosition, err := s.targetPositionRepo.GetByUserIDAndID(ctx, userID, positionID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return exception.ErrTargetPositionNotFound
		}
		s.logger.Error("查询目标岗位失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return exception.ErrInternalServer
	}

	// 更新字段
	targetPosition.PositionName = req.PositionName
	targetPosition.CompanyName = req.CompanyName
	targetPosition.JobSource = req.JobSource
	targetPosition.JobDescription = req.JobDescription

	// 保存更新
	err = s.targetPositionRepo.Update(ctx, targetPosition)
	if err != nil {
		s.logger.Error("更新目标岗位失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return exception.ErrInternalServer
	}

	s.logger.Info("更新目标岗位成功",
		zap.Uint("user_id", userID),
		zap.Uint("position_id", positionID))

	return nil
}

// DeleteTargetPosition 删除目标岗位
func (s *targetPositionService) DeleteTargetPosition(ctx context.Context, userID, positionID uint) error {
	// 检查目标岗位是否存在且属于当前用户
	_, err := s.targetPositionRepo.GetByUserIDAndID(ctx, userID, positionID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return exception.ErrTargetPositionNotFound
		}
		s.logger.Error("查询目标岗位失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return exception.ErrInternalServer
	}

	// 删除目标岗位
	err = s.targetPositionRepo.DeleteByUserIDAndID(ctx, userID, positionID)
	if err != nil {
		s.logger.Error("删除目标岗位失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return exception.ErrInternalServer
	}

	s.logger.Info("删除目标岗位成功",
		zap.Uint("user_id", userID),
		zap.Uint("position_id", positionID))

	return nil
}

// GetTargetPositionById 根据ID获取目标岗位详情
func (s *targetPositionService) GetTargetPositionById(ctx context.Context, userID, positionID uint) (*vo.TargetPositionResponse, error) {
	// 查询目标岗位
	targetPosition, err := s.targetPositionRepo.GetByUserIDAndID(ctx, userID, positionID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exception.ErrTargetPositionNotFound
		}
		s.logger.Error("查询目标岗位失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应VO
	response := convertToTargetPositionResponse(targetPosition)
	return response, nil
}

// GetAllTargetPositions 获取用户所有目标岗位
func (s *targetPositionService) GetAllTargetPositions(ctx context.Context, userID uint) ([]vo.TargetPositionResponse, int64, error) {
	// 查询用户所有目标岗位
	targetPositions, total, err := s.targetPositionRepo.GetAllByUserID(ctx, userID)
	if err != nil {
		s.logger.Error("查询用户目标岗位列表失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, 0, exception.ErrInternalServer
	}

	// 转换为响应VO列表
	responses := make([]vo.TargetPositionResponse, len(targetPositions))
	for i, position := range targetPositions {
		responses[i] = *convertToTargetPositionResponse(position)
	}

	return responses, total, nil
}

// convertToTargetPositionResponse 将模型转换为响应VO
func convertToTargetPositionResponse(targetPosition *models.TargetPosition) *vo.TargetPositionResponse {
	return &vo.TargetPositionResponse{
		ID:             targetPosition.ID,
		UserID:         targetPosition.UserID,
		PositionName:   targetPosition.PositionName,
		CompanyName:    targetPosition.CompanyName,
		JobSource:      targetPosition.JobSource,
		JobDescription: targetPosition.JobDescription,
		CreatedAt:      targetPosition.CreatedAt,
		UpdatedAt:      targetPosition.UpdatedAt,
	}
}
