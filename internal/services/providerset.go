package services

import (
	"github.com/google/wire"
)

// CodeServiceSet 验证码服务依赖注入集合
var CodeServiceSet = wire.NewSet(
	NewCodeService,
	wire.Bind(new(SMSCodeService), new(CodeService)),
)

// QrCodeServiceSet 二维码服务依赖注入集合
var QrCodeServiceSet = wire.NewSet(
	NewQrCodeService,
)

// EmailCodeServiceSet 邮件验证码服务依赖注入集合
var EmailCodeServiceSet = wire.NewSet(
	NewEmailCodeService,
)

// MembershipPlanServiceSet 会员套餐服务依赖注入集合
var MembershipPlanServiceSet = wire.NewSet(
	NewMembershipPlanService,
)

// OrderServiceSet 订单服务依赖注入集合
var OrderServiceSet = wire.NewSet(
	NewOrderService,
)

// CategoryServiceSet 分类服务依赖注入集合
var CategoryServiceSet = wire.NewSet(
	NewCategoryService,
)

// PositionServiceSet 职位服务依赖注入集合
var PositionServiceSet = wire.NewSet(
	NewPositionService,
)

// ResumeServiceSet 简历服务依赖注入集合
var ResumeServiceSet = wire.NewSet(
	NewResumeService,
)

// AIServiceSet AI服务依赖注入集合
var AIServiceSet = wire.NewSet(
	NewAIService,
)

// ExampleServiceSet 示例服务依赖注入集合
var ExampleServiceSet = wire.NewSet(
	NewExampleService,
)

// TemplateServiceSet 模板服务依赖注入集合
var TemplateServiceSet = wire.NewSet(
	NewTemplateService,
)

// MembershipValidationServiceSet 会员校验服务依赖注入集合
var MembershipValidationServiceSet = wire.NewSet(
	NewMembershipValidationService,
)

// TargetPositionServiceSet 目标岗位服务依赖注入集合
var TargetPositionServiceSet = wire.NewSet(
	NewTargetPositionService,
)

// ResumeScoreServiceSet 简历评分服务依赖注入集合
var ResumeScoreServiceSet = wire.NewSet(
	NewResumeScoreService,
)

// UserDownloadCouponServiceSet 用户下载券服务依赖注入集合
var UserDownloadCouponServiceSet = wire.NewSet(
	NewUserDownloadCouponService,
)

// ServiceSet 服务层依赖注入集合
var ServiceSet = wire.NewSet(
	NewUserService,
	CodeServiceSet,
	QrCodeServiceSet,
	NewUploadService,
	EmailCodeServiceSet,
	MembershipPlanServiceSet,
	OrderServiceSet,
	PositionServiceSet,
	CategoryServiceSet,
	ResumeServiceSet,
	AIServiceSet,
	ExampleServiceSet,
	TemplateServiceSet,
	MembershipValidationServiceSet,
	TargetPositionServiceSet,
	ResumeScoreServiceSet,
	UserDownloadCouponServiceSet,
	// 未来可以在这里添加其他服务的构造函数
)
