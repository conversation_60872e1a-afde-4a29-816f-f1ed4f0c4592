package services

import (
	"context"

	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/vo"
	"go.uber.org/zap"
)

// UserDownloadCouponService 用户下载券服务接口
type UserDownloadCouponService interface {
	// GetAvailableCouponsCount 获取用户可用的下载券数量
	GetAvailableCouponsCount(ctx context.Context, userID uint) (*vo.AvailableCouponsCountResponse, error)
	// ConsumeOneCoupon 消费一张下载券
	ConsumeOneCoupon(ctx context.Context, userID uint) error
	// HasAvailableCoupons 检查用户是否有可用的下载券
	HasAvailableCoupons(ctx context.Context, userID uint) (bool, error)
	// CreateCouponsFromOrder 根据订单创建下载券
	CreateCouponsFromOrder(ctx context.Context, userID uint, orderID uint, couponCount int) error
}

// userDownloadCouponService 用户下载券服务实现
type userDownloadCouponService struct {
	couponRepo repository.UserDownloadCouponRepository
	logger     *zap.Logger
}

// NewUserDownloadCouponService 创建用户下载券服务
func NewUserDownloadCouponService(
	couponRepo repository.UserDownloadCouponRepository,
	logger *zap.Logger,
) UserDownloadCouponService {
	return &userDownloadCouponService{
		couponRepo: couponRepo,
		logger:     logger,
	}
}

// GetAvailableCouponsCount 获取用户可用的下载券数量
func (s *userDownloadCouponService) GetAvailableCouponsCount(ctx context.Context, userID uint) (*vo.AvailableCouponsCountResponse, error) {
	// 从仓库获取用户可用的下载券数量
	count, err := s.couponRepo.GetAvailableCouponsCount(ctx, userID)
	if err != nil {
		s.logger.Error("获取用户可用下载券数量失败", zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("获取下载券数量失败")
	}

	// 构建响应
	response := &vo.AvailableCouponsCountResponse{
		Count: int(count),
	}

	s.logger.Info("获取用户可用下载券数量成功", zap.Uint("user_id", userID), zap.Int64("count", count))
	return response, nil
}

// ConsumeOneCoupon 消费一张下载券
func (s *userDownloadCouponService) ConsumeOneCoupon(ctx context.Context, userID uint) error {
	err := s.couponRepo.ConsumeOneCoupon(ctx, userID)
	if err != nil {
		s.logger.Error("消费下载券失败", zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("消费下载券失败")
	}

	s.logger.Info("消费下载券成功", zap.Uint("user_id", userID))
	return nil
}

// HasAvailableCoupons 检查用户是否有可用的下载券
func (s *userDownloadCouponService) HasAvailableCoupons(ctx context.Context, userID uint) (bool, error) {
	count, err := s.couponRepo.GetAvailableCouponsCount(ctx, userID)
	if err != nil {
		s.logger.Error("检查用户可用下载券失败", zap.Uint("user_id", userID), zap.Error(err))
		return false, exception.ErrInternalServer.WithMessage("检查下载券失败")
	}

	return count > 0, nil
}

// CreateCouponsFromOrder 根据订单创建下载券
func (s *userDownloadCouponService) CreateCouponsFromOrder(ctx context.Context, userID uint, orderID uint, couponCount int) error {
	if couponCount <= 0 {
		return nil
	}

	// 创建下载券记录
	coupons := make([]*models.UserDownloadCoupon, couponCount)
	for i := 0; i < couponCount; i++ {
		coupons[i] = &models.UserDownloadCoupon{
			UserID:       userID,
			IsUsed:       false,
			UsedTime:     nil,
			CouponSource: enum.CouponSourceOrder, // 订单充值来源
			OrderID:      orderID,
		}
	}

	// 批量创建下载券
	err := s.couponRepo.CreateCoupons(ctx, coupons)
	if err != nil {
		s.logger.Error("批量创建下载券失败",
			zap.Uint("user_id", userID),
			zap.Uint("order_id", orderID),
			zap.Int("coupon_count", couponCount),
			zap.Error(err))
		return exception.ErrInternalServer.WithMessage("创建下载券失败")
	}

	s.logger.Info("成功创建下载券",
		zap.Uint("user_id", userID),
		zap.Uint("order_id", orderID),
		zap.Int("coupon_count", couponCount))

	return nil
}
