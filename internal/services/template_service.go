package services

import (
	"context"

	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/vo"
	"go.uber.org/zap"
)

// TemplateService 模板服务接口
type TemplateService interface {
	// GetTemplateList 获取模板列表（支持分页）
	GetTemplateList(ctx context.Context, page, pageSize int) (*vo.PaginatedList[vo.TemplateListItemResponse], error)

	// UseTemplate 使用模板
	UseTemplate(ctx context.Context, templateID, resumeID, userID uint) error
}

// templateService 模板服务实现
type templateService struct {
	templateRepo repository.TemplateRepository
	resumeRepo   repository.ResumeRepository
	logger       *zap.Logger
}

// NewTemplateService 创建模板服务
func NewTemplateService(
	templateRepo repository.TemplateRepository,
	resumeRepo repository.ResumeRepository,
	logger *zap.Logger,
) TemplateService {
	return &templateService{
		templateRepo: templateRepo,
		resumeRepo:   resumeRepo,
		logger:       logger,
	}
}

// GetTemplateList 获取模板列表（支持分页）
func (s *templateService) GetTemplateList(ctx context.Context, page, pageSize int) (*vo.PaginatedList[vo.TemplateListItemResponse], error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用仓储层获取分页数据
	templates, total, err := s.templateRepo.GetTemplatesPaginated(ctx, page, pageSize)
	if err != nil {
		s.logger.Error("获取模板列表失败", zap.Error(err))
		return nil, exception.ErrTemplateListQueryFailed
	}

	// 转换为响应格式
	var result []vo.TemplateListItemResponse
	for _, template := range templates {
		item := vo.TemplateListItemResponse{
			ID:              template.ID,
			TemplateName:    template.TemplateName,
			Tags:            template.Tags,
			PreviewImageUrl: template.PreviewImageUrl,
			UsageCount:      template.UsageCount,
		}
		result = append(result, item)
	}

	// 构建分页响应
	response := vo.NewPaginatedList(result, total, page, pageSize)
	return &response, nil
}

// UseTemplate 使用模板
func (s *templateService) UseTemplate(ctx context.Context, templateID, resumeID, userID uint) error {
	// 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, resumeID, userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败", zap.Uint("resume_id", resumeID), zap.Uint("user_id", userID), zap.Error(err))
		return exception.ErrResumeOwnershipFailed
	}

	if !isOwner {
		s.logger.Warn("用户尝试修改非自己的简历模板", zap.Uint("resume_id", resumeID), zap.Uint("user_id", userID))
		return exception.ErrTemplateResumeOwnershipFail
	}

	// 检查模板是否存在并获取模板信息
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		s.logger.Error("获取模板信息失败", zap.Uint("template_id", templateID), zap.Error(err))
		return exception.ErrTemplateNotFound
	}

	// 更新简历的模板ID
	if err := s.resumeRepo.UpdateResumeTemplateID(ctx, resumeID, templateID); err != nil {
		s.logger.Error("更新简历模板ID失败", zap.Uint("resume_id", resumeID), zap.Uint("template_id", templateID), zap.Error(err))
		return exception.ErrTemplateUpdateResumeIDFail
	}

	// 更新简历详情的样式
	if err := s.resumeRepo.UpdateResumeDetailStyle(ctx, resumeID, template.ResumeStyle); err != nil {
		s.logger.Error("更新简历样式失败", zap.Uint("resume_id", resumeID), zap.Uint("template_id", templateID), zap.Error(err))
		return exception.ErrTemplateUpdateStyleFail
	}

	s.logger.Info("成功应用模板", zap.Uint("resume_id", resumeID), zap.Uint("template_id", templateID), zap.Uint("user_id", userID))

	return nil
}
