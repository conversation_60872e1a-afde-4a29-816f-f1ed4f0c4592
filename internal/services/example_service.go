package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ExampleService 示例服务接口
type ExampleService interface {
	// GetExampleById 根据ID获取示例详情
	GetExampleById(ctx context.Context, id uint) (*vo.ExampleDetailResponse, error)

	// GetExampleTdkById 根据ID获取示例TDK信息
	GetExampleTdkById(ctx context.Context, id uint) (*vo.ExampleTdkResponse, error)

	// UseExample 使用例子创建简历
	UseExample(ctx *gin.Context, exampleID uint, userID uint, jwtToken string, fingerprint string) (*vo.UseExampleResponse, error)

	// GetHomePageExamples 获取首页示例列表
	GetHomePageExamples(ctx context.Context) ([]vo.ExampleListItemResponse, error)

	// GetTemplateRecommendationsWithCache 获取模板推荐列表（带永久缓存）
	GetTemplateRecommendationsWithCache(ctx context.Context) ([]vo.ExampleListItemResponse, error)

	// GetTemplateRecommendations 获取模板推荐列表（不带缓存）
	GetTemplateRecommendations(ctx context.Context) ([]vo.ExampleListItemResponse, error)
}

// exampleService 示例服务实现
type exampleService struct {
	exampleRepo                 repository.ExampleRepository
	templateRepo                repository.TemplateRepository
	positionRepo                repository.PositionRepository
	resumeService               ResumeService
	membershipValidationService MembershipValidationService
	redisClient                 *redis.Client
	logger                      *zap.Logger
	db                          *gorm.DB
}

// NewExampleService 创建示例服务
func NewExampleService(
	exampleRepo repository.ExampleRepository,
	templateRepo repository.TemplateRepository,
	positionRepo repository.PositionRepository,
	resumeService ResumeService,
	membershipValidationService MembershipValidationService,
	redisClient *redis.Client,
	logger *zap.Logger,
	db *gorm.DB,
) ExampleService {
	return &exampleService{
		exampleRepo:                 exampleRepo,
		templateRepo:                templateRepo,
		positionRepo:                positionRepo,
		resumeService:               resumeService,
		membershipValidationService: membershipValidationService,
		redisClient:                 redisClient,
		logger:                      logger,
		db:                          db,
	}
}

// GetExampleById 根据ID获取示例详情
func (s *exampleService) GetExampleById(ctx context.Context, id uint) (*vo.ExampleDetailResponse, error) {
	// 使用缓存包装，缓存30分钟（1800秒）
	// 缓存键包含示例ID参数，确保不同ID有不同的缓存
	cacheKey := fmt.Sprintf("%s:%d", utils.CacheKeyExampleById, id)
	return utils.CacheFunc2(cacheKey, func(...any) (*vo.ExampleDetailResponse, error) {
		// 根据ID查询示例
		example, err := s.exampleRepo.GetByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, exception.ErrExampleNotFound
			}
			s.logger.Error("查询示例失败", zap.Error(err), zap.Uint("id", id))
			return nil, exception.ErrExampleDetailQueryFailed
		}

		// 根据template_id查询模板获取resume_style和component_name
		template, err := s.templateRepo.GetByID(ctx, example.TemplateID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				s.logger.Warn("模板不存在", zap.Uint("template_id", example.TemplateID))
				// 模板不存在时，resume_style使用空对象，component_name为空字符串
			} else {
				s.logger.Error("查询模板失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
				return nil, exception.ErrInternalServer.WithMessage("查询模板失败")
			}
		}

		// 生成描述文案
		desc := s.generateExampleDesc(example, template)

		// 构建响应对象
		response := &vo.ExampleDetailResponse{
			ID:         example.ID,
			TemplateID: example.TemplateID,
			UsageCount: example.UsageCount,
			Desc:       desc,

			// 简历内容字段
			BasicInfo:       example.BasicInfo,
			Education:       example.Education,
			Work:            example.Work,
			Project:         example.Project,
			Research:        example.Research,
			Team:            example.Team,
			Portfolio:       example.Portfolio,
			Other:           example.Other,
			PersonalSummary: example.PersonalSummary,
			Honors:          example.Honors,
			Skills:          example.Skills,
			CustomModules:   example.CustomModules,
			Slogan:          example.Slogan,

			// 时间字段
			CreatedAt: example.CreatedAt,
			UpdatedAt: example.UpdatedAt,
		}

		// 如果模板存在，设置resume_style和component_name
		if template != nil {
			response.ResumeStyle = template.ResumeStyle
			response.ComponentName = template.ComponentName
		}

		// 获取tags数据：从职位表中level为3随机取30个
		tags, err := s.getRandomPositionTags()
		if err != nil {
			return nil, err
		}

		// 获取更多推荐
		moreRecommendations, err := s.getMoreRecommendations()
		if err != nil {
			return nil, err
		}

		// 获取热门简历推荐
		hotResumeRecommendations, err := s.getHotResumeRecommendations()
		if err != nil {
			return nil, err
		}

		// 设置tags和更多推荐
		response.Tags = tags
		response.MoreRecommendations = moreRecommendations
		response.HotResumeRecommendations = hotResumeRecommendations

		return response, nil
	}, 1800, s.redisClient)
}

// GetExampleTdkById 根据ID获取示例TDK信息
func (s *exampleService) GetExampleTdkById(ctx context.Context, id uint) (*vo.ExampleTdkResponse, error) {
	// 根据ID查询示例
	example, err := s.exampleRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exception.ErrExampleNotFound
		}
		s.logger.Error("查询示例失败", zap.Error(err), zap.Uint("id", id))
		return nil, exception.ErrExampleTdkQueryFailed
	}

	// 构建TDK响应对象
	response := &vo.ExampleTdkResponse{
		ID:          example.ID,
		Title:       example.SeoTitle,
		Description: example.SeoDescription,
		Keywords:    example.SeoKeywords,
	}

	return response, nil
}

// UseExample 使用例子创建简历
func (s *exampleService) UseExample(ctx *gin.Context, exampleID uint, userID uint, jwtToken string, fingerprint string) (*vo.UseExampleResponse, error) {
	// 1. 校验简历创建权限
	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	validationResult, err := s.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), userID, isLoggedInBool, enum.PrivilegeResumeCreate)
	if err != nil {
		s.logger.Error("权益校验失败",
			zap.Uint("user_id", userID),
			zap.Uint("example_id", exampleID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("权益校验失败")
	}

	if !validationResult.Allowed {
		s.logger.Warn("用户简历创建权益不足",
			zap.Uint("user_id", userID),
			zap.Uint("example_id", exampleID),
			zap.String("reason", validationResult.Reason))
		return nil, exception.ErrForbidden.WithDetail(validationResult.Reason)
	}

	// 2. 根据ID获取示例数据
	example, err := s.exampleRepo.GetByID(ctx.Request.Context(), exampleID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warn("示例不存在", zap.Uint("example_id", exampleID))
			return nil, exception.ErrExampleNotFound
		}
		s.logger.Error("查询示例失败", zap.Error(err), zap.Uint("example_id", exampleID))
		return nil, exception.ErrExampleQueryFailed
	}

	// 2. 获取模板信息（用于获取ResumeStyle）
	template, err := s.templateRepo.GetByID(ctx.Request.Context(), example.TemplateID)
	if err != nil {
		s.logger.Error("查询模板失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
		return nil, exception.ErrExampleUseFailed.WithMessage("查询模板失败")
	}

	// 3. 创建新简历（从示例复制数据）
	newResume := &models.Resume{
		UserID:          userID,
		TemplateID:      example.TemplateID,
		ResumeName:      example.Name + "-复制",
		PreviewImageUrl: "",     // 预览图需要重新生成
		CompletionRate:  "100%", // 设置默认完成度
	}

	// 创建简历记录
	if err := s.resumeService.CreateResume(ctx.Request.Context(), newResume); err != nil {
		s.logger.Error("创建简历失败", zap.Uint("user_id", userID), zap.Error(err))
		return nil, exception.ErrExampleUseFailed.WithMessage("创建简历失败")
	}

	// 4. 创建新简历详情（从示例复制数据，如果数据为空则设置可视参数为false）
	newResumeDetail := &models.ResumeDetail{
		UserID:          userID,
		ResumeID:        newResume.ID,
		BasicInfo:       s.processBasicInfo(example.BasicInfo),
		Education:       s.processEducation(example.Education),
		Work:            s.processWork(example.Work),
		Project:         s.processProject(example.Project),
		Research:        s.processResearch(example.Research),
		Team:            s.processTeam(example.Team),
		Portfolio:       s.processPortfolio(example.Portfolio),
		Other:           s.processOther(example.Other),
		PersonalSummary: s.processPersonalSummary(example.PersonalSummary),
		Honors:          s.processHonors(example.Honors),
		Skills:          s.processSkills(example.Skills),
		CustomModules:   s.processCustomModules(example.CustomModules),
		Slogan:          s.processSlogan(example.Slogan),
		ResumeStyle:     template.ResumeStyle, // 使用模板的样式配置
	}

	// 创建简历详情记录
	if err := s.resumeService.CreateResumeDetail(ctx.Request.Context(), newResumeDetail); err != nil {
		s.logger.Error("创建简历详情失败", zap.Uint("user_id", userID), zap.Uint("resume_id", newResume.ID), zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("创建简历详情失败")
	}

	s.logger.Info("使用例子创建简历成功",
		zap.Uint("example_id", exampleID),
		zap.Uint("resume_id", newResume.ID),
		zap.Uint("user_id", userID),
		zap.String("resume_name", newResume.ResumeName))

	// 异步生成简历预览图
	s.resumeService.GenerateResumePreviewImage(newResume.ID, userID, jwtToken, fingerprint)

	return &vo.UseExampleResponse{
		ResumeID: newResume.ID,
	}, nil
}

// GetHomePageExamples 获取首页示例列表
func (s *exampleService) GetHomePageExamples(ctx context.Context) ([]vo.ExampleListItemResponse, error) {
	// 使用缓存包装，永不过期（ttl设为0表示永不过期）
	return utils.CacheFunc2(utils.CacheKeyHomePageExamples, func(...any) ([]vo.ExampleListItemResponse, error) {
		// 随机获取16个示例
		examples, err := s.exampleRepo.GetRandomExamples(ctx, 16)
		if err != nil {
			s.logger.Error("获取首页示例失败", zap.Error(err))
			return nil, exception.ErrExampleQueryFailed.WithMessage("获取首页示例失败")
		}

		// 转换为响应格式
		var result []vo.ExampleListItemResponse
		for _, example := range examples {
			// 获取模板信息以获取标签和使用次数
			template, err := s.templateRepo.GetByID(ctx, example.TemplateID)
			if err != nil {
				s.logger.Warn("获取模板信息失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
				// 如果获取模板失败，使用默认值
				template = &models.Template{
					Tags:         []string{},
					TemplateName: example.Name,
					UsageCount:   0, // 这个字段不再使用，但保留结构完整性
				}
			}

			item := vo.ExampleListItemResponse{
				ID:              example.ID,
				PreviewImageUrl: example.PreviewImageUrl,
				TemplateID:      example.TemplateID,
				Tags:            template.Tags,
				Name:            example.Name,       // 使用example表的name字段
				UsageCount:      example.UsageCount, // 使用example表的usage_count字段
			}
			result = append(result, item)
		}

		s.logger.Info("获取首页示例成功", zap.Int("count", len(result)))
		return result, nil
	}, 0, s.redisClient)
}

// GetTemplateRecommendationsWithCache 获取模板推荐列表（带永久缓存）
func (s *exampleService) GetTemplateRecommendationsWithCache(ctx context.Context) ([]vo.ExampleListItemResponse, error) {
	// 使用缓存包装，永久缓存（ttl设为0表示永不过期）
	return utils.CacheFunc2(utils.CacheKeyTemplateRecommendations, func(...any) ([]vo.ExampleListItemResponse, error) {
		// 随机获取20个示例作为模板推荐
		examples, err := s.exampleRepo.GetRandomExamples(ctx, 20)
		if err != nil {
			s.logger.Error("获取模板推荐失败", zap.Error(err))
			return nil, exception.ErrExampleQueryFailed.WithMessage("获取模板推荐失败")
		}

		// 转换为响应格式
		var result []vo.ExampleListItemResponse
		for _, example := range examples {
			// 获取模板信息以获取标签
			template, err := s.templateRepo.GetByID(ctx, example.TemplateID)
			if err != nil {
				s.logger.Warn("获取模板信息失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
				// 如果获取模板失败，使用默认值
				template = &models.Template{
					Tags:         []string{},
					TemplateName: example.Name,
				}
			}

			item := vo.ExampleListItemResponse{
				ID:              example.ID,
				PreviewImageUrl: example.PreviewImageUrl,
				TemplateID:      example.TemplateID,
				Tags:            template.Tags,
				Name:            example.Name,       // 使用example表的name字段
				UsageCount:      example.UsageCount, // 使用example表的usage_count字段
			}
			result = append(result, item)
		}

		s.logger.Info("获取模板推荐成功", zap.Int("count", len(result)))
		return result, nil
	}, 0, s.redisClient)
}

// GetTemplateRecommendations 获取模板推荐列表（不带缓存）
func (s *exampleService) GetTemplateRecommendations(ctx context.Context) ([]vo.ExampleListItemResponse, error) {
	// 随机获取20个示例作为模板推荐
	examples, err := s.exampleRepo.GetRandomExamples(ctx, 20)
	if err != nil {
		s.logger.Error("获取模板推荐失败", zap.Error(err))
		return nil, exception.ErrExampleQueryFailed.WithMessage("获取模板推荐失败")
	}

	// 转换为响应格式
	var result []vo.ExampleListItemResponse
	for _, example := range examples {
		// 获取模板信息以获取标签
		template, err := s.templateRepo.GetByID(ctx, example.TemplateID)
		if err != nil {
			s.logger.Warn("获取模板信息失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
			// 如果获取模板失败，使用默认值
			template = &models.Template{
				Tags:         []string{},
				TemplateName: example.Name,
			}
		}

		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			Tags:            template.Tags,
			Name:            example.Name,       // 使用example表的name字段
			UsageCount:      example.UsageCount, // 使用example表的usage_count字段
		}
		result = append(result, item)
	}

	s.logger.Info("获取模板推荐成功（不带缓存）", zap.Int("count", len(result)))
	return result, nil
}

// processBasicInfo 处理基本信息，如果数据为空则设置可视参数为false
func (s *exampleService) processBasicInfo(basicInfo models.BasicInfo) models.BasicInfo {
	// BasicInfo 通常不为空，因为包含基本的姓名等信息，保持原样
	return basicInfo
}

// processEducation 处理教育经历，如果数据为空则设置可视参数为false
func (s *exampleService) processEducation(education models.Education) models.Education {
	if len(education.Item) == 0 {
		education.IsVisible = false
	}
	return education
}

// processWork 处理工作经历，如果数据为空则设置可视参数为false
func (s *exampleService) processWork(work models.Work) models.Work {
	if len(work.Item) == 0 {
		work.IsVisible = false
	}
	return work
}

// processProject 处理项目经历，如果数据为空则设置可视参数为false
func (s *exampleService) processProject(project models.Project) models.Project {
	if len(project.Item) == 0 {
		project.IsVisible = false
	}
	return project
}

// processResearch 处理研究经历，如果数据为空则设置可视参数为false
func (s *exampleService) processResearch(research models.Research) models.Research {
	if len(research.Item) == 0 {
		research.IsVisible = false
	}
	return research
}

// processTeam 处理社团经历，如果数据为空则设置可视参数为false
func (s *exampleService) processTeam(team models.Team) models.Team {
	if len(team.Item) == 0 {
		team.IsVisible = false
	}
	return team
}

// processPortfolio 处理作品集，如果数据为空则设置可视参数为false
func (s *exampleService) processPortfolio(portfolio models.Portfolio) models.Portfolio {
	if len(portfolio.Item) == 0 {
		portfolio.IsVisible = false
	}
	return portfolio
}

// processOther 处理其他模块，如果数据为空则设置可视参数为false
func (s *exampleService) processOther(other models.Other) models.Other {
	if len(other.Item) == 0 {
		other.IsVisible = false
	}
	return other
}

// processPersonalSummary 处理个人总结，如果数据为空则设置可视参数为false
func (s *exampleService) processPersonalSummary(personalSummary models.PersonalSummary) models.PersonalSummary {
	// 检查个人总结内容是否为空
	if personalSummary.Item.Summary.Value == "" {
		personalSummary.IsVisible = false
	}
	return personalSummary
}

// processHonors 处理荣誉栏，如果数据为空则设置可视参数为false
func (s *exampleService) processHonors(honors models.Honors) models.Honors {
	// 检查荣誉栏内容是否为空
	if honors.Item.Values.Value == "" {
		honors.IsVisible = false
	}
	return honors
}

// processSkills 处理技能条，如果数据为空则设置可视参数为false
func (s *exampleService) processSkills(skills models.Skills) models.Skills {
	// 检查技能条内容是否为空
	if skills.Item.Values.Value == "" {
		skills.IsVisible = false
	}
	return skills
}

// processCustomModules 处理自定义模块，如果数据为空则设置可视参数为false
func (s *exampleService) processCustomModules(customModules models.CustomModules) models.CustomModules {
	if len(customModules) == 0 {
		// CustomModules 是数组类型，如果为空则返回空数组
		return models.CustomModules{}
	}
	return customModules
}

// processSlogan 处理简历标语，如果数据为空则设置可视参数为false
func (s *exampleService) processSlogan(slogan models.Slogan) models.Slogan {
	// Slogan 没有 IsVisible 字段，直接返回原值
	return slogan
}

// getRandomPositionTags 获取随机的职位标签（level为3的职位，随机取30个）
func (s *exampleService) getRandomPositionTags() ([]vo.CategoryTagResponse, error) {
	// 从职位表中获取level为3的职位，随机取30个
	positions, err := s.positionRepo.GetRandomPositionsByLevel(context.TODO(), 3, 30)
	if err != nil {
		return nil, err
	}

	// 转换为CategoryTagResponse格式
	var tags []vo.CategoryTagResponse
	for _, position := range positions {
		tag := vo.CategoryTagResponse{
			Name: position.PositionName,
			URL:  fmt.Sprintf("/jianli/%s/", position.SlugCn),
		}
		tags = append(tags, tag)
	}

	return tags, nil
}

// getMoreRecommendations 获取更多推荐（随机20个示例）
func (s *exampleService) getMoreRecommendations() ([]vo.ExampleListItemResponse, error) {
	// 从example表中随机查询20个记录
	examples, err := s.exampleRepo.GetRandomExamples(context.Background(), 20)
	if err != nil {
		return nil, err
	}

	// 转换为ExampleListItemResponse格式
	var recommendations []vo.ExampleListItemResponse
	for _, example := range examples {
		// 查询模板信息获取预览图和标签
		template, err := s.templateRepo.GetByID(context.Background(), example.TemplateID)
		if err != nil {
			s.logger.Warn("查询模板失败", zap.Error(err), zap.Uint("template_id", example.TemplateID))
			continue
		}

		recommendation := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			Tags:            template.Tags,
			Name:            example.Name,       // 使用example表的name字段
			UsageCount:      example.UsageCount, // 使用example表的usage_count字段
		}
		recommendations = append(recommendations, recommendation)
	}

	return recommendations, nil
}

// getHotResumeRecommendations 获取热门简历推荐（随机10个示例）
func (s *exampleService) getHotResumeRecommendations() ([]vo.HotResumeRecommendation, error) {
	// 从example表中随机查询10个记录
	examples, err := s.exampleRepo.GetRandomExamples(context.Background(), 10)
	if err != nil {
		return nil, err
	}

	// 转换为HotResumeRecommendation格式
	var recommendations []vo.HotResumeRecommendation
	for _, example := range examples {
		recommendation := vo.HotResumeRecommendation{
			Name:       example.Name,
			Link:       fmt.Sprintf("/jianli/%d.html", example.ID),
			UsageCount: example.UsageCount,
		}
		recommendations = append(recommendations, recommendation)
	}

	return recommendations, nil
}

// generateExampleDesc 生成示例描述文案
func (s *exampleService) generateExampleDesc(example *models.Example, template *models.Template) string {
	exampleName := example.Name

	// 默认描述
	if template == nil {
		return fmt.Sprintf("熊猫简历%s简历模板，专业AI辅助一键优化%s简历内容，仅需5分钟即可拥有一份精美的%s简历模板，助力你获得「高薪职位」。",
			exampleName, exampleName, exampleName)
	}

	// 根据template的style_id数组查询category表获取所有风格名称
	var categoryNames []string
	if len(template.StyleID) > 0 {
		var categories []models.Category
		err := s.db.Where("id IN ?", template.StyleID).Find(&categories).Error
		if err == nil {
			for _, category := range categories {
				categoryNames = append(categoryNames, category.CategoryName)
			}
		}
	}

	// 获取template的tags数据
	tagsStr := ""
	if len(template.Tags) > 0 {
		tagsStr = strings.Join(template.Tags, "、")
	}

	// 构建描述文案
	desc := fmt.Sprintf("熊猫简历%s简历模板", exampleName)

	// 添加风格信息
	if len(categoryNames) > 0 {
		categoryStr := strings.Join(categoryNames, "、")
		desc += fmt.Sprintf("，%s风格简历模板", categoryStr)
	}

	// 添加支持的功能（从template.Tags获取）
	if tagsStr != "" {
		desc += fmt.Sprintf("，支持%s", tagsStr)
	}

	// 添加结尾部分
	desc += fmt.Sprintf("，专业AI辅助一键优化%s简历内容，仅需5分钟即可拥有一份精美的%s简历模板，助力你获得「高薪职位」。",
		exampleName, exampleName)

	return desc
}
