package services

import (
	"context"
	"fmt"
	"time"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// EmailCodeService 邮件验证码服务接口
type EmailCodeService interface {
	// SendEmailCode 发送邮件验证码
	SendEmailCode(ctx context.Context, email string) error

	// VerifyEmailCode 验证邮件验证码
	VerifyEmailCode(ctx context.Context, email, code string) (bool, error)
}

// emailCodeService 邮件验证码服务实现
type emailCodeService struct {
	redisClient  *redis.Client
	emailService pkg.EmailService
	config       *config.Config
	logger       *zap.Logger
}

// NewEmailCodeService 创建邮件验证码服务
func NewEmailCodeService(
	redisClient *redis.Client,
	emailService pkg.EmailService,
	cfg *config.Config,
	logger *zap.Logger,
) EmailCodeService {
	return &emailCodeService{
		redisClient:  redisClient,
		emailService: emailService,
		config:       cfg,
		logger:       logger,
	}
}

// SendEmailCode 发送邮件验证码
func (s *emailCodeService) SendEmailCode(ctx context.Context, email string) error {
	// 验证邮箱格式
	if !isValidEmail(email) {
		return exception.ErrEmailInvalid
	}

	// 检查发送频率限制
	rateLimitKey := fmt.Sprintf("email:ratelimit:%s", email)
	exists, err := s.redisClient.Exists(ctx, rateLimitKey).Result()
	if err != nil {
		s.logger.Error("检查邮件发送频率失败", zap.Error(err))
		return exception.ErrEmailCodeVerifyFailed
	}

	if exists == 1 {
		remainingTime, err := s.redisClient.TTL(ctx, rateLimitKey).Result()
		if err != nil {
			s.logger.Error("获取邮件发送频率限制剩余时间失败", zap.Error(err))
			return exception.ErrEmailCodeVerifyFailed
		}

		seconds := int(remainingTime.Seconds())
		return exception.ErrEmailRateLimit.WithDetail(fmt.Sprintf("请%d秒后再试", seconds))
	}

	// 生成6位随机验证码
	code := utils.GenerateRandomCode(6)
	s.logger.Info("生成邮件验证码", zap.String("code", code))

	// 将验证码保存到Redis，有效期5分钟
	codeKey := fmt.Sprintf("email:code:%s", email)
	err = s.redisClient.Set(ctx, codeKey, code, 5*time.Minute).Err()
	if err != nil {
		s.logger.Error("保存验证码到Redis失败", zap.Error(err))
		return exception.ErrEmailCodeSendFailed
	}

	// 设置发送频率限制，1分钟内不能再次发送
	err = s.redisClient.Set(ctx, rateLimitKey, 1, 1*time.Minute).Err()
	if err != nil {
		s.logger.Error("设置邮件发送频率限制失败", zap.Error(err))
		// 继续执行，不影响主流程
	}

	// 发送邮件验证码
	err = s.emailService.SendVerificationCode(email, code)
	if err != nil {
		s.logger.Error("发送邮件验证码失败", zap.String("email", email), zap.Error(err))
		return exception.ErrEmailSendFailed.WithDetail("发送邮件失败，请稍后再试")
	}

	s.logger.Info("邮件验证码发送成功", zap.String("email", email))
	return nil
}

// VerifyEmailCode 验证邮件验证码
func (s *emailCodeService) VerifyEmailCode(ctx context.Context, email, code string) (bool, error) {
	codeKey := fmt.Sprintf("email:code:%s", email)

	// 从Redis获取验证码
	savedCode, err := s.redisClient.Get(ctx, codeKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 验证码不存在或已过期
			return false, exception.ErrEmailCodeInvalid
		}
		s.logger.Error("从Redis获取验证码失败", zap.Error(err))
		return false, exception.ErrEmailCodeVerifyFailed
	}

	// 验证码比对
	isValid := savedCode == code

	// 如果验证成功，删除验证码，防止重复使用
	if isValid {
		s.redisClient.Del(ctx, codeKey)
	}

	return isValid, nil
}

// 验证邮箱格式（简单验证）
func isValidEmail(email string) bool {
	// 简单验证，实际项目中可以使用正则表达式进行更严格的验证
	return len(email) >= 3 && len(email) <= 255 &&
		contains(email, "@") && contains(email, ".")
}

// 检查字符串是否包含指定子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s != substr && s != "" && substr != "" &&
		func() bool {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
			return false
		}()
}
