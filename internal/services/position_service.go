package services

import (
	"context"
	"fmt"
	"strconv"

	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// PositionService 职位服务接口
type PositionService interface {
	// GetAllPositionsHierarchy 获取所有职位的层级结构
	GetAllPositionsHierarchy(ctx context.Context) (*vo.PositionListResponse, error)
	// GetPositionsByParentID 根据父级ID获取子分类的层级结构
	GetPositionsByParentID(ctx context.Context, parentIDStr string) (*vo.PositionListResponse, error)
	// SearchPositions 搜索职位关键字
	SearchPositions(ctx context.Context, keyword string) (*vo.SearchResponse, error)
	// InitializeMeiliSearchData 初始化MeiliSearch数据
	InitializeMeiliSearchData(ctx context.Context) error
}

// positionService 职位服务实现
type positionService struct {
	positionRepo       repository.PositionRepository
	categoryRepo       repository.CategoryRepository
	exampleRepo        repository.ExampleRepository
	meiliSearchService *pkg.MeiliSearchService
	redisClient        *redis.Client
	logger             *zap.Logger
}

// NewPositionService 创建职位服务
func NewPositionService(
	positionRepo repository.PositionRepository,
	categoryRepo repository.CategoryRepository,
	exampleRepo repository.ExampleRepository,
	meiliSearchService *pkg.MeiliSearchService,
	redisClient *redis.Client,
	logger *zap.Logger,
) PositionService {
	return &positionService{
		positionRepo:       positionRepo,
		categoryRepo:       categoryRepo,
		exampleRepo:        exampleRepo,
		meiliSearchService: meiliSearchService,
		redisClient:        redisClient,
		logger:             logger,
	}
}

// GetAllPositionsHierarchy 获取所有职位的层级结构
func (s *positionService) GetAllPositionsHierarchy(ctx context.Context) (*vo.PositionListResponse, error) {
	// 使用缓存包装，永不过期（ttl设为0表示永不过期）
	return utils.CacheFunc2(utils.CacheKeyAllPositionsHierarchy, func(...any) (*vo.PositionListResponse, error) {
		// 从仓库获取所有职位数据
		positions, err := s.positionRepo.GetAllPositions(ctx)
		if err != nil {
			s.logger.Error("获取职位数据失败", zap.Error(err))
			return nil, exception.ErrPositionQueryFailed
		}

		// 构建层级结构
		hierarchy := s.buildHierarchy(positions)

		response := vo.PositionListResponse(hierarchy)
		return &response, nil
	}, 0, s.redisClient)
}

// buildHierarchy 构建层级结构
func (s *positionService) buildHierarchy(positions []*models.Position) []vo.PositionItemResponse {
	// 创建ID到职位的映射
	positionMap := make(map[uint]*models.Position)
	for _, position := range positions {
		positionMap[position.ID] = position
	}

	// 创建父级ID到子级列表的映射
	childrenMap := make(map[uint][]vo.PositionItemResponse)

	// 遍历所有职位，构建子级映射
	for _, position := range positions {
		item := vo.PositionItemResponse{
			ID:     position.ID,
			Name:   position.PositionName,
			SlugCn: position.SlugCn,
			SlugEn: position.SlugEn,
		}

		childrenMap[position.ParentID] = append(childrenMap[position.ParentID], item)
	}

	// 递归设置子级
	var setChildren func(items []vo.PositionItemResponse) []vo.PositionItemResponse
	setChildren = func(items []vo.PositionItemResponse) []vo.PositionItemResponse {
		for i := range items {
			if children, exists := childrenMap[items[i].ID]; exists {
				items[i].Children = setChildren(children)
			}
		}
		return items
	}

	// 获取顶级职位（ParentID为0的职位）
	topLevel := childrenMap[0]
	return setChildren(topLevel)
}

// GetPositionsByParentID 根据父级ID获取子分类的层级结构
func (s *positionService) GetPositionsByParentID(ctx context.Context, parentIDStr string) (*vo.PositionListResponse, error) {
	// 转换为uint类型（参数已通过验证中间件验证，确保是有效数字）
	parentID, err := strconv.ParseUint(parentIDStr, 10, 32)
	if err != nil {
		s.logger.Error("转换父级ID失败", zap.String("parent_id", parentIDStr), zap.Error(err))
		return nil, exception.ErrPositionParentIDConvertFailed
	}

	// 使用缓存包装，永不过期（ttl设为0表示永不过期）
	// 缓存键包含parentID参数，确保不同parentID有不同的缓存
	cacheKey := fmt.Sprintf("%s:%s", utils.CacheKeyPositionsByParentID, parentIDStr)
	return utils.CacheFunc2(cacheKey, func(...any) (*vo.PositionListResponse, error) {
		// 从仓库获取指定父级ID下的所有子分类数据
		positions, err := s.positionRepo.GetPositionsByParentID(ctx, uint(parentID))
		if err != nil {
			s.logger.Error("获取子分类数据失败", zap.Uint64("parent_id", parentID), zap.Error(err))
			return nil, exception.ErrPositionChildrenQueryFailed
		}

		// 构建层级结构
		hierarchy := s.buildHierarchyFromPositions(positions, uint(parentID))

		response := vo.PositionListResponse(hierarchy)
		return &response, nil
	}, 0, s.redisClient)
}

// buildHierarchyFromPositions 从指定的职位列表构建层级结构
func (s *positionService) buildHierarchyFromPositions(positions []*models.Position, rootParentID uint) []vo.PositionItemResponse {
	// 创建ID到职位的映射
	positionMap := make(map[uint]*models.Position)
	for _, position := range positions {
		positionMap[position.ID] = position
	}

	// 创建父级ID到子级列表的映射
	childrenMap := make(map[uint][]vo.PositionItemResponse)

	// 遍历所有职位，构建子级映射
	for _, position := range positions {
		item := vo.PositionItemResponse{
			ID:     position.ID,
			Name:   position.PositionName,
			SlugCn: position.SlugCn,
			SlugEn: position.SlugEn,
		}

		childrenMap[position.ParentID] = append(childrenMap[position.ParentID], item)
	}

	// 递归设置子级
	var setChildren func(items []vo.PositionItemResponse) []vo.PositionItemResponse
	setChildren = func(items []vo.PositionItemResponse) []vo.PositionItemResponse {
		for i := range items {
			if children, exists := childrenMap[items[i].ID]; exists {
				items[i].Children = setChildren(children)
			}
		}
		return items
	}

	// 获取指定父级ID下的直接子级
	directChildren := childrenMap[rootParentID]
	return setChildren(directChildren)
}

// SearchPositions 搜索职位关键字
func (s *positionService) SearchPositions(ctx context.Context, keyword string) (*vo.SearchResponse, error) {
	// 参数验证（虽然验证中间件已经验证过，但保留作为防御性编程）
	if keyword == "" {
		return nil, exception.ErrInvalidParam.WithDetail("搜索关键字不能为空")
	}

	// 调用MeiliSearch服务进行搜索
	searchResult, err := s.meiliSearchService.Search(keyword)
	if err != nil {
		s.logger.Error("搜索职位失败", zap.Error(err), zap.String("keyword", keyword))
		return nil, exception.ErrPositionSearchUnavailable
	}

	// 转换为VO格式
	var hits []vo.SearchResultItem
	for _, hit := range searchResult.Hits {
		item := vo.SearchResultItem{
			ID:     hit.ID,
			Type:   hit.Type,
			Name:   hit.Name,
			SlugCn: hit.SlugCn,
			URL:    hit.URL,
		}
		hits = append(hits, item)
	}

	response := &vo.SearchResponse{
		Hits:           hits,
		Query:          searchResult.Query,
		ProcessingTime: searchResult.ProcessingTime,
		Limit:          searchResult.Limit,
		Offset:         searchResult.Offset,
		EstimatedTotal: searchResult.EstimatedTotal,
	}

	return response, nil
}

// InitializeMeiliSearchData 初始化MeiliSearch数据
func (s *positionService) InitializeMeiliSearchData(ctx context.Context) error {
	s.logger.Info("开始初始化MeiliSearch数据")

	// 0. 先清空现有的所有文档
	err := s.meiliSearchService.ClearAllDocuments()
	if err != nil {
		s.logger.Error("清空MeiliSearch现有数据失败", zap.Error(err))
		return exception.ErrInternalServer.WithMessage("清空搜索数据失败")
	}
	s.logger.Info("已清空MeiliSearch现有数据")

	// 0.1 设置可搜索字段
	err = s.meiliSearchService.SetSearchableAttributes([]string{"name", "slug_cn"})
	if err != nil {
		s.logger.Error("设置可搜索字段失败", zap.Error(err))
		return exception.ErrInternalServer.WithMessage("设置搜索字段失败")
	}
	s.logger.Info("已设置可搜索字段: name, slug_cn")

	var documents []pkg.SearchDocument

	// 1. 获取level为1和3的position数据
	positions, err := s.positionRepo.GetPositionsByLevels(ctx, []int{1, 3})
	if err != nil {
		s.logger.Error("获取position数据失败", zap.Error(err))
		return exception.ErrPositionQueryFailed
	}

	// 2. 转换position数据为搜索文档
	for _, position := range positions {
		doc := pkg.SearchDocument{
			ID:     fmt.Sprintf("position_%d", position.ID),
			Type:   1, // 1为position
			Name:   position.PositionName,
			SlugCn: position.SlugCn,
			URL:    fmt.Sprintf("/jianli/%s/", position.SlugCn),
		}
		documents = append(documents, doc)
	}

	s.logger.Info("转换position数据完成", zap.Int("count", len(positions)))

	// 2.1. 获取所有category数据
	categories, err := s.categoryRepo.GetAllCategories(ctx)
	if err != nil {
		s.logger.Error("获取category数据失败", zap.Error(err))
		return exception.ErrInternalServer.WithMessage("获取category数据失败")
	}

	// 2.2. 转换category数据为搜索文档
	for _, category := range categories {
		doc := pkg.SearchDocument{
			ID:     fmt.Sprintf("category_%d", category.ID),
			Type:   1, // 1为category，与position相同
			Name:   category.CategoryName,
			SlugCn: category.SlugCn,
			URL:    fmt.Sprintf("/jianli/%s/", category.SlugCn),
		}
		documents = append(documents, doc)
	}

	s.logger.Info("转换category数据完成", zap.Int("count", len(categories)))

	// 3. 获取所有example数据
	examples, err := s.exampleRepo.GetAllExamples(ctx)
	if err != nil {
		s.logger.Error("获取example数据失败", zap.Error(err))
		return exception.ErrInternalServer.WithMessage("获取example数据失败")
	}

	// 4. 转换example数据为搜索文档
	for _, example := range examples {
		doc := pkg.SearchDocument{
			ID:     fmt.Sprintf("example_%d", example.ID),
			Type:   2, // 2为example
			Name:   example.Name,
			SlugCn: "", // example表没有slug_cn字段
			URL:    fmt.Sprintf("/jianli/%d.html", example.ID),
		}
		documents = append(documents, doc)
	}

	s.logger.Info("转换example数据完成", zap.Int("count", len(examples)))

	// 5. 批量添加到MeiliSearch
	if len(documents) > 0 {
		err = s.meiliSearchService.AddDocuments(documents)
		if err != nil {
			s.logger.Error("批量添加文档到MeiliSearch失败", zap.Error(err))
			return exception.ErrInternalServer.WithMessage("初始化搜索数据失败")
		}
	}

	s.logger.Info("MeiliSearch数据初始化完成", zap.Int("total_documents", len(documents)))
	return nil
}
