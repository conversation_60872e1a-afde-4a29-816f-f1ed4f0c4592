package services

import (
	"context"

	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/vo"
	"go.uber.org/zap"
)

// ResumeScoreService 简历评分服务接口
type ResumeScoreService interface {
	// GetResumeScoreDetail 根据ID获取简历评分详情
	GetResumeScoreDetail(ctx context.Context, userID uint, scoreID uint) (*vo.ResumeScoreDetailResponse, error)
	// GetMyResumeScores 获取我的所有简历评分记录
	GetMyResumeScores(ctx context.Context, userID uint, limit int, offset int) (*vo.ResumeScoreListResponse, error)
	// DeleteResumeScore 删除简历评分记录
	DeleteResumeScore(ctx context.Context, userID uint, scoreID uint) error
}

// resumeScoreService 简历评分服务实现
type resumeScoreService struct {
	resumeScoreRepo    repository.ResumeScoreRepository
	resumeRepo         repository.ResumeRepository
	targetPositionRepo repository.TargetPositionRepository
	logger             *zap.Logger
}

// NewResumeScoreService 创建简历评分服务
func NewResumeScoreService(
	resumeScoreRepo repository.ResumeScoreRepository,
	resumeRepo repository.ResumeRepository,
	targetPositionRepo repository.TargetPositionRepository,
	logger *zap.Logger,
) ResumeScoreService {
	return &resumeScoreService{
		resumeScoreRepo:    resumeScoreRepo,
		resumeRepo:         resumeRepo,
		targetPositionRepo: targetPositionRepo,
		logger:             logger,
	}
}

// GetResumeScoreDetail 根据ID获取简历评分详情
func (s *resumeScoreService) GetResumeScoreDetail(ctx context.Context, userID uint, scoreID uint) (*vo.ResumeScoreDetailResponse, error) {
	// 1. 检查评分记录是否属于当前用户
	isOwner, err := s.resumeScoreRepo.CheckOwnership(ctx, scoreID, userID)
	if err != nil {
		s.logger.Error("检查简历评分记录所有权失败",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID),
			zap.Error(err))
		return nil, err
	}

	if !isOwner {
		s.logger.Warn("用户无权访问该简历评分记录",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID))
		return nil, err // 这里应该返回一个权限错误
	}

	// 2. 获取评分记录
	scoreRecord, err := s.resumeScoreRepo.GetByID(ctx, scoreID)
	if err != nil {
		s.logger.Error("获取简历评分记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID),
			zap.Error(err))
		return nil, err
	}

	// 3. 获取简历信息
	resume, err := s.resumeRepo.GetResumeByID(ctx, scoreRecord.ResumeID)
	if err != nil {
		s.logger.Error("获取简历信息失败",
			zap.Uint("resume_id", scoreRecord.ResumeID),
			zap.Error(err))
		return nil, err
	}

	// 4. 获取目标岗位信息
	targetPosition, err := s.targetPositionRepo.GetByUserIDAndID(ctx, userID, scoreRecord.TargetPositionID)
	if err != nil {
		s.logger.Error("获取目标岗位信息失败",
			zap.Uint("target_position_id", scoreRecord.TargetPositionID),
			zap.Error(err))
		return nil, err
	}

	// 5. 获取详细评价数据
	languageDetails, _ := scoreRecord.GetLanguageExpressionDetails()
	informationDetails, _ := scoreRecord.GetInformationCompletenessDetails()
	contentDetails, _ := scoreRecord.GetContentRelevanceDetails()
	professionalismDetails, _ := scoreRecord.GetProfessionalismDetails()

	// 6. 构建响应数据
	response := &vo.ResumeScoreDetailResponse{
		ID:                             scoreRecord.ID,
		UserID:                         scoreRecord.UserID,
		ResumeID:                       scoreRecord.ResumeID,
		ResumeName:                     resume.ResumeName,
		TargetPositionID:               scoreRecord.TargetPositionID,
		TargetPositionName:             targetPosition.PositionName,
		LanguageExpressionScore:        scoreRecord.LanguageExpressionScore,
		LanguageExpressionDetails:      convertModelToVOScoreDetailItems(languageDetails),
		InformationCompletenessScore:   scoreRecord.InformationCompletenessScore,
		InformationCompletenessDetails: convertModelToVOScoreDetailItems(informationDetails),
		ContentRelevanceScore:          scoreRecord.ContentRelevanceScore,
		ContentRelevanceDetails:        convertModelToVOScoreDetailItems(contentDetails),
		ProfessionalismScore:           scoreRecord.ProfessionalismScore,
		ProfessionalismDetails:         convertModelToVOScoreDetailItems(professionalismDetails),
		OverallScore:                   scoreRecord.OverallScore,
		OverallComment:                 scoreRecord.OverallComment,
		CreatedAt:                      scoreRecord.CreatedAt,
		UpdatedAt:                      scoreRecord.UpdatedAt,
	}

	return response, nil
}

// GetMyResumeScores 获取我的所有简历评分记录
func (s *resumeScoreService) GetMyResumeScores(ctx context.Context, userID uint, limit int, offset int) (*vo.ResumeScoreListResponse, error) {
	// 1. 获取用户的评分记录
	scoreRecords, err := s.resumeScoreRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		s.logger.Error("获取用户简历评分记录失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, err
	}

	// 2. 构建响应数据
	var listItems []vo.ResumeScoreListItem
	for _, record := range scoreRecords {
		// 获取简历信息
		resume, err := s.resumeRepo.GetResumeByID(ctx, record.ResumeID)
		if err != nil {
			s.logger.Error("获取简历信息失败",
				zap.Uint("resume_id", record.ResumeID),
				zap.Error(err))
			continue // 跳过这条记录，继续处理其他记录
		}

		listItems = append(listItems, vo.ResumeScoreListItem{
			ID:           record.ID,
			ResumeID:     record.ResumeID,
			ResumeName:   resume.ResumeName,
			OverallScore: record.OverallScore,
			CreatedAt:    record.CreatedAt,
		})
	}

	// 3. 获取总数（这里简化处理，实际应该单独查询总数）
	total := int64(len(listItems))

	response := &vo.ResumeScoreListResponse{
		List:  listItems,
		Total: total,
	}

	return response, nil
}

// DeleteResumeScore 删除简历评分记录
func (s *resumeScoreService) DeleteResumeScore(ctx context.Context, userID uint, scoreID uint) error {
	// 1. 检查评分记录是否属于当前用户
	isOwner, err := s.resumeScoreRepo.CheckOwnership(ctx, scoreID, userID)
	if err != nil {
		s.logger.Error("检查简历评分记录所有权失败",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID),
			zap.Error(err))
		return err
	}

	if !isOwner {
		s.logger.Warn("用户无权删除该简历评分记录",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID))
		return err // 这里应该返回一个权限错误
	}

	// 2. 删除评分记录
	err = s.resumeScoreRepo.Delete(ctx, scoreID)
	if err != nil {
		s.logger.Error("删除简历评分记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("score_id", scoreID),
			zap.Error(err))
		return err
	}

	s.logger.Info("删除简历评分记录成功",
		zap.Uint("user_id", userID),
		zap.Uint("score_id", scoreID))

	return nil
}

// convertModelToVOScoreDetailItems 将Model的ScoreDetailItem转换为VO的ScoreDetailItem
func convertModelToVOScoreDetailItems(modelItems []models.ScoreDetailItem) []vo.ScoreDetailItem {
	if modelItems == nil {
		return nil
	}

	voItems := make([]vo.ScoreDetailItem, len(modelItems))
	for i, item := range modelItems {
		voItems[i] = vo.ScoreDetailItem{
			Title:   item.Title,
			Comment: item.Comment,
		}
	}
	return voItems
}
