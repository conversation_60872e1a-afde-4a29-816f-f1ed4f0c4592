package services

import (
	"context"
	"encoding/json"
	"errors"
	"mime/multipart"
	"strings"
	"time"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserService 用户服务接口
type UserService interface {
	GetUserByID(id uint) (*models.User, error)
	GetUserByPhone(phone string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	UpdateUser(user *models.User) error
	DeleteUser(id uint) error
	ListUsers(page, pageSize int) ([]*models.User, int64, error)
	LoginByCode(ctx context.Context, phone, code string) (*models.User, error)
	LoginByCodeWithToken(ctx context.Context, phone, code string) (*vo.TokenResponse, error)
	LoginByEmailCode(ctx context.Context, email, code string) (*models.User, error)
	LoginByEmailCodeWithToken(ctx context.Context, email, code string) (*vo.TokenResponse, error)
	LoginByOpenID(openID string) (*models.User, error)
	UpdateAvatar(userID uint, avatarURL string) error
	UpdateAvatarWithFile(ctx context.Context, userID uint, file *multipart.FileHeader) error
	UpdateUsername(userID uint, username string) error
	BindPhone(ctx context.Context, userID uint, phone, code string) error
	BindEmail(ctx context.Context, userID uint, email, code string) error
	BindWechat(ctx context.Context, userID uint, openID string) error
	CheckBindWechatQrCodeStatus(ctx context.Context, userID uint, sceneId string) (*vo.QrCodeStatusResponse, error)
	Logout(ctx context.Context, user *models.User, token string) error
}

// userService 用户服务实现
type userService struct {
	userRepo         repository.UserRepository
	uploadService    UploadService
	codeService      SMSCodeService
	emailCodeService EmailCodeService
	redisClient      *redis.Client
	jwtService       pkg.JWTService
	config           *config.Config
	logger           *zap.Logger
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repository.UserRepository,
	uploadService UploadService,
	codeService SMSCodeService,
	emailCodeService EmailCodeService,
	redisClient *redis.Client,
	jwtService pkg.JWTService,
	cfg *config.Config,
	logger *zap.Logger,
) UserService {
	return &userService{
		userRepo:         userRepo,
		uploadService:    uploadService,
		codeService:      codeService,
		emailCodeService: emailCodeService,
		redisClient:      redisClient,
		jwtService:       jwtService,
		config:           cfg,
		logger:           logger,
	}
}

// LoginByCode 通过手机号和验证码登录
// 先判断手机号是否存在，如果存在则直接返回用户
// 如果不存在则使用指纹查询用户，然后根据用户是否已绑定手机号进行不同的处理
func (s *userService) LoginByCode(ctx context.Context, phone, code string) (*models.User, error) {
	// 验证短信验证码
	valid, err := s.codeService.VerifySMSCode(ctx, phone, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	if !valid {
		pkg.Warn("验证码错误或已过期", zap.String("phone", phone))
		return nil, exception.ErrSMSCodeInvalid
	}

	// 查询手机号是否存在
	user, err := s.userRepo.GetByPhone(phone)
	if err == nil {
		// 如果用户存在，检查用户状态
		if user.Status != enum.UserStatusEnabled {
			pkg.Warn("用户状态异常", zap.String("phone", phone), zap.String("status", user.Status.String()))
			return nil, exception.ErrUserStatusDisabled
		}

		pkg.Info("用户验证码登录成功", zap.String("phone", phone))
		return user, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果是其他错误，直接返回
		pkg.Error("查询用户失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	// 到这里说明手机号不存在，尝试使用指纹查询用户
	// 从上下文中获取指纹
	fingerPrint := ""
	if fp, ok := ctx.Value("fingerPrint").(string); ok && fp != "" {
		fingerPrint = fp
	}

	if fingerPrint == "" {
		// 如果没有指纹，则创建新用户
		return s.createNewUserWithPhone(phone)
	}

	// 使用指纹查询用户
	fingerPrintUser, err := s.userRepo.GetByFingerPrint(fingerPrint)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果指纹用户不存在，则创建新用户
			return s.createNewUserWithPhone(phone)
		}
		// 其他错误
		pkg.Error("查询指纹用户失败", zap.String("fingerPrint", fingerPrint), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	// 如果指纹用户存在，检查是否已绑定手机号
	if fingerPrintUser.Phone != "" {
		// 如果已绑定手机号，则创建新用户
		pkg.Info("指纹用户已绑定手机号，创建新用户",
			zap.String("fingerPrint", fingerPrint),
			zap.String("existingPhone", fingerPrintUser.Phone),
			zap.String("newPhone", phone))
		return s.createNewUserWithPhone(phone)
	}

	// 如果指纹用户是游客用户，则绑定手机号并升级为普通用户
	fingerPrintUser.Phone = phone
	if fingerPrintUser.UserType == enum.UserTypeGuest {
		fingerPrintUser.UserType = enum.UserTypeRegular
		pkg.Info("游客用户升级为普通用户",
			zap.Uint("userID", fingerPrintUser.ID),
			zap.String("fingerPrint", fingerPrint),
			zap.String("phone", phone))
	}

	// 如果指纹用户没有用户名或头像，则生成
	if fingerPrintUser.Username == "" {
		username, err := utils.GenerateRandomNickname()
		if err != nil {
			pkg.Error("生成随机用户名失败", zap.Error(err))
			return nil, exception.ErrUserNameGenFailed
		}
		fingerPrintUser.Username = username
	}

	if fingerPrintUser.Avatar == "" {
		fingerPrintUser.Avatar = utils.GenerateRandomAvatar()
	}

	// 更新用户
	if err := s.userRepo.Update(fingerPrintUser); err != nil {
		pkg.Error("更新用户失败", zap.Uint("userID", fingerPrintUser.ID), zap.Error(err))
		return nil, exception.ErrUserUpdateFailed
	}

	// 如果用户有bd_vid，上报百度转化数据（转化类型为25，表示用户升级）
	if fingerPrintUser.BdVid != "" {
		s.uploadBaiduConversionDataAsync(fingerPrintUser.BdVid, fingerPrintUser.ID, "user_upgrade_phone", 25)
	}

	pkg.Info("指纹用户绑定手机号成功",
		zap.Uint("userID", fingerPrintUser.ID),
		zap.String("fingerPrint", fingerPrint),
		zap.String("phone", phone))
	return fingerPrintUser, nil
}

// createNewUserWithPhone 创建新用户并绑定手机号
func (s *userService) createNewUserWithPhone(phone string) (*models.User, error) {
	pkg.Info("创建新用户并绑定手机号", zap.String("phone", phone))

	// 生成随机用户名，格式为"形容词的名词"
	username, err := utils.GenerateRandomNickname()
	if err != nil {
		pkg.Error("生成随机用户名失败", zap.Error(err))
		return nil, exception.ErrUserNameGenFailed
	}

	// 生成随机头像
	avatar := utils.GenerateRandomAvatar()

	// 创建用户，无需设置密码
	newUser := &models.User{
		Username: username,
		Phone:    phone,
		Avatar:   avatar,
		Status:   enum.UserStatusEnabled,
		UserType: enum.UserTypeRegular, // 设置为普通用户
	}

	if err := s.userRepo.Create(newUser); err != nil {
		pkg.Error("创建用户失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserCreateFailed
	}

	return newUser, nil
}

// createNewUserWithEmail 创建新用户并绑定邮箱
func (s *userService) createNewUserWithEmail(email string) (*models.User, error) {
	pkg.Info("创建新用户并绑定邮箱", zap.String("email", email))

	// 生成随机用户名，格式为"形容词的名词"
	username, err := utils.GenerateRandomNickname()
	if err != nil {
		pkg.Error("生成随机用户名失败", zap.Error(err))
		return nil, exception.ErrUserNameGenFailed
	}

	// 生成随机头像
	avatar := utils.GenerateRandomAvatar()

	// 创建用户，无需设置密码
	newUser := &models.User{
		Username: username,
		Email:    email,
		Avatar:   avatar,
		Status:   enum.UserStatusEnabled,
		UserType: enum.UserTypeRegular, // 设置为普通用户
	}

	if err := s.userRepo.Create(newUser); err != nil {
		pkg.Error("创建用户失败", zap.String("email", email), zap.Error(err))
		return nil, exception.ErrUserCreateFailed
	}

	return newUser, nil
}

// LoginByCodeWithToken 通过手机号和验证码登录并生成token
func (s *userService) LoginByCodeWithToken(ctx context.Context, phone, code string) (*vo.TokenResponse, error) {
	// 先调用LoginByCode方法获取用户
	user, err := s.LoginByCode(ctx, phone, code)
	if err != nil {
		// LoginByCode方法已经记录了错误日志，这里直接返回
		return nil, err
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user.ID)
	if err != nil {
		pkg.Error("生成JWT Token失败", zap.Error(err))
		return nil, exception.ErrTokenGenFailed
	}

	// 返回token响应
	return &vo.TokenResponse{Token: token}, nil
}

// LoginByEmailCode 通过邮箱和验证码登录
func (s *userService) LoginByEmailCode(ctx context.Context, email, code string) (*models.User, error) {
	// 验证邮箱验证码
	valid, err := s.emailCodeService.VerifyEmailCode(ctx, email, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.String("email", email), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	if !valid {
		pkg.Warn("验证码错误或已过期", zap.String("email", email))
		return nil, exception.ErrSMSCodeInvalid
	}

	// 查询邮箱是否存在
	user, err := s.userRepo.GetByEmail(email)
	if err == nil {
		// 如果用户存在，检查用户状态
		if user.Status != enum.UserStatusEnabled {
			pkg.Warn("用户状态异常", zap.String("email", email), zap.String("status", user.Status.String()))
			return nil, exception.ErrUserStatusDisabled
		}

		pkg.Info("用户邮箱验证码登录成功", zap.String("email", email))
		return user, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果是其他错误，直接返回
		pkg.Error("查询用户失败", zap.String("email", email), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	// 到这里说明邮箱不存在，尝试使用指纹查询用户
	// 从上下文中获取指纹
	fingerPrint := ""
	if fp, ok := ctx.Value("fingerPrint").(string); ok && fp != "" {
		fingerPrint = fp
	}

	if fingerPrint == "" {
		// 如果没有指纹，则创建新用户
		return s.createNewUserWithEmail(email)
	}

	// 使用指纹查询用户
	fingerPrintUser, err := s.userRepo.GetByFingerPrint(fingerPrint)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果指纹用户不存在，则创建新用户
			return s.createNewUserWithEmail(email)
		}
		// 其他错误
		pkg.Error("查询指纹用户失败", zap.String("fingerPrint", fingerPrint), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	// 如果指纹用户存在，检查是否已绑定邮箱
	if fingerPrintUser.Email != "" {
		// 如果已绑定邮箱，则创建新用户
		pkg.Info("指纹用户已绑定邮箱，创建新用户",
			zap.String("fingerPrint", fingerPrint),
			zap.String("existingEmail", fingerPrintUser.Email),
			zap.String("newEmail", email))
		return s.createNewUserWithEmail(email)
	}

	// 如果指纹用户是游客用户，则绑定邮箱并升级为普通用户
	fingerPrintUser.Email = email
	if fingerPrintUser.UserType == enum.UserTypeGuest {
		fingerPrintUser.UserType = enum.UserTypeRegular
		pkg.Info("游客用户升级为普通用户",
			zap.Uint("userID", fingerPrintUser.ID),
			zap.String("fingerPrint", fingerPrint),
			zap.String("email", email))
	}

	// 如果指纹用户没有用户名或头像，则生成
	if fingerPrintUser.Username == "" {
		username, err := utils.GenerateRandomNickname()
		if err != nil {
			pkg.Error("生成随机用户名失败", zap.Error(err))
			return nil, exception.ErrUserNameGenFailed
		}
		fingerPrintUser.Username = username
	}

	if fingerPrintUser.Avatar == "" {
		fingerPrintUser.Avatar = utils.GenerateRandomAvatar()
	}

	// 更新用户
	if err := s.userRepo.Update(fingerPrintUser); err != nil {
		pkg.Error("更新用户失败", zap.Error(err))
		return nil, exception.ErrUserUpdateFailed
	}

	// 如果用户有bd_vid，上报百度转化数据（转化类型为25，表示用户升级）
	if fingerPrintUser.BdVid != "" {
		s.uploadBaiduConversionDataAsync(fingerPrintUser.BdVid, fingerPrintUser.ID, "user_upgrade_email", 25)
	}

	pkg.Info("指纹用户绑定邮箱成功",
		zap.Uint("userID", fingerPrintUser.ID),
		zap.String("fingerPrint", fingerPrint),
		zap.String("email", email))

	return fingerPrintUser, nil
}

// LoginByEmailCodeWithToken 通过邮箱和验证码登录并返回token
func (s *userService) LoginByEmailCodeWithToken(ctx context.Context, email, code string) (*vo.TokenResponse, error) {
	// 调用登录方法
	user, err := s.LoginByEmailCode(ctx, email, code)
	if err != nil {
		return nil, err
	}

	// 生成token
	token, err := s.jwtService.GenerateToken(user.ID)
	if err != nil {
		pkg.Error("生成token失败", zap.Error(err))
		return nil, exception.ErrTokenGenFailed
	}

	return &vo.TokenResponse{Token: token}, nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id uint) (*models.User, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		pkg.Error("根据ID获取用户失败", zap.Uint("userId", id), zap.Error(err))
		return nil, err
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		pkg.Warn("用户状态异常", zap.Uint("userId", id), zap.String("status", user.Status.String()))
		return nil, errors.New("用户" + user.Status.String())
	}

	return user, nil
}

// GetUserByPhone 根据手机号获取用户
func (s *userService) GetUserByPhone(phone string) (*models.User, error) {
	return s.userRepo.GetByPhone(phone)
}

// GetUserByEmail 根据邮箱获取用户
func (s *userService) GetUserByEmail(email string) (*models.User, error) {
	return s.userRepo.GetByEmail(email)
}

// UpdateUser 更新用户
func (s *userService) UpdateUser(user *models.User) error {
	// 先获取更新前的用户信息，用于比较
	oldUser, err := s.userRepo.GetByID(user.ID)
	if err != nil {
		return err
	}

	// 更新用户
	err = s.userRepo.Update(user)
	if err != nil {
		return err
	}

	// 检查是否从游客用户升级为普通用户，且有bd_vid
	if oldUser.UserType == enum.UserTypeGuest &&
		user.UserType == enum.UserTypeRegular &&
		user.BdVid != "" {
		s.uploadBaiduConversionDataAsync(user.BdVid, user.ID, "user_upgrade_wechat", 25)
	}

	return nil
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(id uint) error {
	return s.userRepo.Delete(id)
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(page, pageSize int) ([]*models.User, int64, error) {
	return s.userRepo.List(page, pageSize)
}

// LoginByOpenID 通过微信OpenID登录（如果用户不存在则自动创建）
func (s *userService) LoginByOpenID(openID string) (*models.User, error) {
	// 查询用户是否存在
	user, err := s.userRepo.GetByOpenID(openID)
	if err != nil {
		// 如果用户不存在，则创建用户
		if errors.Is(err, gorm.ErrRecordNotFound) {
			pkg.Info("用户不存在，自动创建用户", zap.String("openID", openID))

			// 生成随机用户名，格式为"形容词的名词"
			username, err := utils.GenerateRandomNickname()
			if err != nil {
				pkg.Error("生成随机用户名失败", zap.Error(err))
				return nil, errors.New("创建用户失败")
			}

			// 生成随机头像
			avatar := utils.GenerateRandomAvatar()

			// 创建用户，无需设置密码
			newUser := &models.User{
				Username: username,
				OpenID:   openID,
				Avatar:   avatar,
				Status:   enum.UserStatusEnabled,
				UserType: enum.UserTypeRegular, // 设置为普通用户
			}

			if err := s.userRepo.Create(newUser); err != nil {
				pkg.Error("创建用户失败", zap.String("openID", openID), zap.Error(err))
				return nil, errors.New("创建用户失败")
			}

			return newUser, nil
		}

		pkg.Error("查询用户失败", zap.String("openID", openID), zap.Error(err))
		return nil, err
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		pkg.Warn("用户状态异常", zap.String("openID", openID), zap.String("status", user.Status.String()))
		return nil, errors.New("用户" + user.Status.String())
	}

	pkg.Info("用户OpenID登录成功", zap.String("openID", openID), zap.Uint("userId", user.ID))
	return user, nil
}

// UpdateAvatar 更新用户头像
func (s *userService) UpdateAvatar(userID uint, avatarURL string) error {
	// 验证头像URL格式
	if err := s.validateAvatarURL(avatarURL); err != nil {
		return err
	}

	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 更新头像
	if err := s.userRepo.UpdateAvatar(userID, avatarURL); err != nil {
		pkg.Error("更新用户头像失败", zap.Uint("userId", userID), zap.String("avatarURL", avatarURL), zap.Error(err))
		return exception.ErrAvatarUpdateFailed
	}

	pkg.Info("头像更新成功", zap.Uint("userId", userID), zap.String("oldAvatar", user.Avatar), zap.String("newAvatar", avatarURL))
	return nil
}

// validateAvatarURL 验证头像URL格式
func (s *userService) validateAvatarURL(avatarURL string) error {
	// 去除首尾空格
	avatarURL = strings.TrimSpace(avatarURL)

	// 检查是否为空
	if avatarURL == "" {
		return exception.ErrAvatarInvalid.WithMessage("头像URL不能为空")
	}

	// 检查URL格式（这里可以添加更严格的URL验证）
	// 例如：检查是否是有效的HTTP/HTTPS URL，是否是允许的域名等
	if !strings.HasPrefix(avatarURL, "http://") && !strings.HasPrefix(avatarURL, "https://") {
		return exception.ErrAvatarInvalid.WithMessage("头像URL必须是有效的HTTP或HTTPS地址")
	}

	return nil
}

// UpdateAvatarWithFile 通过文件上传更新用户头像
func (s *userService) UpdateAvatarWithFile(ctx context.Context, userID uint, file *multipart.FileHeader) error {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 调用上传服务上传头像到OSS
	avatarURL, _, err := s.uploadService.UploadAvatar(ctx, file)
	if err != nil {
		pkg.Error("上传头像失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrAvatarUploadFailed.WithDetail(err.Error())
	}

	// 更新用户头像URL
	if err := s.userRepo.UpdateAvatar(userID, avatarURL); err != nil {
		pkg.Error("更新用户头像失败", zap.Uint("userId", userID), zap.String("avatarURL", avatarURL), zap.Error(err))
		return exception.ErrAvatarUpdateFailed
	}

	pkg.Info("用户头像修改成功", zap.Uint("userId", userID), zap.String("oldAvatar", user.Avatar), zap.String("newAvatar", avatarURL))
	return nil
}

// UpdateUsername 更新用户名
func (s *userService) UpdateUsername(userID uint, username string) error {
	// 验证用户名格式
	if err := s.validateUsername(username); err != nil {
		return err
	}

	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 更新用户名
	if err := s.userRepo.UpdateUsername(userID, username); err != nil {
		pkg.Error("更新用户名失败", zap.Uint("userId", userID), zap.String("username", username), zap.Error(err))
		return exception.ErrUsernameUpdateFailed
	}

	pkg.Info("用户名更新成功", zap.Uint("userId", userID), zap.String("oldUsername", user.Username), zap.String("newUsername", username))
	return nil
}

// validateUsername 验证用户名格式
func (s *userService) validateUsername(username string) error {
	// 去除首尾空格
	username = strings.TrimSpace(username)

	// 检查长度
	if len(username) < 2 {
		return exception.ErrUsernameTooShort
	}
	if len(username) > 20 {
		return exception.ErrUsernameTooLong
	}

	// 检查是否为空或只包含空格
	if username == "" {
		return exception.ErrUsernameInvalid
	}

	// 可以在这里添加更多的用户名格式验证规则
	// 例如：不能包含特殊字符、不能是纯数字等

	return nil
}

// BindPhone 绑定手机号
func (s *userService) BindPhone(ctx context.Context, userID uint, phone, code string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return err
	}

	// 检查手机号是否已被其他用户使用
	existingUser, err := s.GetUserByPhone(phone)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		pkg.Error("检查手机号是否已被使用失败", zap.Error(err))
		return exception.ErrInternalServer
	}
	if existingUser != nil {
		return exception.ErrInvalidParam.WithMessage("该手机号已被其他用户使用")
	}

	// 验证验证码
	valid, err := s.codeService.VerifySMSCode(ctx, phone, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.Error(err))
		return err
	}

	if !valid {
		return exception.ErrInvalidParam.WithMessage("验证码错误或已过期")
	}

	// 更新手机号
	user.Phone = phone
	if err := s.userRepo.Update(user); err != nil {
		return exception.ErrInternalServer.WithDetail(err.Error())
	}

	return nil
}

// BindWechat 绑定微信
func (s *userService) BindWechat(ctx context.Context, userID uint, openID string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return err
	}

	// 检查用户是否已经绑定了微信
	if user.OpenID != "" {
		return exception.ErrInvalidParam.WithMessage("该用户已绑定微信账号")
	}

	// 检查OpenID是否已被其他用户使用
	existingUser, err := s.userRepo.GetByOpenID(openID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		pkg.Error("检查OpenID是否已被使用失败", zap.Error(err))
		return exception.ErrInternalServer
	}
	if existingUser != nil {
		return exception.ErrInvalidParam.WithMessage("该微信账号已被其他用户绑定")
	}

	// 更新用户的OpenID
	user.OpenID = openID
	if err := s.userRepo.Update(user); err != nil {
		return exception.ErrInternalServer.WithDetail(err.Error())
	}

	pkg.Info("用户绑定微信成功", zap.Uint("userID", userID), zap.String("openID", openID))
	return nil
}

// CheckBindWechatQrCodeStatus 检查绑定微信二维码状态
func (s *userService) CheckBindWechatQrCodeStatus(ctx context.Context, userID uint, sceneId string) (*vo.QrCodeStatusResponse, error) {
	// 构建Redis键
	redisKey := "qrcode:bind:" + sceneId

	// 获取Redis中存储的二维码信息
	qrCodeInfoStr, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 二维码不存在或已过期
			return &vo.QrCodeStatusResponse{
				Status: "EXPIRED",
			}, nil
		}
		pkg.Error("获取绑定微信二维码信息失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeExpired
	}

	// 解析二维码信息
	var qrCodeInfo QrCodeInfo
	if err := json.Unmarshal([]byte(qrCodeInfoStr), &qrCodeInfo); err != nil {
		pkg.Error("解析绑定微信二维码信息失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeExpired
	}

	// 检查二维码是否属于当前用户
	if qrCodeInfo.UserID != userID {
		pkg.Warn("二维码不属于当前用户",
			zap.String("sceneId", sceneId),
			zap.Uint("qrCodeUserID", qrCodeInfo.UserID),
			zap.Uint("currentUserID", userID),
		)
		return nil, exception.ErrInvalidParam.WithMessage("无效的二维码")
	}

	// 构建基础响应
	statusResponse := &vo.QrCodeStatusResponse{
		Status: string(qrCodeInfo.Status),
	}

	// 如果状态为已扫码，执行绑定操作
	if qrCodeInfo.Status == QrCodeStatusScanned && qrCodeInfo.OpenID != "" {
		// 调用绑定微信方法
		err := s.BindWechat(ctx, userID, qrCodeInfo.OpenID)
		if err != nil {
			pkg.Error("绑定微信失败",
				zap.String("sceneId", sceneId),
				zap.Uint("userID", userID),
				zap.String("openID", qrCodeInfo.OpenID),
				zap.Error(err),
			)
			// 绑定失败，返回错误给前端
			return nil, err
		}

		pkg.Info("微信绑定成功",
			zap.String("sceneId", sceneId),
			zap.Uint("userID", userID),
			zap.String("openID", qrCodeInfo.OpenID),
		)

		// 绑定成功后，删除Redis中的二维码信息
		s.redisClient.Del(ctx, redisKey)
	}

	return statusResponse, nil
}

// Logout 用户退出登录
func (s *userService) Logout(ctx context.Context, user *models.User, token string) error {
	// 解析token以获取过期时间
	claims, err := s.jwtService.ParseToken(token)
	if err != nil {
		pkg.Error("解析token失败", zap.Error(err))
		return exception.ErrTokenInvalid
	}

	// 计算token剩余有效期
	expiration := time.Until(s.jwtService.GetExpirationTime(claims))
	if expiration < 0 {
		expiration = time.Hour // 如果token已过期，设置一个默认的过期时间
	}

	// 将token加入黑名单
	err = s.redisClient.Set(ctx, "black:"+token, user.ID, expiration).Err()
	if err != nil {
		pkg.Error("添加token到黑名单失败", zap.Error(err))
		return exception.ErrInternalServer
	}

	pkg.Info("用户退出登录", zap.Uint("userId", user.ID))
	return nil
}

// BindEmail 绑定邮箱
func (s *userService) BindEmail(ctx context.Context, userID uint, email, code string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return err
	}

	// 检查邮箱是否已被其他用户使用
	existingUser, err := s.GetUserByEmail(email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		pkg.Error("检查邮箱是否已被使用失败", zap.Error(err))
		return exception.ErrInternalServer
	}
	if existingUser != nil {
		return exception.ErrInvalidParam.WithMessage("该邮箱已被其他用户使用")
	}

	// 验证验证码
	valid, err := s.emailCodeService.VerifyEmailCode(ctx, email, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.Error(err))
		return err
	}

	if !valid {
		return exception.ErrInvalidParam.WithMessage("验证码错误或已过期")
	}

	// 更新邮箱
	user.Email = email
	if err := s.userRepo.Update(user); err != nil {
		return exception.ErrInternalServer.WithDetail(err.Error())
	}

	return nil
}

// uploadBaiduConversionDataAsync 异步上传百度转化数据
func (s *userService) uploadBaiduConversionDataAsync(bdVid string, userID uint, userType string, newType int) {
	if bdVid == "" {
		return
	}

	go func() {
		ctx := context.Background()
		err := utils.UploadBaiduConversionData(ctx, bdVid, s.config.FrontendDomain, newType, s.logger)
		if err != nil {
			s.logger.Error("上传百度转化数据失败",
				zap.String("bd_vid", bdVid),
				zap.Uint("user_id", userID),
				zap.String("type", userType),
				zap.Int("new_type", newType),
				zap.Error(err))
		} else {
			s.logger.Info("成功上传百度转化数据",
				zap.String("bd_vid", bdVid),
				zap.Uint("user_id", userID),
				zap.String("type", userType),
				zap.Int("new_type", newType))
		}
	}()
}
