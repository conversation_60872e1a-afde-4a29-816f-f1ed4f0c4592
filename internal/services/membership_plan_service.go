package services

import (
	"context"

	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// MembershipPlanService 会员套餐服务接口
type MembershipPlanService interface {
	// GetVisiblePlans 获取所有可见的会员套餐
	GetVisiblePlans(ctx context.Context) (*vo.MembershipPlanListResponse, error)
	// GetVisibleDownloadCouponPlans 获取所有可见的下载券套餐
	GetVisibleDownloadCouponPlans(ctx context.Context) (*vo.DownloadCouponPlanListResponse, error)
}

// membershipPlanService 会员套餐服务实现
type membershipPlanService struct {
	planRepo    repository.MembershipPlanRepository
	redisClient *redis.Client
	logger      *zap.Logger
}

// NewMembershipPlanService 创建会员套餐服务
func NewMembershipPlanService(
	planRepo repository.MembershipPlanRepository,
	redisClient *redis.Client,
	logger *zap.Logger,
) MembershipPlanService {
	return &membershipPlanService{
		planRepo:    planRepo,
		redisClient: redisClient,
		logger:      logger,
	}
}

// GetVisiblePlans 获取所有可见的会员套餐
func (s *membershipPlanService) GetVisiblePlans(ctx context.Context) (*vo.MembershipPlanListResponse, error) {
	// 使用缓存包装，缓存30分钟（1800秒）
	return utils.CacheFunc2(utils.CacheKeyVisibleMembershipPlans, func(...any) (*vo.MembershipPlanListResponse, error) {
		// 从仓库获取所有可见的会员套餐
		plans, err := s.planRepo.GetVisiblePlans(ctx)
		if err != nil {
			s.logger.Error("获取会员套餐失败", zap.Error(err))
			return nil, exception.ErrMembershipPlanQueryFailed.WithDetail(err.Error())
		}

		// 转换为响应对象
		planResponses := make([]vo.MembershipPlanResponse, 0, len(plans))
		for _, plan := range plans {
			// 转换为响应对象
			planResponse := vo.ConvertToMembershipPlanResponse(plan, "")
			planResponses = append(planResponses, planResponse)
		}

		// 构建响应
		response := &vo.MembershipPlanListResponse{
			List: planResponses,
		}

		s.logger.Info("获取会员套餐成功", zap.Int("count", len(planResponses)))
		return response, nil
	}, 1800, s.redisClient)
}

// GetVisibleDownloadCouponPlans 获取所有可见的下载券套餐
func (s *membershipPlanService) GetVisibleDownloadCouponPlans(ctx context.Context) (*vo.DownloadCouponPlanListResponse, error) {
	// 使用缓存包装，缓存30分钟（1800秒）
	return utils.CacheFunc2(utils.CacheKeyVisibleDownloadCouponPlans, func(...any) (*vo.DownloadCouponPlanListResponse, error) {
		// 从仓库获取所有可见的下载券套餐
		plans, err := s.planRepo.GetVisibleDownloadCouponPlans(ctx)
		if err != nil {
			s.logger.Error("获取下载券套餐失败", zap.Error(err))
			return nil, exception.ErrMembershipPlanQueryFailed.WithDetail(err.Error())
		}

		// 转换为响应对象
		planResponses := make([]vo.DownloadCouponPlanResponse, 0, len(plans))
		for _, plan := range plans {
			// 转换为响应对象
			planResponse := vo.ConvertToDownloadCouponPlanResponse(plan)
			planResponses = append(planResponses, planResponse)
		}

		// 构建响应
		response := &vo.DownloadCouponPlanListResponse{
			List: planResponses,
		}

		s.logger.Info("获取下载券套餐成功", zap.Int("count", len(planResponses)))
		return response, nil
	}, 1800, s.redisClient)
}
