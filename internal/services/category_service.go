package services

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/utils"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// CategoryService 分类服务接口
type CategoryService interface {
	GetAllCategoriesGrouped() (vo.CategoryListResponse, error)
	GetCategoryBySlug(slug string) (*vo.CategoryDetailResponse, error)
	GetCategoryTdkBySlug(slug string) (*vo.CategoryTdkResponse, error)
	GetExamplesBySlug(slug, page, pageSize string) (*vo.PaginatedList[vo.ExampleListItemResponse], error)
}

// categoryService 分类服务实现
type categoryService struct {
	db           *gorm.DB
	positionRepo repository.PositionRepository
	exampleRepo  repository.ExampleRepository
	redisClient  *redis.Client
}

// NewCategoryService 创建分类服务实例
func NewCategoryService(db *gorm.DB, positionRepo repository.PositionRepository, exampleRepo repository.ExampleRepository, redisClient *redis.Client) CategoryService {
	return &categoryService{
		db:           db,
		positionRepo: positionRepo,
		exampleRepo:  exampleRepo,
		redisClient:  redisClient,
	}
}

// GetAllCategoriesGrouped 获取所有分类数据并按类型分组
func (s *categoryService) GetAllCategoriesGrouped() (vo.CategoryListResponse, error) {
	// 使用缓存包装，永不过期（ttl设为0表示永不过期）
	return utils.CacheFunc2(utils.CacheKeyAllCategoriesGrouped, func(...any) (vo.CategoryListResponse, error) {
		var categories []models.Category

		// 查询所有分类数据，按分类类型和排序字段排序，只查is_show为true的
		err := s.db.Where("is_show = ?", true).Order("category_type ASC, sort ASC").Find(&categories).Error
		if err != nil {
			return nil, err
		}

		// 按分类类型分组
		groupMap := make(map[enum.CategoryType][]models.Category)
		for _, category := range categories {
			groupMap[category.CategoryType] = append(groupMap[category.CategoryType], category)
		}

		// 构建响应数据
		var result vo.CategoryListResponse

		// 按照枚举顺序添加分组
		categoryTypes := []enum.CategoryType{
			enum.CategoryTypeHotTemplate,
			enum.CategoryTypeUniversityMajor,
			enum.CategoryTypeDesignStyle,
		}

		for i, categoryType := range categoryTypes {
			if items, exists := groupMap[categoryType]; exists && len(items) > 0 {
				group := vo.CategoryGroupResponse{
					Name:         categoryType.String(),
					CategoryType: int(categoryType),
					Children:     make([]vo.CategoryItemResponse, 0, len(items)),
				}

				// 转换分类项
				for _, item := range items {
					categoryItem := vo.CategoryItemResponse{
						ID:     item.ID,
						Name:   item.CategoryName,
						SlugCn: item.SlugCn,
						SlugEn: item.SlugEn,
					}
					group.Children = append(group.Children, categoryItem)
				}

				result = append(result, group)
			}

			// 特殊处理：在第一个分组后添加职位信息分组
			if i == 0 {
				positionGroup, err := s.getPositionGroup()
				if err != nil {
					return nil, err
				}
				if positionGroup != nil {
					result = append(result, *positionGroup)
				}
			}
		}

		return result, nil
	}, 0, s.redisClient)
}

// getPositionGroup 获取行业职位分组
func (s *categoryService) getPositionGroup() (*vo.CategoryGroupResponse, error) {
	// 查询职位表的一级数据（parent_id = 0）
	var positions []*models.Position
	err := s.db.Where("parent_id = ?", 0).Order("sort ASC, id ASC").Find(&positions).Error
	if err != nil {
		return nil, err
	}

	// 如果没有一级职位数据，返回nil
	if len(positions) == 0 {
		return nil, nil
	}

	// 构建行业职位分组
	group := &vo.CategoryGroupResponse{
		Name:         "行业职位",
		CategoryType: int(enum.CategoryTypePosition),
		Children:     make([]vo.CategoryItemResponse, 0, len(positions)),
	}

	// 转换职位数据为分类项格式
	for _, position := range positions {
		categoryItem := vo.CategoryItemResponse{
			ID:     position.ID,
			Name:   position.PositionName,
			SlugCn: position.SlugCn,
			SlugEn: position.SlugEn,
		}
		group.Children = append(group.Children, categoryItem)
	}

	return group, nil
}

// GetCategoryBySlug 根据slug查看分类详情
func (s *categoryService) GetCategoryBySlug(slug string) (*vo.CategoryDetailResponse, error) {
	// 使用缓存包装，永不过期（ttl设为0表示永不过期）
	// 缓存键包含slug参数，确保不同slug有不同的缓存
	cacheKey := fmt.Sprintf("%s:%s", utils.CacheKeyCategoryBySlug, slug)
	return utils.CacheFunc2(cacheKey, func(...any) (*vo.CategoryDetailResponse, error) {
		// 先查询分类表
		if response, found, err := s.findCategoryBySlug(slug); err != nil {
			return nil, err
		} else if found {
			return response, nil
		}

		// 分类表中没找到，查询职位表
		if response, found, err := s.findPositionBySlug(slug); err != nil {
			return nil, err
		} else if found {
			return response, nil
		}

		// 都没找到，返回空数据
		// 获取更多推荐
		moreRecommendations, err := s.getMoreRecommendations()
		if err != nil {
			return nil, err
		}

		response := &vo.CategoryDetailResponse{
			CategoryType: 0, // 未知类型
			SelectData: vo.CategoryItemWithChildrenResponse{
				ID:       0,
				Name:     "",
				SlugCn:   "",
				SlugEn:   "",
				Children: []vo.CategoryItemWithChildrenResponse{},
			},
			HasData:             false,               // 没有查到数据
			MoreRecommendations: moreRecommendations, // 更多推荐
			Description:         "",                  // 空描述
		}
		return response, nil
	}, 0, s.redisClient)
}

// GetCategoryTdkBySlug 根据slug获取分类TDK信息
func (s *categoryService) GetCategoryTdkBySlug(slug string) (*vo.CategoryTdkResponse, error) {
	// 先查询分类表
	var category models.Category
	err := s.db.Where("slug_cn = ?", slug).First(&category).Error
	if err == nil {
		// 找到分类，返回分类的TDK信息
		return &vo.CategoryTdkResponse{
			ID:          category.ID,
			Title:       category.SeoTitle,
			Description: category.SeoDescription,
			Keywords:    category.SeoKeywords,
		}, nil
	}

	if err != gorm.ErrRecordNotFound {
		// 数据库错误
		return nil, exception.ErrCategoryTdkQueryFailed
	}

	// 分类表中没找到，查询职位表
	var position models.Position
	err = s.db.Where("slug_cn = ?", slug).First(&position).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exception.ErrCategoryNotFound
		}
		return nil, exception.ErrCategoryTdkQueryFailed
	}

	// 找到职位，返回职位的TDK信息
	return &vo.CategoryTdkResponse{
		ID:          position.ID,
		Title:       position.SeoTitle,
		Description: position.SeoDescription,
		Keywords:    position.SeoKeywords,
	}, nil
}

// findCategoryBySlug 在分类表中查找指定slug的分类
func (s *categoryService) findCategoryBySlug(slug string) (*vo.CategoryDetailResponse, bool, error) {
	// 特殊处理：如果slug为"all"，直接返回所有example数据
	if slug == "all" {
		examples, err := s.getAllExamples()
		if err != nil {
			return nil, false, err
		}

		// 获取CategoryType为1的随机标签
		tags, err := s.getRandomCategoryTags(enum.CategoryTypeHotTemplate)
		if err != nil {
			return nil, false, err
		}

		// 获取更多推荐
		moreRecommendations, err := s.getMoreRecommendations()
		if err != nil {
			return nil, false, err
		}

		response := &vo.CategoryDetailResponse{
			CategoryType: 0, // 特殊类型，表示所有示例
			SelectData: vo.CategoryItemWithChildrenResponse{
				ID:       0,
				Name:     "所有示例",
				SlugCn:   "all",
				SlugEn:   "all",
				Children: []vo.CategoryItemWithChildrenResponse{}, // 空数组
			},
			HasData:             true, // 有数据
			Examples:            examples,
			Tags:                tags,
			MoreRecommendations: moreRecommendations,                                                                                                                          // 更多推荐
			Description:         "熊猫简历PandaResume专业智能的简历在线制作工具，依托前沿 AI 技术，一键优简历化容、专业简历模板免费使用，结合海量高颜值简历模板，仅需5分钟即可生成契合岗位需求的精美专业求职简历！精准匹配招聘方的岗位要求，助您在激烈的求职竞争中脱颖而出，轻松斩获高薪 offer！", // 固定描述
		}
		return response, true, nil
	}

	var category models.Category
	err := s.db.Where("slug_cn = ?", slug).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, nil // 没找到，不是错误
		}
		return nil, false, err // 数据库错误
	}

	// 构建基础响应数据
	response := &vo.CategoryDetailResponse{
		CategoryType: int(category.CategoryType),
		SelectData: vo.CategoryItemWithChildrenResponse{
			ID:       category.ID,
			Name:     category.CategoryName,
			SlugCn:   category.SlugCn,
			SlugEn:   category.SlugEn,
			Children: []vo.CategoryItemWithChildrenResponse{}, // 空数组
		},
		HasData: true, // 查到了分类数据
	}

	// 根据分类类型获取对应的示例数据和标签
	var tags []vo.CategoryTagResponse

	if category.CategoryType == enum.CategoryTypeUniversityMajor {
		// 大学专业类型（CategoryType = 2），查询example表的major_id
		examples, err := s.getExamplesByMajorID(category.ID)
		if err != nil {
			return nil, false, err
		}
		response.Examples = examples

		// 获取CategoryType为2的随机标签
		tags, err = s.getRandomCategoryTags(enum.CategoryTypeUniversityMajor)
		if err != nil {
			return nil, false, err
		}
	} else if category.CategoryType == enum.CategoryTypeDesignStyle {
		// 设计风格类型（CategoryType = 3），先查template表的style_id字段，再查example表
		examples, err := s.getExamplesByStyleID(category.ID)
		if err != nil {
			return nil, false, err
		}
		response.Examples = examples

		// 调用getRandomPositionTags获取职位标签
		tags, err = s.getRandomPositionTags()
		if err != nil {
			return nil, false, err
		}
	} else if category.CategoryType == enum.CategoryTypeHotTemplate {
		// 热门模板类型（CategoryType = 1），根据category的is_internship字段查询example的随机120个数据
		examples, err := s.getExamplesByHotTemplateCategory(category)
		if err != nil {
			return nil, false, err
		}
		response.Examples = examples

		tags, err = s.getRandomCategoryTags(enum.CategoryTypeHotTemplate)
		if err != nil {
			return nil, false, err
		}
	} else {
		// 其他类型返回空示例列表和空标签
		emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, 1, 40)
		response.Examples = &emptyList
		tags = []vo.CategoryTagResponse{}
	}

	// 获取更多推荐
	moreRecommendations, err := s.getMoreRecommendations()
	if err != nil {
		return nil, false, err
	}

	// 根据分类类型设置描述
	var description string
	if category.CategoryType == enum.CategoryTypeHotTemplate {
		// CategoryType = 1，使用分类名称替换XXX
		description = fmt.Sprintf("熊猫简历PandaResume专业智能的%s、简历在线制作工具，依托前沿 AI 技术，一键优化容、%s，结合海量高颜值%s，仅需5分钟即可生成契合岗位需求的精美%s！精准匹配招聘方的岗位要求，助您在激烈的求职竞争中脱颖而出，轻松斩获高薪 offer！",
			category.CategoryName, category.CategoryName, category.CategoryName, category.CategoryName)
	} else if category.CategoryType == enum.CategoryTypeUniversityMajor {
		// CategoryType = 2，使用大学专业模板
		description = fmt.Sprintf("熊猫简历PandaResume专业智能的大学%s专业简历在线制作工具，依托前沿 AI 技术，一键优%s化容、%s专业简历模板，结合海量高颜值大学%s专业简历模板，仅需5分钟即可生成契合岗位需求的精美%s专业简历！精准匹配招聘方的岗位要求，助您在激烈的求职竞争中脱颖而出，轻松斩获高薪 offer！",
			category.CategoryName, category.CategoryName, category.CategoryName, category.CategoryName, category.CategoryName)
	} else if category.CategoryType == enum.CategoryTypeDesignStyle {
		// CategoryType = 3，使用设计风格模板
		description = fmt.Sprintf("熊猫简历PandaResume专业智能的%s风格简历在线制作工具，依托前沿 AI 技术，一键优%s化容、%s风格简历模板，结合海量高颜值%s风格简历模板，仅需5分钟即可生成契合岗位需求的精美%s风格简历！精准匹配招聘方的岗位要求，助您在激烈的求职竞争中脱颖而出，轻松斩获高薪 offer！",
			category.CategoryName, category.CategoryName, category.CategoryName, category.CategoryName, category.CategoryName)
	} else {
		// 其他类型暂时使用空描述
		description = ""
	}

	response.Tags = tags
	response.MoreRecommendations = moreRecommendations
	response.Description = description
	return response, true, nil
}

// findPositionBySlug 在职位表中查找指定slug的职位
func (s *categoryService) findPositionBySlug(slug string) (*vo.CategoryDetailResponse, bool, error) {
	var position models.Position
	err := s.db.Where("slug_cn = ?", slug).First(&position).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, nil // 没找到，不是错误
		}
		return nil, false, err // 数据库错误
	}

	// 找到职位，构建完整的层级结构
	hierarchy, err := s.buildPositionHierarchy(position.ID)
	if err != nil {
		return nil, false, err
	}

	examples, err := s.getExamplesByPositionLevel(position.ID, position.Level)
	if err != nil {
		return nil, false, err
	}

	// 获取tags数据：从职位表中level为3随机取30个
	tags, err := s.getRandomPositionTags()
	if err != nil {
		return nil, false, err
	}

	// 获取更多推荐
	moreRecommendations, err := s.getMoreRecommendations()
	if err != nil {
		return nil, false, err
	}

	// 构建描述文本，将XXX替换为职位名称
	description := fmt.Sprintf("熊猫简历PandaResume专业智能的%s简历在线制作工具，依托前沿 AI 技术，一键优%s化容、%s简历模板，结合海量高颜值%s简历模板，仅需5分钟即可生成契合岗位需求的精美%s简历！精准匹配招聘方的%s岗位要求，助您在激烈的求职竞争中脱颖而出，轻松斩获高薪 offer！",
		position.PositionName, position.PositionName, position.PositionName, position.PositionName, position.PositionName, position.PositionName)

	response := &vo.CategoryDetailResponse{
		CategoryType:        int(enum.CategoryTypePosition),
		SelectData:          *hierarchy,
		HasData:             true, // 查到了职位数据
		Examples:            examples,
		Tags:                tags,
		MoreRecommendations: moreRecommendations, // 更多推荐
		Description:         description,         // 描述
	}

	return response, true, nil
}

// buildPositionHierarchy 构建职位的完整层级结构
func (s *categoryService) buildPositionHierarchy(positionID uint) (*vo.CategoryItemWithChildrenResponse, error) {
	// 获取当前职位信息
	var position models.Position
	err := s.db.First(&position, positionID).Error
	if err != nil {
		return nil, err
	}

	// 找到一级职位ID
	var rootID uint
	if position.ParentID == 0 {
		// 当前就是一级职位
		rootID = position.ID
	} else {
		// 从path字段解析一级职位ID
		// path格式："1,2,3" 其中1是一级职位ID
		pathParts := strings.Split(position.Path, ",")
		if len(pathParts) >= 1 {
			rootIDStr := pathParts[0]
			if id, err := strconv.ParseUint(rootIDStr, 10, 32); err == nil {
				rootID = uint(id)
			}
		}
	}

	// 查询一级职位
	var rootPosition models.Position
	err = s.db.First(&rootPosition, rootID).Error
	if err != nil {
		return nil, err
	}

	// 获取根职位下的所有子职位
	var allPositions []*models.Position
	err = s.db.Where("path LIKE ? OR path LIKE ? OR parent_id = ?",
		fmt.Sprintf("%d,%%", rootID), fmt.Sprintf("%%,%d,%%", rootID), rootID).
		Order("sort ASC, id ASC").Find(&allPositions).Error
	if err != nil {
		return nil, err
	}

	// 构建层级结构
	hierarchy := s.buildPositionHierarchyRecursive(rootPosition, allPositions)
	return hierarchy, nil
}

// buildPositionHierarchyRecursive 递归构建职位层级结构
func (s *categoryService) buildPositionHierarchyRecursive(rootPosition models.Position, allPositions []*models.Position) *vo.CategoryItemWithChildrenResponse {
	// 创建根节点
	root := &vo.CategoryItemWithChildrenResponse{
		ID:       rootPosition.ID,
		Name:     rootPosition.PositionName,
		SlugCn:   rootPosition.SlugCn,
		SlugEn:   rootPosition.SlugEn,
		Children: []vo.CategoryItemWithChildrenResponse{},
	}

	// 创建ID到职位的映射
	positionMap := make(map[uint]*models.Position)
	for _, pos := range allPositions {
		positionMap[pos.ID] = pos
	}

	// 创建父级ID到子级列表的映射
	childrenMap := make(map[uint][]vo.CategoryItemWithChildrenResponse)

	// 遍历所有职位，构建子级映射
	for _, pos := range allPositions {
		item := vo.CategoryItemWithChildrenResponse{
			ID:     pos.ID,
			Name:   pos.PositionName,
			SlugCn: pos.SlugCn,
			SlugEn: pos.SlugEn,
		}

		childrenMap[pos.ParentID] = append(childrenMap[pos.ParentID], item)
	}

	// 递归设置子级
	var setChildren func(items []vo.CategoryItemWithChildrenResponse) []vo.CategoryItemWithChildrenResponse
	setChildren = func(items []vo.CategoryItemWithChildrenResponse) []vo.CategoryItemWithChildrenResponse {
		for i := range items {
			if children, exists := childrenMap[items[i].ID]; exists {
				items[i].Children = setChildren(children)
			}
		}
		return items
	}

	// 设置根节点的子级
	if children, exists := childrenMap[rootPosition.ID]; exists {
		root.Children = setChildren(children)
	}

	return root
}

// getExamplesByPositionLevel 根据职位ID和level字段查询example数据
func (s *categoryService) getExamplesByPositionLevel(positionID uint, level int) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 根据level字段确定查询条件
	var query *gorm.DB
	switch level {
	case 1:
		// 一级分类：查询 first_category_id
		query = s.db.Where("first_category_id = ?", positionID)
	case 2:
		// 二级分类：查询 second_category_id
		query = s.db.Where("second_category_id = ?", positionID)
	case 3:
		// 三级分类：查询 third_category_id
		query = s.db.Where("third_category_id = ?", positionID)
	default:
		// 无效的level，返回空结果
		emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, 1, 40)
		return &emptyList, nil
	}

	// 统计总数
	err := query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 分页查询，第一页40条数据
	err = query.Order("sort ASC").Limit(40).Offset(0).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有模板ID，用于批量查询模板信息
	templateIDs := make([]uint, 0, len(examples))
	for _, example := range examples {
		templateIDs = append(templateIDs, example.TemplateID)
	}

	// 批量查询模板信息
	var templates []models.Template
	templateMap := make(map[uint]models.Template)
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}

		// 构建模板ID到模板的映射
		for _, template := range templates {
			templateMap[template.ID] = template
		}
	}

	// 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount, // 直接从example表获取使用人数
			Name:            example.Name,       // 直接从example表获取名称
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, 1, 40)
	return &response, nil
}

// GetExamplesBySlug 根据slug获取示例列表
func (s *categoryService) GetExamplesBySlug(slug, page, pageSize string) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	// 解析分页参数
	pageNum, err := strconv.Atoi(page)
	if err != nil || pageNum < 1 {
		pageNum = 1
	}

	pageSizeNum, err := strconv.Atoi(pageSize)
	if err != nil || pageSizeNum < 1 || pageSizeNum > 100 {
		pageSizeNum = 40
	}

	// 先查询分类表
	if response, found, err := s.findCategoryExamplesBySlug(slug, pageNum, pageSizeNum); err != nil {
		return nil, err
	} else if found {
		return response, nil
	}

	// 分类表中没找到，查询职位表
	if response, found, err := s.findPositionExamplesBySlug(slug, pageNum, pageSizeNum); err != nil {
		return nil, err
	} else if found {
		return response, nil
	}

	// 都没找到，返回空数据
	emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, pageNum, pageSizeNum)
	return &emptyList, nil
}

// findCategoryExamplesBySlug 在分类表中查找指定slug的分类并返回示例列表
func (s *categoryService) findCategoryExamplesBySlug(slug string, page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], bool, error) {
	// 特殊处理：如果slug为"all"，直接返回所有example数据（支持分页）
	if slug == "all" {
		examples, err := s.getAllExamplesWithPagination(page, pageSize)
		if err != nil {
			return nil, false, err
		}
		return examples, true, nil
	}

	var category models.Category
	err := s.db.Where("slug_cn = ?", slug).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, nil // 没找到，不是错误
		}
		return nil, false, err // 数据库错误
	}

	// 如果是大学专业类型（CategoryType = 2），查询example表的major_id
	if category.CategoryType == enum.CategoryTypeUniversityMajor {
		examples, err := s.getExamplesByMajorIDWithPagination(category.ID, page, pageSize)
		if err != nil {
			return nil, false, err
		}
		return examples, true, nil
	} else if category.CategoryType == enum.CategoryTypeDesignStyle {
		// 如果是设计风格类型（CategoryType = 3），先查template表的style_id字段，再查example表
		examples, err := s.getExamplesByStyleIDWithPagination(category.ID, page, pageSize)
		if err != nil {
			return nil, false, err
		}
		return examples, true, nil
	}

	// 其他类型返回空示例列表
	emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, page, pageSize)
	return &emptyList, true, nil
}

// findPositionExamplesBySlug 在职位表中查找指定slug的职位并返回示例列表
func (s *categoryService) findPositionExamplesBySlug(slug string, page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], bool, error) {
	var position models.Position
	err := s.db.Where("slug_cn = ?", slug).First(&position).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, nil // 没找到，不是错误
		}
		return nil, false, err // 数据库错误
	}

	// 打印position信息
	fmt.Printf("Position: ID=%d, Name=%s, Level=%d, ParentID=%d, Path=%s\n",
		position.ID, position.PositionName, position.Level, position.ParentID, position.Path)

	// 根据职位的level字段查询example数据
	examples, err := s.getExamplesByPositionLevelWithPagination(position.ID, position.Level, page, pageSize)
	if err != nil {
		return nil, false, err
	}

	return examples, true, nil
}

// getExamplesByPositionLevelWithPagination 根据职位ID和level字段查询example数据（支持分页）
func (s *categoryService) getExamplesByPositionLevelWithPagination(positionID uint, level, page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 根据level字段确定查询条件
	var query *gorm.DB
	switch level {
	case 1:
		// 一级分类：查询 first_category_id
		query = s.db.Where("first_category_id = ?", positionID)
	case 2:
		// 二级分类：查询 second_category_id
		query = s.db.Where("second_category_id = ?", positionID)
	case 3:
		// 三级分类：查询 third_category_id
		query = s.db.Where("third_category_id = ?", positionID)
	default:
		// 无效的level，返回空结果
		emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, page, pageSize)
		return &emptyList, nil
	}

	// 统计总数
	err := query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 分页查询
	err = query.Order("sort ASC").Limit(pageSize).Offset(offset).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有模板ID，用于批量查询模板信息
	templateIDs := make([]uint, 0, len(examples))
	for _, example := range examples {
		templateIDs = append(templateIDs, example.TemplateID)
	}

	// 批量查询模板信息
	var templates []models.Template
	templateMap := make(map[uint]models.Template)
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}

		// 构建模板ID到模板的映射
		for _, template := range templates {
			templateMap[template.ID] = template
		}
	}

	// 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount, // 直接从example表获取使用人数
			Name:            example.Name,       // 直接从example表获取名称
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, page, pageSize)
	return &response, nil
}

// getExamplesByMajorID 根据专业ID查询example表的major_id字段
func (s *categoryService) getExamplesByMajorID(majorID uint) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 查询example表中major_id等于指定ID的记录
	query := s.db.Where("major_id = ?", majorID)

	// 统计总数
	err := query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 查询数据（默认分页参数：第1页，每页40条）
	err = query.Order("sort ASC").Limit(40).Offset(0).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有相关的模板信息
	var templateIDs []uint
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 查询模板信息
	var templates []models.Template
	templateMap := make(map[uint]models.Template)
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}

		for _, template := range templates {
			templateMap[template.ID] = template
		}
	}

	// 构建响应数据
	var list []vo.ExampleListItemResponse
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, 1, 40)
	return &response, nil
}

// getExamplesByMajorIDWithPagination 根据专业ID查询example表的major_id字段（支持分页）
func (s *categoryService) getExamplesByMajorIDWithPagination(majorID uint, page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 查询example表中major_id等于指定ID的记录
	query := s.db.Where("major_id = ?", majorID)

	// 统计总数
	err := query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询数据（支持分页）
	err = query.Order("sort ASC").Limit(pageSize).Offset(offset).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有相关的模板信息
	var templateIDs []uint
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 查询模板信息
	var templates []models.Template
	templateMap := make(map[uint]models.Template)
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}

		for _, template := range templates {
			templateMap[template.ID] = template
		}
	}

	// 构建响应数据
	var list []vo.ExampleListItemResponse
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, page, pageSize)
	return &response, nil
}

// getExamplesByStyleID 根据设计风格ID查询example表
func (s *categoryService) getExamplesByStyleID(styleID uint) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	// 1. 先查询template表，找到style_id字段包含指定styleID的模板
	var templates []models.Template
	err := s.db.Where("JSON_CONTAINS(style_id, ?)", fmt.Sprintf("[%d]", styleID)).Find(&templates).Error
	if err != nil {
		return nil, err
	}

	// 如果没有找到相关模板，返回空结果
	if len(templates) == 0 {
		emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, 1, 40)
		return &emptyList, nil
	}

	// 2. 提取所有模板ID
	templateIDs := make([]uint, 0, len(templates))
	for _, template := range templates {
		templateIDs = append(templateIDs, template.ID)
	}

	// 3. 查询example表中template_id在这些模板ID中的记录
	var examples []models.Example
	var total int64

	query := s.db.Where("template_id IN ?", templateIDs)

	// 统计总数
	err = query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 查询数据（默认分页参数：第1页，每页40条）
	err = query.Order("sort ASC").Limit(40).Offset(0).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 4. 构建模板ID到模板的映射，用于获取tags
	templateMap := make(map[uint]models.Template)
	for _, template := range templates {
		templateMap[template.ID] = template
	}

	// 5. 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, 1, 40)
	return &response, nil
}

// getExamplesByStyleIDWithPagination 根据设计风格ID查询example表（支持分页）
func (s *categoryService) getExamplesByStyleIDWithPagination(styleID uint, page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	// 1. 先查询template表，找到style_id字段包含指定styleID的模板
	var templates []models.Template
	err := s.db.Where("JSON_CONTAINS(style_id, ?)", fmt.Sprintf("[%d]", styleID)).Find(&templates).Error
	if err != nil {
		return nil, err
	}

	// 如果没有找到相关模板，返回空结果
	if len(templates) == 0 {
		emptyList := vo.NewPaginatedList([]vo.ExampleListItemResponse{}, 0, page, pageSize)
		return &emptyList, nil
	}

	// 2. 提取所有模板ID
	templateIDs := make([]uint, 0, len(templates))
	for _, template := range templates {
		templateIDs = append(templateIDs, template.ID)
	}

	// 3. 查询example表中template_id在这些模板ID中的记录
	var examples []models.Example
	var total int64

	query := s.db.Where("template_id IN ?", templateIDs)

	// 统计总数
	err = query.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 分页查询
	err = query.Order("sort ASC").Limit(pageSize).Offset(offset).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 4. 构建模板ID到模板的映射，用于获取tags
	templateMap := make(map[uint]models.Template)
	for _, template := range templates {
		templateMap[template.ID] = template
	}

	// 5. 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, page, pageSize)
	return &response, nil
}

// getAllExamples 获取所有example数据（默认分页）
func (s *categoryService) getAllExamples() (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 统计总数
	err := s.db.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 查询数据（默认分页参数：第1页，每页40条）
	err = s.db.Order("sort ASC").Limit(40).Offset(0).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有相关的模板信息
	var templateIDs []uint
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 查询模板信息
	var templates []models.Template
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建模板ID到模板的映射
	templateMap := make(map[uint]models.Template)
	for _, template := range templates {
		templateMap[template.ID] = template
	}

	// 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, 1, 40)
	return &response, nil
}

// getAllExamplesWithPagination 获取所有example数据（支持分页）
func (s *categoryService) getAllExamplesWithPagination(page, pageSize int) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	var examples []models.Example
	var total int64

	// 统计总数
	err := s.db.Model(&models.Example{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 分页查询
	err = s.db.Order("sort ASC").Limit(pageSize).Offset(offset).Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// 获取所有相关的模板信息
	var templateIDs []uint
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 查询模板信息
	var templates []models.Template
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建模板ID到模板的映射
	templateMap := make(map[uint]models.Template)
	for _, template := range templates {
		templateMap[template.ID] = template
	}

	// 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		}

		list = append(list, item)
	}

	response := vo.NewPaginatedList(list, total, page, pageSize)
	return &response, nil
}

// getRandomPositionTags 获取随机的职位标签（level为3的职位，随机取30个）
func (s *categoryService) getRandomPositionTags() ([]vo.CategoryTagResponse, error) {
	// 从职位表中获取level为3的职位，随机取30个
	positions, err := s.positionRepo.GetRandomPositionsByLevel(context.TODO(), 3, 30)
	if err != nil {
		return nil, err
	}

	// 转换为CategoryTagResponse格式
	var tags []vo.CategoryTagResponse
	for _, position := range positions {
		tag := vo.CategoryTagResponse{
			Name: position.PositionName,
			URL:  fmt.Sprintf("/jianli/%s/", position.SlugCn),
		}
		tags = append(tags, tag)
	}

	return tags, nil
}

// getMoreRecommendations 获取更多推荐（随机20个示例）
func (s *categoryService) getMoreRecommendations() ([]vo.ExampleListItemResponse, error) {
	// 从example表中随机查询20个记录
	examples, err := s.exampleRepo.GetRandomExamples(context.Background(), 20)
	if err != nil {
		return nil, err
	}

	// 收集所有模板ID
	templateIDs := make([]uint, 0, len(examples))
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 查询模板信息
	var templates []models.Template
	templateMap := make(map[uint]models.Template)
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}

		for _, template := range templates {
			templateMap[template.ID] = template
		}
	}

	// 构建响应数据
	list := make([]vo.ExampleListItemResponse, 0, len(examples))
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount,
			Name:            example.Name,
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		} else {
			item.Tags = []string{}
		}

		list = append(list, item)
	}

	return list, nil
}

// getRandomCategoryTags 获取随机的分类标签
func (s *categoryService) getRandomCategoryTags(categoryType enum.CategoryType) ([]vo.CategoryTagResponse, error) {
	var categories []models.Category

	// 从分类表中获取指定类型的分类，随机取30个
	err := s.db.Where("category_type = ?", categoryType).
		Order("RAND()").
		Limit(30).
		Find(&categories).Error
	if err != nil {
		return nil, err
	}

	// 转换为CategoryTagResponse格式
	var tags []vo.CategoryTagResponse
	for _, category := range categories {
		tag := vo.CategoryTagResponse{
			Name: category.CategoryName,
			URL:  fmt.Sprintf("/jianli/%s/", category.SlugCn),
		}
		tags = append(tags, tag)
	}

	return tags, nil
}

// getExamplesByHotTemplateCategory 根据热门模板分类获取example数据
func (s *categoryService) getExamplesByHotTemplateCategory(category models.Category) (*vo.PaginatedList[vo.ExampleListItemResponse], error) {
	// 根据category的is_internship字段查询example的随机120个数据
	examples, err := s.exampleRepo.GetRandomExamplesByInternship(context.Background(), category.IsInternship, 120)
	if err != nil {
		return nil, err
	}

	// 获取所有相关的模板信息
	var templateIDs []uint
	for _, example := range examples {
		if example.TemplateID > 0 {
			templateIDs = append(templateIDs, example.TemplateID)
		}
	}

	// 批量查询模板信息
	var templates []models.Template
	if len(templateIDs) > 0 {
		err = s.db.Where("id IN ?", templateIDs).Find(&templates).Error
		if err != nil {
			return nil, err
		}
	}

	// 创建模板ID到模板的映射
	templateMap := make(map[uint]models.Template)
	for _, template := range templates {
		templateMap[template.ID] = template
	}

	// 转换为ExampleListItemResponse格式
	var list []vo.ExampleListItemResponse
	for _, example := range examples {
		item := vo.ExampleListItemResponse{
			ID:              example.ID,
			PreviewImageUrl: example.PreviewImageUrl,
			TemplateID:      example.TemplateID,
			UsageCount:      example.UsageCount, // 直接从example表获取使用人数
			Name:            example.Name,       // 直接从example表获取名称
		}

		// 从关联的模板中获取tags
		if template, exists := templateMap[example.TemplateID]; exists {
			item.Tags = template.Tags
		} else {
			item.Tags = []string{}
		}

		list = append(list, item)
	}

	// 强制设置分页为第1页，每页120条，总共120条数据
	response := vo.NewPaginatedList(list, int64(len(list)), 1, 120)
	return &response, nil
}
