package middleware

import (
	"bytes"
	"io"
	"net/http"
	"reflect"
	"strings"

	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
)

// isEmptyStruct 检查是否为空结构体
func isEmptyStruct(t reflect.Type) bool {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Kind() == reflect.Struct && t.NumField() == 0
}

// hasURIFields 检查结构体是否包含URI标签的字段
func hasURIFields(t reflect.Type) bool {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() != reflect.Struct {
		return false
	}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		if uriTag := field.Tag.Get("uri"); uriTag != "" {
			return true
		}
	}
	return false
}

// AutoBindAndValidate 自动绑定和验证中间件
// 此中间件根据控制器方法的签名自动验证第二个参数（DTO）
func AutoBindAndValidate(handler any) gin.HandlerFunc {
	handlerValue := reflect.ValueOf(handler)
	handlerType := handlerValue.Type()

	// 检查handler是否是函数
	if handlerType.Kind() != reflect.Func {
		panic("handler必须是函数")
	}

	// 检查函数类型，确保至少有两个参数：context和DTO
	if handlerType.NumIn() < 2 {
		panic("handler必须至少有两个参数：*gin.Context和DTO")
	}

	// 检查第一个参数是否是*gin.Context
	contextType := handlerType.In(0)
	if contextType != reflect.TypeOf((*gin.Context)(nil)) {
		panic("handler的第一个参数必须是*gin.Context")
	}

	// 获取第二个参数类型（DTO类型）
	dtoType := handlerType.In(1)

	// 检查是否为空结构体
	isEmptyDTO := isEmptyStruct(dtoType)

	return func(c *gin.Context) {
		// 创建一个DTO类型的新实例
		dtoValue := reflect.New(dtoType)
		dto := dtoValue.Interface()

		// 如果不是空结构体，则进行绑定和验证
		if !isEmptyDTO {
			// 根据请求类型选择不同的绑定方法
			var bindErr error
			method := c.Request.Method

			// 对于需要读取请求体的方法，先保存原始请求体
			// 注意：DELETE方法也可能有请求体
			var bodyBytes []byte
			// 检查是否有请求体
			hasBody := method == http.MethodPost || method == http.MethodPut || method == http.MethodPatch
			// DELETE请求可能有也可能没有请求体，需要检查Content-Length
			if method == http.MethodDelete {
				contentLength := c.Request.ContentLength
				hasBody = contentLength > 0
			}

			if hasBody {
				bodyBytes, _ = io.ReadAll(c.Request.Body)
				// 恢复请求体供绑定使用
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}

			// 根据 Content-Type 选择合适的绑定方法
			contentType := c.GetHeader("Content-Type")

			// 检查DTO是否包含URI字段和路径中是否包含URI参数
			dtoHasURIFields := hasURIFields(dtoType)
			hasURIParams := strings.Contains(c.Request.URL.Path, ":")

			var uriErr error
			// 只有当DTO包含URI字段时才进行URI绑定
			if dtoHasURIFields {
				uriErr = c.ShouldBindUri(dto)

				// 如果路径中包含URI参数但绑定失败，直接返回错误
				if hasURIParams && uriErr != nil {
					pkg.Warn("路径参数验证失败", zap.Error(uriErr), zap.String("path", c.Request.URL.Path))
					// 翻译验证错误
					errorMessages := pkg.TranslateURIError(uriErr)
					// 使用异常机制抛出验证错误
					response.ThrowError(c, exception.ErrValidationFailed.WithDetailsMap(errorMessages))
					return
				}
			}

			// 根据请求方法和内容类型选择合适的绑定方法
			switch {
			case method == http.MethodGet:
				// GET 请求使用查询参数
				bindErr = c.ShouldBindQuery(dto)
			case method == http.MethodDelete:
				// DELETE 请求优先使用URI参数绑定，如果URI绑定成功，则不需要其他绑定
				if dtoHasURIFields && uriErr == nil && hasURIParams {
					// URI绑定成功，不需要其他绑定
					bindErr = nil
				} else if hasBody {
					// 如果有请求体，根据Content-Type处理
					if contentType == "" || strings.Contains(contentType, "application/x-www-form-urlencoded") {
						// 如果没有Content-Type或者是表单，先尝试查询参数
						bindErr = c.ShouldBindQuery(dto)
					} else if strings.Contains(contentType, "application/json") {
						// 如果是JSON，则使用JSON绑定
						bindErr = c.ShouldBindJSON(dto)
					} else {
						// 其他情况使用通用绑定
						bindErr = c.ShouldBind(dto)
					}
				} else {
					// 如果没有请求体，尝试查询参数
					bindErr = c.ShouldBindQuery(dto)
				}
			case strings.Contains(contentType, "multipart/form-data"):
				// 处理 multipart/form-data 请求（文件上传）
				bindErr = c.ShouldBindWith(dto, binding.FormMultipart)
			case strings.Contains(contentType, "application/x-www-form-urlencoded"):
				// 处理 form 表单提交
				bindErr = c.ShouldBindWith(dto, binding.Form)
			case strings.Contains(contentType, "application/json"):
				// 处理 JSON 请求
				bindErr = c.ShouldBindJSON(dto)
			default:
				// 其他情况使用通用绑定
				bindErr = c.ShouldBind(dto)
			}

			// 如果URI绑定成功但其他绑定失败，使用其他绑定的错误
			// 如果URI绑定失败但其他绑定成功，使用其他绑定的结果
			// 如果两者都失败，使用更有意义的错误
			if dtoHasURIFields && bindErr != nil && uriErr != nil {
				// 如果路径中有URI参数，优先使用URI绑定错误
				if strings.Contains(c.Request.URL.Path, ":") && strings.Contains(uriErr.Error(), "uri:") {
					bindErr = uriErr
				}
			} else if dtoHasURIFields && uriErr == nil {
				// URI绑定成功，其他绑定失败或成功，保留URI绑定的结果
				// 不需要额外处理，因为URI绑定已经将值设置到dto中
			}

			// 如果保存了请求体，需要再次恢复供后续处理使用
			if hasBody {
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}

			// 如果绑定失败
			if bindErr != nil {
				pkg.Warn("请求参数验证失败", zap.Error(bindErr))
				// 翻译验证错误
				errorMessages := pkg.Translate(bindErr)
				// 使用异常机制抛出验证错误
				response.ThrowError(c, exception.ErrValidationFailed.WithDetailsMap(errorMessages))
				return
			}
		}

		// 准备handler的参数
		args := make([]reflect.Value, handlerType.NumIn())
		args[0] = reflect.ValueOf(c)
		args[1] = dtoValue.Elem() // 使用解引用后的值

		// 如果有更多参数，使用默认零值填充
		for i := 2; i < handlerType.NumIn(); i++ {
			args[i] = reflect.New(handlerType.In(i)).Elem()
		}

		// 调用原始handler
		handlerValue.Call(args)
	}
}

// 控制器方法类型，用于简化方法签名定义
type ControllerMethod[T any] func(*gin.Context, T)

// WithValidation 为控制器方法添加自动验证
// 用法：router.POST("/register", middleware.WithValidation(controller.Register))
func WithValidation[T any](method ControllerMethod[T]) gin.HandlerFunc {
	return AutoBindAndValidate(method)
}

// 不需要DTO验证的控制器方法类型
type SimpleControllerMethod func(*gin.Context)

// WithoutValidation 包装不需要验证的控制器方法
// 用法：router.GET("/health", middleware.WithoutValidation(controller.HealthCheck))
func WithoutValidation(method SimpleControllerMethod) gin.HandlerFunc {
	return func(c *gin.Context) {
		method(c)
	}
}
