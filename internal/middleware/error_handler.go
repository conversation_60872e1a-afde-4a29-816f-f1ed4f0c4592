package middleware

import (
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ErrorHandler 错误处理中间件
// 应放在路由中间件链的最前面，以捕获后续处理中产生的错误
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 在请求处理函数之前执行的代码

		// 调用下一个中间件或处理函数
		c.Next()

		// 在请求处理函数之后执行的代码
		// 检查是否有错误
		if len(c.Errors) > 0 {
			// 只处理第一个错误（通常只有一个）
			err := c.Errors.Last().Err

			// 判断错误是否为ApiError类型
			if apiErr, ok := err.(*exception.ApiError); ok {
				// 对于ApiError，使用定义的HTTP状态码和业务码
				pkg.Warn("应用错误",
					zap.Int("http_code", apiErr.HttpCode),
					zap.Int("business_code", apiErr.Code),
					zap.String("message", apiErr.Message))

				// 响应错误消息（详细信息已经包含在Message中）
				response.ErrorJSON(c, apiErr.HttpCode, apiErr.Code, apiErr.Message)
			} else {
				// 对于其它类型的错误，默认为内部服务器错误
				pkg.Error("未捕获的错误", zap.Error(err))
				response.ServerError(c, "服务器内部错误")
			}

			// 已处理错误，不再向上传递
			c.Abort()
		}
	}
}
