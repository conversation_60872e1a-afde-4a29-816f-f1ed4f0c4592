package middleware

import (
	"github.com/gin-gonic/gin"
)

// CORSMiddleware 跨域中间件
// 允许来自指定源的跨域请求
func CORSMiddleware() gin.HandlerFunc {
	// 允许的域名列表
	allowedOrigins := []string{
		"http://127.0.0.1:3000",
		"http://127.0.0.1:3002",
		"http://**************:3000",
		"http://127.0.0.1:3001",
		"http://localhost:3000",
		"http://localhost:3002",
		"http://localhost:3001",
		"http://***************:3000",
		"https://resume.avrilko.com",
		"http://resume-front:3000",
	}

	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 检查请求的源是否在允许列表中
		var allowedOrigin string
		for _, allowed := range allowedOrigins {
			if origin == allowed {
				allowedOrigin = origin
				break
			}
		}

		// 只有当找到匹配的源时，才设置 CORS 头
		if allowedOrigin != "" {
			c.Writer.Header().Set("Access-Control-Allow-Origin", allowedOrigin)
			// 允许的HTTP方法
			c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			// 允许的请求头
			c.Writer.Header().Set("Access-Control-Allow-Headers", "Cache-Control, Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Fingerprint, X-Bd-Vid, X-Channel")
			// 允许暴露的响应头
			c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length")
			// 允许携带凭证（如Cookie）
			c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		}

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
