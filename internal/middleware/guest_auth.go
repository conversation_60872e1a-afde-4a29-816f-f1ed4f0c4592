package middleware

import (
	"strings"

	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// GuestAuthMiddleware 游客认证中间件
// 先尝试JWT认证，如果失败则尝试浏览器指纹认证
// 如果没有浏览器指纹，则报错
// 如果有浏览器指纹，则尝试识别或创建游客用户
func GuestAuthMiddleware(jwtService pkg.JWTService, userService services.UserService, userRepo repository.UserRepository, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先尝试JWT认证
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			// 检查Bearer前缀
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				// 验证令牌
				token := parts[1]

				// 检查token是否在黑名单中
				exists, err := redisClient.Exists(c, "black:"+token).Result()
				if err == nil && exists == 0 {
					claims, err := jwtService.ParseToken(token)
					if err == nil && !jwtService.IsExpired(claims) {
						// 获取用户ID
						userID := jwtService.GetUserID(claims)

						// 查询数据库获取用户信息
						user, err := userService.GetUserByID(userID)
						if err == nil {
							// 将用户ID和用户模型保存到上下文中
							c.Set("userId", userID)
							c.Set("user", user)
							// 设置登录状态为true，表示JWT认证成功
							c.Set("isLoggedIn", true)
							pkg.Debug("JWT认证成功", zap.Uint("userId", userID))
							c.Next()
							return
						}
					}
				}
			}
		}

		// JWT认证失败，尝试浏览器指纹认证
		fingerPrint := c.GetHeader("X-Fingerprint")
		if fingerPrint == "" {
			// 没有指纹，报错
			pkg.Warn("请求中没有浏览器指纹")
			response.ThrowError(c, exception.New(400, 40001, "缺少浏览器指纹"))
			c.Abort()
			return
		}

		// 获取百度投放ID
		bdVid := c.GetHeader("X-Bd-Vid")
		// 获取渠道信息
		channel := c.GetHeader("X-Channel")

		// 根据指纹查找或创建游客用户
		user, err := userRepo.FirstOrCreateByFingerPrint(fingerPrint, bdVid, channel)
		if err != nil {
			pkg.Error("根据指纹查找或创建游客用户失败", zap.String("fingerPrint", fingerPrint), zap.String("bdVid", bdVid), zap.String("channel", channel), zap.Error(err))
			// 报错
			response.ThrowError(c, exception.ErrInternalServer.WithMessage("游客认证失败"))
			c.Abort()
			return
		}

		// 将用户ID和用户模型保存到上下文中
		c.Set("userId", user.ID)
		c.Set("user", user)
		// 设置登录状态为false，表示指纹认证
		c.Set("isLoggedIn", false)
		pkg.Debug("游客用户已识别", zap.Uint("userId", user.ID), zap.String("fingerPrint", fingerPrint))
		c.Next()
	}
}
