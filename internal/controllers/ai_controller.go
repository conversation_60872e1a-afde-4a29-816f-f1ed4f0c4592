package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AIController AI控制器
type AIController struct {
	aiService                   services.AIService
	membershipValidationService services.MembershipValidationService
	logger                      *zap.Logger
}

// NewAIController 创建AI控制器
func NewAIController(
	aiService services.AIService,
	membershipValidationService services.MembershipValidationService,
	logger *zap.Logger,
) *AIController {
	return &AIController{
		aiService:                   aiService,
		membershipValidationService: membershipValidationService,
		logger:                      logger,
	}
}

// Prompt AI提示词接口
// @Summary AI提示词处理
// @Description 根据简历模块和提示词类型，使用AI生成相应的内容，支持流式传输
// @Tags AI助手
// @Accept json
// @Produce text/plain
// @Param request body dto.PromptRequest true "AI提示词请求"
// @Success 200 {string} string "AI生成的内容（流式传输）"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/prompt [post]
func (c *AIController) Prompt(ctx *gin.Context, req dto.PromptRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层处理AI请求
	streamResp, err := c.aiService.ProcessPrompt(ctx, user.ID, req)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}
	defer streamResp.Close()

	// 设置SSE响应头
	ctx.Header("Content-Type", "text/event-stream")
	ctx.Header("Cache-Control", "no-cache")
	ctx.Header("Connection", "keep-alive")
	ctx.Header("Access-Control-Allow-Headers", "Cache-Control")
	ctx.Header("X-Accel-Buffering", "no") // 禁用nginx缓冲

	// 获取响应写入器
	writer := ctx.Writer
	flusher, ok := writer.(http.Flusher)
	if !ok {
		c.logger.Error("响应写入器不支持流式传输")
		response.ThrowError(ctx, exception.ErrInternalServer.WithDetail("服务器不支持流式传输"))
		return
	}

	// 处理流式响应
	ctx.Status(http.StatusOK)

	for chunk := range streamResp.Stream {
		// 检查是否有错误
		if chunk.Error != nil {
			c.logger.Error("AI流式响应错误", zap.Error(chunk.Error))
			break
		}

		// 检查是否完成
		if chunk.Done {
			writer.Write([]byte("data: [DONE]\n\n"))
			flusher.Flush()
			break
		}

		// 使用OpenAI API兼容格式
		if chunk.Content != "" {
			// 构建OpenAI格式的响应
			openaiResponse := map[string]interface{}{
				"id": chunk.RequestID,
				"choices": []map[string]interface{}{
					{
						"index": 0,
						"delta": map[string]interface{}{
							"content": chunk.Content,
						},
						"finish_reason": nil,
					},
				},
			}

			responseData, _ := json.Marshal(openaiResponse)
			writer.Write([]byte(fmt.Sprintf("data: %s\n\n", responseData)))
			flusher.Flush()
		}
	}
}

// GetAICallRecords 获取AI调用记录列表
// @Summary 获取AI调用记录列表
// @Description 根据简历ID分页获取AI调用记录列表，返回枚举的字符串值
// @Tags AI助手
// @Accept json
// @Produce json
// @Param resume_id query uint true "简历ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} response.Response[any] "AI调用记录列表"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/records [get]
func (c *AIController) GetAICallRecords(ctx *gin.Context, req dto.AICallRecordListRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取AI调用记录（业务逻辑已封装在service层）
	pageResponse, err := c.aiService.GetAICallRecords(ctx.Request.Context(), user.ID, req.ResumeID, req.Page, req.PageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	ctx.JSON(200, response.Success("获取AI调用记录成功", pageResponse))
}

// ParseFile 文件解析接口
// @Summary 文件解析
// @Description 上传文件并解析内容，支持pdf、txt、csv、docx、doc、xlsx、xls、pptx、ppt、md、mobi、epub格式，最大10MB
// @Tags AI助手
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "要解析的文件"
// @Success 200 {object} response.Response[vo.ParseFileResponse] "解析成功"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 413 {object} response.Response[any] "文件过大"
// @Failure 415 {object} response.Response[any] "不支持的文件类型"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/parse-file [post]
func (c *AIController) ParseFile(ctx *gin.Context, req dto.ParseFileRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层解析文件
	parseResponse, err := c.aiService.ParseFile(ctx.Request.Context(), user.ID, req.File)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	ctx.JSON(200, response.Success("文件解析成功", parseResponse))
}

// GenerateResume AI生成简历接口
// @Summary AI生成简历
// @Description 根据用户提供的话术，使用AI生成完整的简历数据，包括基本信息、教育经历、工作经历、项目经历和个人总结
// @Tags AI助手
// @Accept json
// @Produce json
// @Param request body dto.GenerateResumeRequest true "AI生成简历请求"
// @Success 200 {object} response.Response[dto.GenerateResumeResponse] "生成成功"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/generate-resume [post]
func (c *AIController) GenerateResume(ctx *gin.Context, req dto.GenerateResumeRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 从请求头中获取JWT Token
	authHeader := ctx.GetHeader("Authorization")
	var jwtToken string
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			jwtToken = parts[1]
		}
	}

	// 从请求头中获取浏览器指纹
	fingerprint := ctx.GetHeader("X-Fingerprint")

	// 调用服务层生成简历
	generateResponse, err := c.aiService.GenerateResume(ctx, user.ID, req, jwtToken, fingerprint)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	ctx.JSON(200, response.Success("AI生成简历成功", generateResponse))
}

// OptimizeResume AI优化简历接口
// @Summary AI优化简历
// @Description 根据简历ID，使用AI优化简历内容，将优化后的内容保存到简历草稿表
// @Tags AI助手
// @Accept json
// @Produce json
// @Param request body dto.OptimizeResumeRequest true "AI优化简历请求"
// @Success 200 {object} response.Response[dto.OptimizeResumeResponse] "优化成功"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/optimize-resume [post]
func (c *AIController) OptimizeResume(ctx *gin.Context, req dto.OptimizeResumeRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 1. 校验用户权益
	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	validationResult, err := c.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), user.ID, isLoggedInBool, enum.PrivilegeAIOptimize)
	if err != nil {
		c.logger.Error("AI优化权益校验失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("resume_id", req.ResumeID),
			zap.Error(err))
		response.ThrowError(ctx, exception.ErrInternalServer.WithDetail("权益校验失败"))
		return
	}

	if !validationResult.Allowed {
		c.logger.Warn("用户权益不足",
			zap.Uint("user_id", user.ID),
			zap.Uint("resume_id", req.ResumeID),
			zap.String("reason", validationResult.Reason))
		response.ThrowError(ctx, exception.ErrForbidden.WithDetail(validationResult.Reason))
		return
	}

	// 2. 调用服务层优化简历
	optimizeResponse, err := c.aiService.OptimizeResume(ctx.Request.Context(), user.ID, req.ResumeID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	ctx.JSON(200, response.Success("AI优化简历成功", optimizeResponse))
}

// ScoreResume AI简历打分接口
// @Summary AI简历打分
// @Description 根据简历ID和目标岗位ID，使用AI对简历进行四维度打分评估：语言与表达、信息完整性、内容相关性、简历专业性。每个维度包含总分和详细评价项。
// @Tags AI助手
// @Accept json
// @Produce json
// @Param request body dto.ScoreResumeRequest true "AI简历打分请求"
// @Success 200 {object} response.Response[vo.ScoreResumeResponse] "打分成功，返回四维度评分结果"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 403 {object} response.Response[any] "权益不足"
// @Failure 404 {object} response.Response[any] "简历或目标岗位不存在"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/score-resume [post]
func (c *AIController) ScoreResume(ctx *gin.Context, req dto.ScoreResumeRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 1. 校验用户权益
	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	// 使用AI简历打分权益类型进行校验
	validationResult, err := c.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), user.ID, isLoggedInBool, enum.PrivilegeAIDiagnose)
	if err != nil {
		c.logger.Error("AI简历打分权益校验失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("resume_id", req.ResumeID),
			zap.Uint("position_id", req.PositionID),
			zap.Error(err))
		response.ThrowError(ctx, exception.ErrInternalServer.WithDetail("权益校验失败"))
		return
	}

	if !validationResult.Allowed {
		c.logger.Warn("用户权益不足",
			zap.Uint("user_id", user.ID),
			zap.Uint("resume_id", req.ResumeID),
			zap.Uint("position_id", req.PositionID),
			zap.String("reason", validationResult.Reason))
		response.ThrowError(ctx, exception.ErrForbidden.WithDetail(validationResult.Reason))
		return
	}

	// 调用服务层进行简历打分
	scoreResponse, err := c.aiService.ScoreResume(ctx.Request.Context(), user.ID, req.ResumeID, req.PositionID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	c.logger.Info("AI简历打分完成",
		zap.Uint("user_id", user.ID),
		zap.Uint("resume_id", req.ResumeID),
		zap.Uint("position_id", req.PositionID),
		zap.Float64("overall_score", scoreResponse.OverallScore))

	// 返回打分结果
	ctx.JSON(200, response.Success("AI简历打分完成", scoreResponse))
}

// BatchValidatePrivilege 批量权限校验接口
// @Summary 批量权限校验
// @Description 批量校验用户是否可以使用多项权益，返回权限校验结果、弹窗类型、弹窗标题和描述文案
// @Tags AI助手
// @Accept json
// @Produce json
// @Param request body dto.BatchValidatePrivilegeRequest true "批量权限校验请求"
// @Success 200 {object} response.Response[vo.BatchValidatePrivilegeResponse] "校验成功"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /ai/batch-validate-privilege [post]
func (c *AIController) BatchValidatePrivilege(ctx *gin.Context, req dto.BatchValidatePrivilegeRequest) {
	// 从上下文中获取用户信息
	user := ctx.MustGet("user").(*models.User)

	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	// 初始化响应结构
	batchResponse := &vo.BatchValidatePrivilegeResponse{
		AllAllowed: true,
	}

	// 逐个校验权限，只要有一个不通过就结束循环
	for _, privilegeType := range req.PrivilegeTypes {
		// 校验单个权限
		validationResult, err := c.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), user.ID, isLoggedInBool, privilegeType)
		if err != nil {
			c.logger.Error("权限校验失败",
				zap.Uint("user_id", user.ID),
				zap.String("privilege_type", privilegeType.String()),
				zap.Error(err))
			response.ThrowError(ctx, exception.ErrInternalServer.WithDetail("权限校验失败"))
			return
		}

		// 如果权限不通过，设置弹窗信息并结束循环
		if !validationResult.Allowed {
			batchResponse.AllAllowed = false
			c.setModalInfo(batchResponse, isLoggedInBool, privilegeType)
			break
		}
	}

	// 返回成功响应
	ctx.JSON(200, response.Success("批量权限校验完成", batchResponse))
}

// setModalInfo 设置弹窗信息
func (c *AIController) setModalInfo(batchResponse *vo.BatchValidatePrivilegeResponse, isLoggedIn bool, privilegeType enum.PrivilegeType) {
	// 获取权益名称
	privilegeName := privilegeType.String()

	if !isLoggedIn {
		// 未登录用户显示登录弹窗
		batchResponse.ModalType = enum.ModalTypeLogin
		batchResponse.ModalTitle = "登录提示"
		batchResponse.ModalDescription = fmt.Sprintf("「%s」功能需要登录后使用，登录后可享受更多权益", privilegeName)
	} else {
		// 已登录用户显示会员付费弹窗
		batchResponse.ModalType = enum.ModalTypeMembership
		batchResponse.ModalTitle = "升级会员"

		// 针对简历创建数量限制的特殊文案
		if privilegeType == enum.PrivilegeResumeCreate {
			batchResponse.ModalDescription = "您的简历创建数量已达上限，可以升级会员创建更多简历，或前往我的简历删除不需要的简历后继续使用"
		} else {
			batchResponse.ModalDescription = fmt.Sprintf("「%s」功能使用次数已达上限，升级会员可享受更多权益和无限制使用", privilegeName)
		}
	}
}
