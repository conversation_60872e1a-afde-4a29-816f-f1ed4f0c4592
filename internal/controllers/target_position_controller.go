package controllers

import (
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TargetPositionController 目标岗位控制器
type TargetPositionController struct {
	targetPositionService services.TargetPositionService
	resumeScoreService    services.ResumeScoreService
	logger                *zap.Logger
}

// NewTargetPositionController 创建目标岗位控制器
func NewTargetPositionController(
	targetPositionService services.TargetPositionService,
	resumeScoreService services.ResumeScoreService,
	logger *zap.Logger,
) *TargetPositionController {
	return &TargetPositionController{
		targetPositionService: targetPositionService,
		resumeScoreService:    resumeScoreService,
		logger:                logger,
	}
}

// CreateTargetPosition 创建目标岗位
// @Summary 创建目标岗位
// @Description 用户创建新的目标岗位信息
// @Tags 目标岗位管理
// @Accept json
// @Produce json
// @Param request body dto.CreateTargetPositionRequest true "创建目标岗位请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.CreateTargetPositionResponse} "创建成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /target-positions [post]
// @Security BearerAuth
func (c *TargetPositionController) CreateTargetPosition(ctx *gin.Context, req *dto.CreateTargetPositionRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层创建目标岗位
	targetPosition, err := c.targetPositionService.CreateTargetPosition(ctx.Request.Context(), user.ID, req)
	if err != nil {
		c.logger.Error("创建目标岗位失败",
			zap.Uint("user_id", user.ID),
			zap.String("position_name", req.PositionName),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 构造响应数据
	responseData := vo.CreateTargetPositionResponse{
		ID: targetPosition.ID,
	}

	response.SuccessJSON(ctx, "创建成功", responseData)
}

// UpdateTargetPosition 更新目标岗位
// @Summary 更新目标岗位
// @Description 用户更新已有的目标岗位信息
// @Tags 目标岗位管理
// @Accept json
// @Produce json
// @Param id path int true "岗位ID" example(1)
// @Param request body dto.UpdateTargetPositionRequest true "更新目标岗位请求"
// @Success 200 {object} vo.SuccessAPIResponse "更新成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "目标岗位不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /target-positions/{id} [put]
// @Security BearerAuth
func (c *TargetPositionController) UpdateTargetPosition(ctx *gin.Context, req *dto.UpdateTargetPositionRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层更新目标岗位
	err := c.targetPositionService.UpdateTargetPosition(ctx.Request.Context(), user.ID, req.ID, req)
	if err != nil {
		c.logger.Error("更新目标岗位失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("position_id", req.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "更新成功")
}

// DeleteTargetPosition 删除目标岗位
// @Summary 删除目标岗位
// @Description 用户删除已有的目标岗位信息
// @Tags 目标岗位管理
// @Accept json
// @Produce json
// @Param id path int true "岗位ID" example(1)
// @Success 200 {object} vo.SuccessAPIResponse "删除成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "目标岗位不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /target-positions/{id} [delete]
// @Security BearerAuth
func (c *TargetPositionController) DeleteTargetPosition(ctx *gin.Context, req *dto.DeleteTargetPositionRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除目标岗位
	err := c.targetPositionService.DeleteTargetPosition(ctx.Request.Context(), user.ID, req.ID)
	if err != nil {
		c.logger.Error("删除目标岗位失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("position_id", req.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "删除成功")
}

// GetAllTargetPositions 获取用户所有目标岗位
// @Summary 获取用户所有目标岗位
// @Description 获取当前用户的所有目标岗位列表
// @Tags 目标岗位管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.TargetPositionListResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /target-positions [get]
// @Security BearerAuth
func (c *TargetPositionController) GetAllTargetPositions(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取所有目标岗位
	positions, total, err := c.targetPositionService.GetAllTargetPositions(ctx.Request.Context(), user.ID)
	if err != nil {
		c.logger.Error("获取目标岗位列表失败",
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 构造响应数据
	responseData := vo.TargetPositionListResponse{
		List:  positions,
		Total: total,
	}

	response.SuccessJSON(ctx, "获取成功", responseData)
}

// GetTargetPositionById 根据ID获取目标岗位详情
// @Summary 根据ID获取目标岗位详情
// @Description 根据岗位ID获取目标岗位的详细信息
// @Tags 目标岗位管理
// @Accept json
// @Produce json
// @Param id path int true "岗位ID" example(1)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.TargetPositionResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "目标岗位不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /target-positions/{id} [get]
// @Security BearerAuth
func (c *TargetPositionController) GetTargetPositionById(ctx *gin.Context, req *dto.GetTargetPositionRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取目标岗位详情
	position, err := c.targetPositionService.GetTargetPositionById(ctx.Request.Context(), user.ID, req.ID)
	if err != nil {
		c.logger.Error("获取目标岗位详情失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("position_id", req.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", position)
}

// GetResumeScoreDetail 根据ID获取简历评分详情
// @Summary 根据ID获取简历评分详情
// @Description 根据评分记录ID获取简历评分的详细信息，包含四个维度的评分和详细评价
// @Tags 简历评分管理
// @Accept json
// @Produce json
// @Param id path int true "评分记录ID" example(1)
// @Success 200 {object} response.Response[vo.ResumeScoreDetailResponse] "获取成功"
// @Failure 401 {object} response.Response[any] "未授权"
// @Failure 403 {object} response.Response[any] "无权限访问"
// @Failure 404 {object} response.Response[any] "评分记录不存在"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /resume-scores/{id} [get]
// @Security BearerAuth
func (c *TargetPositionController) GetResumeScoreDetail(ctx *gin.Context, req *dto.GetResumeScoreRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取简历评分详情
	scoreDetail, err := c.resumeScoreService.GetResumeScoreDetail(ctx.Request.Context(), user.ID, req.ID)
	if err != nil {
		c.logger.Error("获取简历评分详情失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("score_id", req.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", scoreDetail)
}

// GetMyResumeScores 获取我的所有简历评分记录
// @Summary 获取我的所有简历评分记录
// @Description 获取当前用户的所有简历评分记录列表，包含简历名称、总体评分、创建时间等信息
// @Tags 简历评分管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1) example(1)
// @Param limit query int false "每页数量" default(10) example(10)
// @Success 200 {object} response.Response[vo.ResumeScoreListResponse] "获取成功"
// @Failure 400 {object} response.Response[any] "参数错误"
// @Failure 401 {object} response.Response[any] "未授权"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /resume-scores [get]
// @Security BearerAuth
func (c *TargetPositionController) GetMyResumeScores(ctx *gin.Context, req *dto.GetMyResumeScoresRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 设置默认分页参数
	page := 1
	limit := 10

	if req.Page > 0 {
		page = req.Page
	}
	if req.Limit > 0 {
		limit = req.Limit
	}

	offset := (page - 1) * limit

	// 调用服务层获取简历评分列表
	scoreList, err := c.resumeScoreService.GetMyResumeScores(ctx.Request.Context(), user.ID, limit, offset)
	if err != nil {
		c.logger.Error("获取简历评分列表失败",
			zap.Uint("user_id", user.ID),
			zap.Int("page", page),
			zap.Int("limit", limit),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", scoreList)
}

// DeleteResumeScore 删除简历评分记录
// @Summary 删除简历评分记录
// @Description 根据评分记录ID删除简历评分记录，只有记录的所有者才能删除
// @Tags 简历评分管理
// @Accept json
// @Produce json
// @Param id path int true "评分记录ID" example(1)
// @Success 200 {object} response.Response[any] "删除成功"
// @Failure 401 {object} response.Response[any] "未授权"
// @Failure 403 {object} response.Response[any] "无权限访问"
// @Failure 404 {object} response.Response[any] "评分记录不存在"
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /resume-scores/{id} [delete]
// @Security BearerAuth
func (c *TargetPositionController) DeleteResumeScore(ctx *gin.Context, req *dto.DeleteResumeScoreRequest) {
	// 从上下文中获取用户信息（中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除简历评分记录
	err := c.resumeScoreService.DeleteResumeScore(ctx.Request.Context(), user.ID, req.ID)
	if err != nil {
		c.logger.Error("删除简历评分记录失败",
			zap.Uint("user_id", user.ID),
			zap.Uint("score_id", req.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "删除成功")
}
