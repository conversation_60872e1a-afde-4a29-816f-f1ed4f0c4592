package controllers

import (
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/google/wire"
)

// ControllerSet 控制器依赖注入集合
var ControllerSet = wire.NewSet(
	api.NewUserController,
	api.NewAuthController,
	api.NewWechatController,
	api.NewCommonController,
	api.NewUploadController,
	api.NewMembershipPlanController,
	api.NewOrderController,
	api.NewOpenAPIController,
	api.NewCategoryController,
	api.NewPositionController,
	api.NewResumeController,
	api.NewAIController,
	api.NewExampleController,
	api.NewTemplateController,
	api.NewTargetPositionController,
	// 未来可以在这里添加其他控制器的构造函数
)
