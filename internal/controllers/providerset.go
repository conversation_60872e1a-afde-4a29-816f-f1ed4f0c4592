package controllers

import (
	"github.com/google/wire"
)

// ControllerSet 控制器依赖注入集合
var ControllerSet = wire.NewSet(
	NewUserController,
	NewAuthController,
	NewWechatController,
	NewCommonController,
	NewUploadController,
	NewMembershipPlanController,
	NewOrderController,
	NewOpenAPIController,
	NewCategoryController,
	NewPositionController,
	NewResumeController,
	NewAIController,
	NewExampleController,
	NewTemplateController,
	NewTargetPositionController,
	// 未来可以在这里添加其他控制器的构造函数
)
