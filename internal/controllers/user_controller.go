package controllers

import (
	"strings"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// UserController 用户控制器
type UserController struct {
	userService               services.UserService
	userDownloadCouponService services.UserDownloadCouponService
	jwtService                pkg.JWTService
	config                    *config.Config
	redisClient               *redis.Client
	ossService                pkg.OSSService
	codeService               services.CodeService
	wechatService             *pkg.WeChatService
	qrCodeService             services.QrCodeService
}

// NewUserController 创建用户控制器
func NewUserController(
	userService services.UserService,
	userDownloadCouponService services.UserDownloadCouponService,
	jwtService pkg.JWTService,
	cfg *config.Config,
	redisClient *redis.Client,
	ossService pkg.OSSService,
	codeService services.CodeService,
	wechatService *pkg.WeChatService,
	qrCodeService services.QrCodeService,
) *UserController {
	return &UserController{
		userService:               userService,
		userDownloadCouponService: userDownloadCouponService,
		jwtService:                jwtService,
		config:                    cfg,
		redisClient:               redisClient,
		ossService:                ossService,
		codeService:               codeService,
		wechatService:             wechatService,
		qrCodeService:             qrCodeService,
	}
}

// GetUserInfo 获取当前登录用户信息
// @Summary 获取当前登录用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UserResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "用户不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/info [get]
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 从上下文中获取登录状态
	isLoggedIn, exists := ctx.Get("isLoggedIn")
	loggedIn := true // 默认为true，因为这是登录用户接口
	if exists {
		if value, ok := isLoggedIn.(bool); ok {
			loggedIn = value
		}
	}

	response.SuccessJSON(ctx, "获取成功", convertToUserResponseWithLogin(user, loggedIn))
}

// Logout 退出登录
// @Summary 退出登录
// @Description 用户退出登录，清除登录状态
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse "退出成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Security ApiKeyAuth
// @Router /users/logout [post]
func (c *UserController) Logout(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 获取token
	authHeader := ctx.GetHeader("Authorization")
	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 调用服务层的Logout方法
	err := c.userService.Logout(ctx, user, token)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON[any](ctx, "退出成功", nil)
}

// GetGuestInfo 获取游客用户基本信息
// @Summary 获取游客用户基本信息
// @Description 获取当前游客用户的基本信息，需要指纹认证
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UserResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "缺少浏览器指纹"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /users/guest/info [get]
func (c *UserController) GetGuestInfo(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 从上下文中获取登录状态
	isLoggedIn, exists := ctx.Get("isLoggedIn")
	loggedIn := false
	if exists {
		if value, ok := isLoggedIn.(bool); ok {
			loggedIn = value
		}
	}

	// 记录用户访问信息
	pkg.Debug("用户访问信息接口", zap.Uint("userId", user.ID), zap.Int("userType", int(user.UserType)), zap.Bool("isLoggedIn", loggedIn))
	response.SuccessJSON(ctx, "获取成功", convertToUserResponseWithLogin(user, loggedIn))
}

// UpdateUsername 修改用户名
// @Summary 修改用户名
// @Description 修改当前登录用户的用户名
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.UpdateUsernameRequest true "修改用户名请求"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/username [put]
func (c *UserController) UpdateUsername(ctx *gin.Context, req dto.UpdateUsernameRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层更新用户名
	err := c.userService.UpdateUsername(user.ID, req.Username)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "用户名修改成功")
}

// UpdateAvatar 修改用户头像
// @Summary 修改用户头像
// @Description 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
// @Tags 用户管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/avatar [put]
func (c *UserController) UpdateAvatar(ctx *gin.Context, req dto.UpdateAvatarRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层处理文件上传和头像更新
	err := c.userService.UpdateAvatarWithFile(ctx, user.ID, req.File)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "头像修改成功")
}

// BindPhone 绑定手机号
// @Summary 绑定手机号
// @Description 为当前登录用户绑定手机号，需要先发送验证码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.BindPhoneRequest true "绑定手机号请求"
// @Success 200 {object} vo.SuccessAPIResponse "绑定成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/bind-phone [post]
func (c *UserController) BindPhone(ctx *gin.Context, req dto.BindPhoneRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层绑定手机号
	err := c.userService.BindPhone(ctx, user.ID, req.Phone, req.Code)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "手机号绑定成功")
}

// BindEmail 绑定邮箱
// @Summary 绑定邮箱
// @Description 为当前登录用户绑定邮箱，需要先发送验证码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.BindEmailRequest true "绑定邮箱请求"
// @Success 200 {object} vo.SuccessAPIResponse "绑定成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/bind-email [post]
func (c *UserController) BindEmail(ctx *gin.Context, req dto.BindEmailRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层绑定邮箱
	err := c.userService.BindEmail(ctx, user.ID, req.Email, req.Code)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "邮箱绑定成功")
}

// GetBindWechatQrCode 获取绑定微信二维码
// @Summary 获取绑定微信二维码
// @Description 获取当前登录用户绑定微信的二维码，返回二维码图片URL和场景值
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.QrCodeLoginResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/bind-wechat/qrcode [get]
func (c *UserController) GetBindWechatQrCode(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层生成绑定微信二维码
	qrCodeResponse, err := c.qrCodeService.GenerateBindWechatQrCode(ctx, user.ID)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取绑定微信二维码成功", qrCodeResponse)
}

// CheckBindWechatQrCodeStatus 检查绑定微信二维码状态
// @Summary 检查绑定微信二维码状态
// @Description 检查绑定微信二维码的扫描状态，如果已扫描则执行绑定操作
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.CheckBindWechatQrCodeStatusRequest true "检查绑定微信二维码状态请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.QrCodeStatusResponse} "检查成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/bind-wechat/qrcode/status [post]
func (c *UserController) CheckBindWechatQrCodeStatus(ctx *gin.Context, req dto.CheckBindWechatQrCodeStatusRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层检查绑定微信二维码状态
	statusResponse, err := c.userService.CheckBindWechatQrCodeStatus(ctx, user.ID, req.SceneId)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "二维码状态检查成功", statusResponse)
}

// convertToUserResponseWithLogin 将用户模型转换为响应VO，并包含登录状态
func convertToUserResponseWithLogin(user *models.User, isLoggedIn bool) vo.UserResponse {
	return vo.UserResponse{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Phone:      user.Phone,
		OpenID:     user.OpenID,
		Avatar:     user.Avatar,
		CreatedAt:  user.CreatedAt,
		UserType:   user.UserType,
		IsLoggedIn: isLoggedIn,
	}
}

// GetAvailableCouponsCount 获取用户可用的下载券数量
// @Summary 获取用户可用的下载券数量
// @Description 获取当前登录用户可用的下载券数量
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.AvailableCouponsCountResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /users/download-coupons/count [get]
func (c *UserController) GetAvailableCouponsCount(ctx *gin.Context) {
	// 从上下文中获取用户信息
	user, exists := ctx.Get("user")
	if !exists {
		response.ThrowError(ctx, exception.ErrUserNotFound)
		return
	}

	userModel := user.(*models.User)

	// 调用服务获取用户可用的下载券数量
	result, err := c.userDownloadCouponService.GetAvailableCouponsCount(ctx.Request.Context(), userModel.ID)
	if err != nil {
		pkg.Error("获取用户可用下载券数量失败",
			zap.Uint("user_id", userModel.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取下载券数量成功", result)
}
