package api

import (
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// OrderController 订单控制器
type OrderController struct {
	orderService services.OrderService
	logger       *zap.Logger
}

// NewOrderController 创建订单控制器
func NewOrderController(
	orderService services.OrderService,
	logger *zap.Logger,
) *OrderController {
	return &OrderController{
		orderService: orderService,
		logger:       logger,
	}
}

// CreateAlipayOrder 创建支付宝订单
// @Summary 创建支付宝订单
// @Description 创建支付宝订单并返回支付二维码链接
// @Tags API/订单管理
// @Accept json
// @Produce json
// @Param request body dto.CreateOrderRequest true "创建订单请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.OrderResponse} "创建成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "会员套餐不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/orders/alipay [post]
func (c *OrderController) CreateAlipayOrder(ctx *gin.Context, req dto.CreateOrderRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务创建支付宝订单
	orderResponse, err := c.orderService.CreateAlipayOrder(ctx, user.ID, req.PlanID, req.BdVid)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "创建订单成功", orderResponse)
}

// CreateWechatPayOrder 创建微信支付订单
// @Summary 创建微信支付订单
// @Description 创建微信支付订单并返回支付二维码链接
// @Tags API/订单管理
// @Accept json
// @Produce json
// @Param request body dto.CreateOrderRequest true "创建订单请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.OrderResponse} "创建成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "会员套餐不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/orders/wechat [post]
func (c *OrderController) CreateWechatPayOrder(ctx *gin.Context, req dto.CreateOrderRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务创建微信支付订单
	orderResponse, err := c.orderService.CreateWechatPayOrder(ctx, user.ID, req.PlanID, req.BdVid)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "创建订单成功", orderResponse)
}

// GetOrderStatus 查询订单状态
// @Summary 查询订单状态
// @Description 根据订单号查询订单状态，返回订单号、支付状态(整数值:1待支付 2支付处理中 3支付成功 4支付失败 5支付超时)和失败原因(如果有)
// @Tags API/订单管理
// @Accept json
// @Produce json
// @Param request body dto.QueryOrderStatusRequest true "查询订单状态请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.OrderStatusResponse} "查询成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "订单不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/orders/status [post]
func (c *OrderController) GetOrderStatus(ctx *gin.Context, req dto.QueryOrderStatusRequest) {
	// 调用服务查询订单状态
	orderStatus, err := c.orderService.GetOrderStatus(ctx, req.OrderNo)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "查询订单状态成功", orderStatus)
}

// GetUserOrders 获取用户订单列表
// @Summary 获取用户订单列表
// @Description 分页获取当前用户的订单列表，支持按支付状态筛选，按创建时间倒序排列
// @Tags API/订单管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1" minimum(1) example(1)
// @Param page_size query int false "每页条数，默认为10，最大100" minimum(1) maximum(100) example(10)
// @Param payment_status query string false "支付状态筛选：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时，支持多个状态组合查询(用逗号分隔)，不传则查询所有状态" example("1,3")
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.OrderListResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/orders [get]
func (c *OrderController) GetUserOrders(ctx *gin.Context, req dto.GetUserOrdersRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务获取用户订单列表
	orderList, err := c.orderService.GetUserOrders(ctx, user.ID, &req)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取订单列表成功", orderList)
}
