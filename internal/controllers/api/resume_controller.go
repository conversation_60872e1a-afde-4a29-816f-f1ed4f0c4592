package api

import (
	"fmt"
	"strings"

	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// ResumeController 简历控制器
type ResumeController struct {
	resumeService services.ResumeService
	redisClient   *redis.Client
	logger        *zap.Logger
}

// NewResumeController 创建简历控制器
func NewResumeController(
	resumeService services.ResumeService,
	redisClient *redis.Client,
	logger *zap.Logger,
) *ResumeController {
	return &ResumeController{
		resumeService: resumeService,
		redisClient:   redisClient,
		logger:        logger,
	}
}

// GetResumeDetail 根据简历ID获取简历详情
// @Summary 获取简历详情
// @Description 根据简历ID获取简历的详细信息，包括基本信息、教育经历、工作经历等所有模块
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ResumeDetailResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id} [get]
// @Security BearerAuth
func (c *ResumeController) GetResumeDetail(ctx *gin.Context, req dto.GetResumeDetailRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取简历详情
	resumeDetail, err := c.resumeService.GetResumeDetailByID(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("获取简历详情失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取简历详情成功", resumeDetail)
}

// GetResumeDraftDetail 根据简历草稿ID获取简历草稿详情
// @Summary 获取简历草稿详情
// @Description 根据简历草稿ID获取简历草稿的详细信息，包括基本信息、教育经历、工作经历等所有模块
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param draft_id path string true "简历草稿ID"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ResumeDetailResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历草稿"
// @Failure 404 {object} vo.ErrorAPIResponse "简历草稿不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resume-drafts/{draft_id} [get]
// @Security BearerAuth
func (c *ResumeController) GetResumeDraftDetail(ctx *gin.Context, req dto.GetResumeDraftDetailRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取简历草稿详情
	resumeDraftDetail, err := c.resumeService.GetResumeDraftDetailByID(ctx.Request.Context(), req.DraftID, user.ID)
	if err != nil {
		c.logger.Error("获取简历草稿详情失败",
			zap.String("draft_id", req.DraftID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取简历草稿详情成功", resumeDraftDetail)
}

// SaveResumeDetail 保存简历详情
// @Summary 保存简历详情
// @Description 保存简历的所有模块内容，包括基本信息、教育经历、工作经历等
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Param request body dto.SaveResumeRequest true "简历详情数据"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.SaveResumeResponse} "保存成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id} [put]
// @Security BearerAuth
func (c *ResumeController) SaveResumeDetail(ctx *gin.Context, req dto.SaveResumeRequest) {
	// 直接获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 从请求头中获取JWT Token
	authHeader := ctx.GetHeader("Authorization")
	var jwtToken string
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			jwtToken = parts[1]
		}
	}

	// 从请求头中获取浏览器指纹
	fingerprint := ctx.GetHeader("X-Fingerprint")

	// 调用服务层保存简历详情
	saveResponse, err := c.resumeService.SaveResumeDetail(ctx.Request.Context(), req.ResumeID, user.ID, &req, jwtToken, fingerprint)
	if err != nil {
		c.logger.Error("保存简历详情失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "保存简历详情成功", saveResponse)
}

// UpdateResumeName 修改简历名称
// @Summary 修改简历名称
// @Description 修改指定简历的名称
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Param request body dto.UpdateResumeNameRequest true "简历名称数据"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UpdateResumeNameResponse} "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/name [put]
// @Security BearerAuth
func (c *ResumeController) UpdateResumeName(ctx *gin.Context, req dto.UpdateResumeNameRequest) {
	// 直接获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层修改简历名称
	updateResponse, err := c.resumeService.UpdateResumeName(ctx.Request.Context(), req.ResumeID, user.ID, req.ResumeName)
	if err != nil {
		c.logger.Error("修改简历名称失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.String("resume_name", req.ResumeName),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "修改简历名称成功", updateResponse)
}

// RecordOnline 记录在线用户
// @Summary 记录在线用户
// @Description 记录用户在线状态并返回当前在线人数（5分钟内活跃用户）
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=map[string]int64} "记录成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/online [post]
// @Security BearerAuth
func (c *ResumeController) RecordOnline(ctx *gin.Context) {
	// 直接获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层记录在线用户
	onlineCount, err := c.resumeService.RecordOnlineUser(ctx.Request.Context(), user.ID)
	if err != nil {
		c.logger.Error("记录在线用户失败",
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 构造响应数据
	responseData := map[string]int64{
		"online_count": onlineCount,
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "记录在线用户成功", responseData)
}

// DownloadResumePDF 下载简历PDF
// @Summary 下载简历PDF
// @Description 根据简历ID生成并下载PDF文件
// @Tags API/简历管理
// @Produce application/pdf
// @Param resume_id path string true "简历ID"
// @Success 200 {file} binary "PDF文件"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/download [get]
// @Security BearerAuth
func (c *ResumeController) DownloadResumePDF(ctx *gin.Context, req dto.DownloadResumePDFRequest) {
	// 获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 从请求头中获取JWT Token
	authHeader := ctx.GetHeader("Authorization")
	var jwtToken string
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			jwtToken = parts[1]
		}
	}

	// 从请求头中获取浏览器指纹
	fingerprint := ctx.GetHeader("X-Fingerprint")

	// 调用服务层下载简历PDF
	pdfBytes, fileName, err := c.resumeService.DownloadResumePDF(ctx.Request.Context(), req.ResumeID, user.ID, jwtToken, fingerprint)
	if err != nil {
		c.logger.Error("下载简历PDF失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/pdf")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	ctx.Header("Content-Length", fmt.Sprintf("%d", len(pdfBytes)))

	// 直接返回PDF文件流
	ctx.Data(200, "application/pdf", pdfBytes)

	c.logger.Info("简历PDF下载成功",
		zap.String("resume_id", req.ResumeID),
		zap.Uint("user_id", user.ID),
		zap.String("file_name", fileName),
		zap.Int("file_size", len(pdfBytes)))
}

// ShareResumeByEmail 邮件分享简历
// @Summary 邮件分享简历
// @Description 生成简历PDF并通过邮件发送给指定邮箱地址
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Param request body dto.ShareResumeByEmailRequest true "邮件分享数据"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ShareResumeByEmailResponse} "分享成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/share/email [post]
// @Security BearerAuth
func (c *ResumeController) ShareResumeByEmail(ctx *gin.Context, req dto.ShareResumeByEmailRequest) {
	// 获取用户信息（JWT中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 从请求头中获取JWT Token
	authHeader := ctx.GetHeader("Authorization")
	var jwtToken string
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			jwtToken = parts[1]
		}
	}

	// 从请求头中获取浏览器指纹
	fingerprint := ctx.GetHeader("X-Fingerprint")

	// 调用服务层邮件分享简历
	shareResponse, err := c.resumeService.ShareResumeByEmail(ctx.Request.Context(), req.ResumeID, user.ID, req.Email, req.FileName, jwtToken, fingerprint)
	if err != nil {
		c.logger.Error("邮件分享简历失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.String("email", req.Email),
			zap.String("file_name", req.FileName),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "简历邮件分享任务已启动", shareResponse)
}

// GetResumeBasicInfo 获取简历基本信息
// @Summary 获取简历基本信息
// @Description 根据简历ID获取简历的基本信息（ID、名称、预览图、创建时间）
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.GetResumeBasicInfoResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/basic [get]
// @Security BearerAuth
func (c *ResumeController) GetResumeBasicInfo(ctx *gin.Context, req dto.GetResumeBasicInfoRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取简历基本信息
	basicInfo, err := c.resumeService.GetResumeBasicInfo(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("获取简历基本信息失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取简历基本信息成功", basicInfo)
}

// GetAllMyResumes 获取所有自己的简历
// @Summary 获取所有自己的简历
// @Description 获取当前用户的所有简历列表，只返回简历表的基本信息，按更新时间排序
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.GetAllMyResumesResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes [get]
// @Security BearerAuth
func (c *ResumeController) GetAllMyResumes(ctx *gin.Context) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取所有简历
	resumeList, err := c.resumeService.GetAllMyResumes(ctx.Request.Context(), user.ID)
	if err != nil {
		c.logger.Error("获取用户简历列表失败",
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取简历列表成功", resumeList)
}

// GetDeletedResumes 获取回收站简历
// @Summary 获取回收站简历
// @Description 获取当前用户回收站中的所有简历列表，只返回已删除的简历基本信息，按删除时间排序
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.GetDeletedResumesResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/trash [get]
// @Security BearerAuth
func (c *ResumeController) GetDeletedResumes(ctx *gin.Context) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取回收站简历
	resumeList, err := c.resumeService.GetDeletedResumes(ctx.Request.Context(), user.ID)
	if err != nil {
		c.logger.Error("获取用户回收站简历列表失败",
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取回收站简历列表成功", resumeList)
}

// DeleteResume 删除简历
// @Summary 删除简历
// @Description 根据简历ID删除简历及其所有相关数据
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse "删除成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id} [delete]
// @Security BearerAuth
func (c *ResumeController) DeleteResume(ctx *gin.Context, req dto.DeleteResumeRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除简历
	err := c.resumeService.DeleteResume(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("删除简历失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessNoDataJSON(ctx, "删除简历成功")
}

// PermanentlyDeleteResume 物理删除简历
// @Summary 物理删除简历
// @Description 根据简历ID彻底删除简历及其所有相关数据，只能删除回收站中的简历
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse "彻底删除成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历或简历不在回收站"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/permanently [delete]
// @Security BearerAuth
func (c *ResumeController) PermanentlyDeleteResume(ctx *gin.Context, req dto.PermanentlyDeleteResumeRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层物理删除简历
	err := c.resumeService.PermanentlyDeleteResume(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("物理删除简历失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessNoDataJSON(ctx, "彻底删除简历成功")
}

// RestoreResume 恢复简历
// @Summary 恢复简历
// @Description 根据简历ID从回收站恢复简历，只能恢复回收站中的简历
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse "恢复成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历或简历不在回收站"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/restore [put]
// @Security BearerAuth
func (c *ResumeController) RestoreResume(ctx *gin.Context, req dto.RestoreResumeRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层恢复简历
	err := c.resumeService.RestoreResume(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("恢复简历失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessNoDataJSON(ctx, "恢复简历成功")
}

// CopyResume 复制简历
// @Summary 复制简历
// @Description 根据简历ID复制简历及其所有相关数据，新简历名称会在原名称后加上"-复制"
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Success 200 {object} vo.SuccessAPIResponse "复制成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历"
// @Failure 404 {object} vo.ErrorAPIResponse "简历不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/copy [post]
// @Security BearerAuth
func (c *ResumeController) CopyResume(ctx *gin.Context, req dto.CopyResumeRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层复制简历
	err := c.resumeService.CopyResume(ctx.Request.Context(), req.ResumeID, user.ID)
	if err != nil {
		c.logger.Error("复制简历失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessNoDataJSON(ctx, "复制简历成功")
}

// ApplyDraft 应用草稿到简历
// @Summary 应用草稿到简历
// @Description 将草稿数据覆盖到指定简历，用草稿内容更新简历的所有模块数据
// @Tags API/简历管理
// @Accept json
// @Produce json
// @Param resume_id path string true "简历ID"
// @Param request body dto.ApplyDraftRequest true "应用草稿数据"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ApplyDraftResponse} "应用成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权访问该简历或草稿"
// @Failure 404 {object} vo.ErrorAPIResponse "简历或草稿不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/resumes/{resume_id}/apply-draft [post]
// @Security BearerAuth
func (c *ResumeController) ApplyDraft(ctx *gin.Context, req dto.ApplyDraftRequest) {
	// 获取用户信息（游客中间件已确保用户存在）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层应用草稿
	applyResponse, err := c.resumeService.ApplyDraft(ctx.Request.Context(), req.ResumeID, req.DraftID, user.ID)
	if err != nil {
		c.logger.Error("应用草稿失败",
			zap.String("resume_id", req.ResumeID),
			zap.Uint("draft_id", req.DraftID),
			zap.Uint("user_id", user.ID),
			zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "应用草稿成功", applyResponse)
}
