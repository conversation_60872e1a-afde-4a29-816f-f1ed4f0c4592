package controllers

import (
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PositionController 职位控制器
type PositionController struct {
	positionService services.PositionService
	logger          *zap.Logger
}

// NewPositionController 创建职位控制器
func NewPositionController(
	positionService services.PositionService,
	logger *zap.Logger,
) *PositionController {
	return &PositionController{
		positionService: positionService,
		logger:          logger,
	}
}

// GetAllPositions 获取所有职位信息（层级结构）
// @Summary 获取所有职位信息
// @Description 获取所有职位信息，按层级结构返回，支持无限层级嵌套
// @Tags 职位管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.PositionListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /positions [get]
func (c *PositionController) GetAllPositions(ctx *gin.Context) {
	// 调用服务层获取职位层级数据
	positions, err := c.positionService.GetAllPositionsHierarchy(ctx)
	if err != nil {
		c.logger.Error("获取职位数据失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", positions)
}

// GetPositionsByParentID 根据一级分类ID获取下面的二级三级分类
// @Summary 根据一级分类ID获取子分类
// @Description 根据一级分类ID获取下面的二级三级分类，按层级结构返回
// @Tags 职位管理
// @Accept json
// @Produce json
// @Param parent_id path int true "一级分类ID"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.PositionListResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /positions/{parent_id}/children [get]
func (c *PositionController) GetPositionsByParentID(ctx *gin.Context, req *dto.GetPositionsByParentIDRequest) {
	// 调用服务层获取子分类数据（参数已通过验证中间件验证）
	positions, err := c.positionService.GetPositionsByParentID(ctx, req.ParentID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", positions)
}

// SearchPositions 搜索职位和示例
// @Summary 搜索职位和示例
// @Description 根据关键字搜索职位和示例信息，支持按名称和slug_cn搜索，返回包含type字段区分职位(1)和示例(2)的结果，最多返回10条结果
// @Tags 职位管理
// @Accept json
// @Produce json
// @Param keyword path string true "搜索关键字"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.SearchResponse} "搜索成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /positions/search/{keyword} [get]
func (c *PositionController) SearchPositions(ctx *gin.Context, req *dto.SearchPositionsRequest) {
	// 调用服务层进行搜索
	searchResult, err := c.positionService.SearchPositions(ctx, req.Keyword)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "搜索成功", searchResult)
}
