package api

import (
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MembershipPlanController 会员套餐控制器
type MembershipPlanController struct {
	planService services.MembershipPlanService
	logger      *zap.Logger
}

// NewMembershipPlanController 创建会员套餐控制器
func NewMembershipPlanController(
	planService services.MembershipPlanService,
	logger *zap.Logger,
) *MembershipPlanController {
	return &MembershipPlanController{
		planService: planService,
		logger:      logger,
	}
}

// GetVisiblePlans 获取所有可见的会员套餐
// @Summary 获取所有可见的会员套餐
// @Description 获取所有可见的会员套餐，按照排序字段降序排列（值越大越靠前）
// @Tags 会员套餐
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.MembershipPlanListResponse} "成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /membership-plans [get]
func (c *MembershipPlanController) GetVisiblePlans(ctx *gin.Context) {
	// 调用服务获取所有可见的会员套餐
	plans, err := c.planService.GetVisiblePlans(ctx)
	if err != nil {
		c.logger.Error("获取会员套餐失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取会员套餐成功", plans)
}

// GetVisibleDownloadCouponPlans 获取所有可见的下载券套餐
// @Summary 获取所有可见的下载券套餐
// @Description 获取所有可见的下载券套餐，只返回基本信息（ID、名称、价格），按照排序字段降序排列（值越大越靠前）
// @Tags 会员套餐
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.DownloadCouponPlanListResponse} "成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /download-coupon-plans [get]
func (c *MembershipPlanController) GetVisibleDownloadCouponPlans(ctx *gin.Context) {
	// 调用服务获取所有可见的下载券套餐
	plans, err := c.planService.GetVisibleDownloadCouponPlans(ctx)
	if err != nil {
		c.logger.Error("获取下载券套餐失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取下载券套餐成功", plans)
}
