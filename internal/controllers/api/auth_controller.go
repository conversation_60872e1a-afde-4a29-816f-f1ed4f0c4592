package api

import (
	"context"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/exception"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// AuthController 认证控制器
type AuthController struct {
	userService      services.UserService
	userRepo         repository.UserRepository
	codeService      services.CodeService
	emailCodeService services.EmailCodeService
	qrCodeService    services.QrCodeService
	jwtService       pkg.JWTService
	wechatService    *pkg.WeChatService
	config           *config.Config
	redisClient      *redis.Client
}

// NewAuthController 创建认证控制器
func NewAuthController(
	userService services.UserService,
	userRepo repository.UserRepository,
	codeService services.CodeService,
	emailCodeService services.EmailCodeService,
	qrCodeService services.QrCodeService,
	jwtService pkg.JWTService,
	wechatService *pkg.WeChatService,
	cfg *config.Config,
	redisClient *redis.Client,
) *AuthController {
	return &AuthController{
		userService:      userService,
		userRepo:         userRepo,
		codeService:      codeService,
		emailCodeService: emailCodeService,
		qrCodeService:    qrCodeService,
		jwtService:       jwtService,
		wechatService:    wechatService,
		config:           cfg,
		redisClient:      redisClient,
	}
}

// LoginCode 验证码登录
// @Summary 验证码登录
// @Description 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.LoginCodeRequest true "登录信息"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.TokenResponse} "登录成功，返回token"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "验证码错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/login-code [post]
func (c *AuthController) LoginCode(ctx *gin.Context, req dto.LoginCodeRequest) {
	// 从请求头中获取指纹信息
	fingerPrint := ctx.GetHeader("X-Fingerprint")

	// 创建一个新的上下文，包含指纹信息
	newCtx := context.WithValue(ctx.Request.Context(), "fingerPrint", fingerPrint)

	// 使用验证码登录并生成token
	tokenResponse, err := c.userService.LoginByCodeWithToken(newCtx, req.Phone, req.Code)
	if err != nil {
		// 服务层已经记录了错误日志，这里直接返回
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "登录成功", tokenResponse)
}

// SendSMSCode 发送短信验证码
// @Summary 发送短信验证码
// @Description 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.SendSMSCodeRequest true "发送验证码请求"
// @Success 200 {object} vo.SuccessAPIResponse "发送成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 429 {object} vo.ErrorAPIResponse "发送频率限制"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/sms/code [post]
func (c *AuthController) SendSMSCode(ctx *gin.Context, req dto.SendSMSCodeRequest) {
	// 调用验证码服务发送验证码
	err := c.codeService.SendSMSCode(ctx, req.Phone)
	if err != nil {
		// 直接将服务层的错误抛出
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "验证码发送成功")
}

// SendBindPhoneCode 发送绑定手机号验证码
// @Summary 发送绑定手机号验证码
// @Description 向指定手机号发送绑定手机号验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.SendSMSCodeRequest true "发送验证码请求"
// @Success 200 {object} vo.SuccessAPIResponse "发送成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 429 {object} vo.ErrorAPIResponse "发送频率限制"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/auth/sms/bind-phone-code [post]
func (c *AuthController) SendBindPhoneCode(ctx *gin.Context, req dto.SendSMSCodeRequest) {
	// 检查手机号是否已被其他用户使用
	existingUser, err := c.userService.GetUserByPhone(req.Phone)
	if err == nil && existingUser != nil {
		response.ThrowError(ctx, exception.ErrInvalidParam.WithMessage("该手机号已被其他用户使用"))
		return
	}

	// 调用验证码服务发送验证码
	err = c.codeService.SendSMSCode(ctx, req.Phone)
	if err != nil {
		// 直接将服务层的错误抛出
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "验证码发送成功")
}

// SendBindEmailCode 发送绑定邮箱验证码
// @Summary 发送绑定邮箱验证码
// @Description 向指定邮箱发送绑定邮箱验证码，验证码有效期5分钟，同一邮箱1分钟内只能发送一次
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.SendEmailCodeRequest true "发送验证码请求"
// @Success 200 {object} vo.SuccessAPIResponse "发送成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 429 {object} vo.ErrorAPIResponse "发送频率限制"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/auth/email/bind-email-code [post]
func (c *AuthController) SendBindEmailCode(ctx *gin.Context, req dto.SendEmailCodeRequest) {
	// 检查邮箱是否已被其他用户使用
	existingUser, err := c.userService.GetUserByEmail(req.Email)
	if err == nil && existingUser != nil {
		response.ThrowError(ctx, exception.ErrInvalidParam.WithMessage("该邮箱已被其他用户使用"))
		return
	}

	// 调用验证码服务发送验证码
	err = c.emailCodeService.SendEmailCode(ctx, req.Email)
	if err != nil {
		// 直接将服务层的错误抛出
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "验证码发送成功")
}

// SendEmailCode 发送邮件验证码
// @Summary 发送邮件验证码
// @Description 向指定邮箱发送验证码，验证码有效期5分钟，同一邮箱号码1分钟内只能发送一次
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.SendEmailCodeRequest true "发送验证码请求"
// @Success 200 {object} vo.SuccessAPIResponse "发送成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 429 {object} vo.ErrorAPIResponse "发送频率限制"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/email/code [post]
func (c *AuthController) SendEmailCode(ctx *gin.Context, req dto.SendEmailCodeRequest) {
	// 调用验证码服务发送验证码
	err := c.emailCodeService.SendEmailCode(ctx, req.Email)
	if err != nil {
		// 直接将服务层的错误抛出
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "验证码发送成功")
}

// LoginEmailCode 邮箱验证码登录
// @Summary 邮箱验证码登录
// @Description 用户使用邮箱和验证码登录并获取令牌，如果用户不存在则自动注册
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.LoginEmailCodeRequest true "邮箱验证码登录请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.TokenResponse} "登录成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "验证码错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/email/login [post]
func (c *AuthController) LoginEmailCode(ctx *gin.Context, req dto.LoginEmailCodeRequest) {
	// 从请求头中获取指纹信息
	fingerPrint := ctx.GetHeader("X-Fingerprint")

	// 创建一个新的上下文，包含指纹信息
	newCtx := context.WithValue(ctx.Request.Context(), "fingerPrint", fingerPrint)

	// 使用验证码登录并生成token
	tokenResponse, err := c.userService.LoginByEmailCodeWithToken(newCtx, req.Email, req.Code)
	if err != nil {
		// 服务层已经记录了错误日志，这里直接返回
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "登录成功", tokenResponse)
}

// GetLoginQrCode 获取登录二维码
// @Summary 获取登录二维码
// @Description 获取微信扫码登录的二维码，返回二维码图片URL和场景值
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.QrCodeLoginResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/login/qrcode [get]
func (c *AuthController) GetLoginQrCode(ctx *gin.Context) {
	// 从上下文中获取用户ID，如果有的话
	var userID uint = 0
	user, exists := ctx.Get("user")
	if exists {
		if u, ok := user.(*models.User); ok {
			userID = u.ID
		}
	}

	// 调用服务层生成登录二维码，传入用户ID
	qrCodeResponse, err := c.qrCodeService.GenerateLoginQrCode(ctx, userID)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取登录二维码成功", qrCodeResponse)
}

// CheckQrCodeStatus 检查二维码状态
// @Summary 检查二维码状态
// @Description 检查微信扫码登录的二维码状态，如果已扫码并且用户存在则返回token
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.QrCodeStatusRequest true "二维码状态请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.QrCodeStatusResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/login/qrcode/status [post]
func (c *AuthController) CheckQrCodeStatus(ctx *gin.Context, req dto.QrCodeStatusRequest) {
	// 调用服务层检查二维码状态，传入用户服务
	statusResponse, err := c.qrCodeService.CheckQrCodeStatus(ctx, req.SceneId, c.userService)
	if err != nil {
		// 服务层已记录错误日志，这里直接返回错误
		response.ThrowError(ctx, err)
		return
	}

	// 返回二维码状态
	response.SuccessJSON(ctx, "二维码状态查询成功", statusResponse)
}
