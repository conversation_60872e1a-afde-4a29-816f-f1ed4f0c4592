package api

import (
	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/vo"
	"github.com/gin-gonic/gin"
)

// CommonController 通用控制器
type CommonController struct {
}

// NewCommonController 创建通用控制器
func NewCommonController() *CommonController {
	return &CommonController{}
}

// GetEnums 获取枚举列表
// @Summary 获取枚举列表
// @Description 获取系统中定义的枚举列表，返回枚举的code和name，方便前端下拉组件使用
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} response.Response[vo.SimpleEnumsResponse] "获取成功" {"code":0,"message":"获取枚举列表成功","data":{"account_status":[{"code":0,"name":"正常"},{"code":1,"name":"异常"}],"notice_status":[{"code":0,"name":"待处理"},{"code":1,"name":"已接受"},{"code":2,"name":"已拒绝"}]}}
// @Failure 500 {object} response.Response[any] "服务器内部错误"
// @Router /api/common/enums [get]
func (c *CommonController) GetEnums(ctx *gin.Context) {
	// 创建简化的响应结构
	enumsResponse := vo.SimpleEnumsResponse{}

	// 处理简历模块枚举
	enumsResponse["resume_module"] = []vo.EnumItem{
		{Code: enum.ResumeModuleBasicInfo, Name: enum.ResumeModuleBasicInfo.String()},
		{Code: enum.ResumeModuleEducation, Name: enum.ResumeModuleEducation.String()},
		{Code: enum.ResumeModuleWork, Name: enum.ResumeModuleWork.String()},
		{Code: enum.ResumeModuleProject, Name: enum.ResumeModuleProject.String()},
		{Code: enum.ResumeModuleResearch, Name: enum.ResumeModuleResearch.String()},
		{Code: enum.ResumeModuleTeam, Name: enum.ResumeModuleTeam.String()},
		{Code: enum.ResumeModulePortfolio, Name: enum.ResumeModulePortfolio.String()},
		{Code: enum.ResumeModuleOther, Name: enum.ResumeModuleOther.String()},
		{Code: enum.ResumeModulePersonalSummary, Name: enum.ResumeModulePersonalSummary.String()},
		{Code: enum.ResumeModuleHonors, Name: enum.ResumeModuleHonors.String()},
		{Code: enum.ResumeModuleSkills, Name: enum.ResumeModuleSkills.String()},
		{Code: enum.ResumeModuleCustomModules, Name: enum.ResumeModuleCustomModules.String()},
		{Code: enum.ResumeModuleSlogan, Name: enum.ResumeModuleSlogan.String()},
		{Code: enum.ResumeModuleResumeStyle, Name: enum.ResumeModuleResumeStyle.String()},
	}

	// 处理提示词类型枚举
	enumsResponse["prompt_type"] = []vo.EnumItem{
		{Code: enum.PromptTypeGenerate, Name: enum.PromptTypeGenerate.String()},
		{Code: enum.PromptTypeContinue, Name: enum.PromptTypeContinue.String()},
		{Code: enum.PromptTypeProfessional, Name: enum.PromptTypeProfessional.String()},
		{Code: enum.PromptTypeConcise, Name: enum.PromptTypeConcise.String()},
		{Code: enum.PromptTypeDetailed, Name: enum.PromptTypeDetailed.String()},
	}

	// 返回成功响应
	response.SuccessJSON(ctx, "获取枚举列表成功", enumsResponse)
}
