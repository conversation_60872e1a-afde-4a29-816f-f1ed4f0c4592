package controllers

import (
	"github.com/avrilko/resume-server/internal/dto"
	"github.com/avrilko/resume-server/internal/response"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
)

// CategoryController 分类控制器
type CategoryController struct {
	categoryService services.CategoryService
}

// NewCategoryController 创建分类控制器
func NewCategoryController(categoryService services.CategoryService) *CategoryController {
	return &CategoryController{
		categoryService: categoryService,
	}
}

// GetAllCategories 获取所有分类数据
// @Summary 获取所有分类数据
// @Description 获取所有分类数据，按分类类型分组返回
// @Tags 分类管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.CategoryListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /categories [get]
func (c *CategoryController) GetAllCategories(ctx *gin.Context) {
	// 调用服务层获取分类数据
	categories, err := c.categoryService.GetAllCategoriesGrouped()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", categories)
}

// GetCategoryBySlug 根据slug获取分类详情
// @Summary 根据slug获取分类详情
// @Description 先查询分类表slug_cn，存在则返回分类数据，不存在则查询职位表并返回完整层级结构
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param slug path string true "分类或职位的slug"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.CategoryDetailResponse} "获取成功"
// @Failure 404 {object} vo.ErrorAPIResponse "分类不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /categories/{slug} [get]
func (c *CategoryController) GetCategoryBySlug(ctx *gin.Context) {
	// 获取路径参数
	slug := ctx.Param("slug")

	// 调用服务层获取分类详情
	category, err := c.categoryService.GetCategoryBySlug(slug)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", category)
}

// GetCategoryTdkBySlug 根据slug获取分类TDK信息
// @Summary 根据slug获取分类TDK信息
// @Description 先查询分类表slug_cn，存在则返回分类TDK数据，不存在则查询职位表TDK数据
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param slug path string true "分类或职位的slug"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.CategoryTdkResponse} "获取成功"
// @Failure 404 {object} vo.ErrorAPIResponse "分类不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /categories/tdk/{slug} [get]
func (c *CategoryController) GetCategoryTdkBySlug(ctx *gin.Context, req *dto.GetCategoryTdkBySlugRequest) {
	// 调用服务层获取分类TDK信息
	tdk, err := c.categoryService.GetCategoryTdkBySlug(req.Slug)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", tdk)
}

// GetExamplesBySlug 根据slug获取示例列表
// @Summary 根据slug获取示例列表
// @Description 根据分类或职位的slug获取对应的示例列表，支持分页
// @Tags 分类管理
// @Accept json
// @Produce json
// @Param slug path string true "分类或职位的slug"
// @Param page query int false "页码，默认为1" default(1)
// @Param page_size query int false "每页条数，默认为40" default(40)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ExampleListSwaggerResponse} "获取成功"
// @Failure 404 {object} vo.ErrorAPIResponse "分类不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /categories/{slug}/examples [get]
func (c *CategoryController) GetExamplesBySlug(ctx *gin.Context) {
	// 获取路径参数
	slug := ctx.Param("slug")

	// 获取查询参数
	page := ctx.DefaultQuery("page", "1")
	pageSize := ctx.DefaultQuery("page_size", "40")

	// 调用服务层获取示例列表
	examples, err := c.categoryService.GetExamplesBySlug(slug, page, pageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", examples)
}
