package api

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/contract"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/messages"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// WechatController 微信控制器
type WechatController struct {
	wechatService *pkg.WeChatService
	redisClient   *redis.Client
	userService   services.UserService
}

// NewWechatController 创建微信控制器
func NewWechatController(wechatService *pkg.WeChatService, redisClient *redis.Client, userService services.UserService) *WechatController {
	return &WechatController{
		wechatService: wechatService,
		redisClient:   redisClient,
		userService:   userService,
	}
}

// HandleMessagePush 处理微信消息推送
// @Summary 接收微信服务器消息推送
// @Description 接收微信公众号的消息推送，包括普通消息、事件推送等
// @Tags API/微信管理
// @Accept xml
// @Produce xml
// @Success 200 {string} string "成功"
// @Router /api/wechat/serve [post]
func (c *WechatController) HandleMessagePush(ctx *gin.Context) {
	// 获取微信公众号实例
	officialAccount := c.wechatService.GetOfficialAccount()

	// 处理微信服务器的消息请求
	rs, err := officialAccount.Server.Notify(ctx.Request, func(event contract.EventInterface) interface{} {
		// 记录事件信息
		msgType := event.GetMsgType()
		if msgType == "event" {
			eventType := event.GetEvent()
			// 处理扫码事件和关注事件
			if eventType == "SCAN" || eventType == "subscribe" {
				openID := event.GetFromUserName()
				eventKey := event.GetEventKey()
				ticket := event.GetTicket()

				pkg.Info("收到扫码或关注事件",
					zap.String("event", eventType),
					zap.String("openid", openID),
					zap.String("eventKey", eventKey),
					zap.String("ticket", ticket),
				)

				// 处理扫码事件逻辑，使用独立的上下文避免请求结束后上下文被取消
				go c.handleScanEvent(context.Background(), eventType, openID, eventKey)

				// 回复用户消息
				return messages.NewText("登录成功，请返回网站页面继续操作！")
			}
		}

		// 返回success给微信服务器
		return "success"
	})

	if err != nil {
		pkg.Error("处理微信消息失败", zap.Error(err))
		ctx.String(http.StatusInternalServerError, "处理失败")
		return
	}

	// 将响应返回给微信服务器
	if rs != nil {
		text, err := io.ReadAll(rs.Body)
		if err != nil {
			pkg.Error("读取响应失败", zap.Error(err))
			ctx.String(http.StatusInternalServerError, "读取响应失败")
			return
		}
		ctx.String(http.StatusOK, string(text))
	} else {
		ctx.String(http.StatusOK, "success")
	}
}

// handleScanEvent 处理扫码事件
func (c *WechatController) handleScanEvent(ctx context.Context, eventType, openID, eventKey string) {
	// 从eventKey中提取场景值
	// subscribe事件的eventKey格式为: qrscene_XXX，需要去掉前缀
	// SCAN事件的eventKey直接就是场景值
	sceneID := eventKey
	if eventType == "subscribe" && strings.HasPrefix(eventKey, "qrscene_") {
		sceneID = strings.TrimPrefix(eventKey, "qrscene_")
	}

	// 处理不同类型的二维码
	if strings.HasPrefix(sceneID, "login_") {
		c.handleLoginQrCode(ctx, sceneID, openID)
	} else if strings.HasPrefix(sceneID, "bind_") {
		c.handleBindWechatQrCode(ctx, sceneID, openID)
	} else {
		pkg.Info("未知的二维码类型，忽略", zap.String("sceneID", sceneID))
	}
}

// handleLoginQrCode 处理登录二维码
func (c *WechatController) handleLoginQrCode(ctx context.Context, sceneID, openID string) {
	// 构建Redis键
	redisKey := "qrcode:login:" + sceneID

	pkg.Info("构建Redis键", zap.String("redisKey", redisKey))

	// 获取Redis中存储的二维码信息
	qrCodeInfoStr, err := c.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			pkg.Warn("二维码不存在或已过期", zap.String("sceneID", sceneID))
		} else {
			pkg.Error("获取二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
		}
		return
	}

	// 解析二维码信息
	var qrCodeInfo services.QrCodeInfo
	if err := json.Unmarshal([]byte(qrCodeInfoStr), &qrCodeInfo); err != nil {
		pkg.Error("解析二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
		return
	}

	// 更新二维码状态
	if qrCodeInfo.Status == services.QrCodeStatusPending {
		qrCodeInfo.Status = services.QrCodeStatusScanned
		qrCodeInfo.OpenID = openID
		qrCodeInfo.UpdatedAt = time.Now().Unix()
		// 保留原来的UserID字段，不进行修改

		// 重新序列化并保存到Redis
		newQrCodeInfoStr, err := json.Marshal(qrCodeInfo)
		if err != nil {
			pkg.Error("序列化二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}
		// 获取剩余的过期时间
		ttl, err := c.redisClient.TTL(ctx, redisKey).Result()
		if err != nil {
			pkg.Error("获取二维码过期时间失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}

		// 使用相同的过期时间更新Redis
		if err := c.redisClient.Set(ctx, redisKey, string(newQrCodeInfoStr), ttl).Err(); err != nil {
			pkg.Error("更新二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}

		pkg.Info("二维码状态已更新为已扫描",
			zap.String("sceneID", sceneID),
			zap.String("openID", openID),
			zap.String("status", string(qrCodeInfo.Status)),
		)
	} else {
		pkg.Info("二维码已处理过，当前状态",
			zap.String("sceneID", sceneID),
			zap.String("status", string(qrCodeInfo.Status)),
		)
	}
}

// HandleServerVerification 处理微信服务器验证
// @Summary 处理微信服务器验证请求
// @Description 处理微信公众号服务器配置时的验证请求
// @Tags API/微信管理
// @Produce plain
// @Success 200 {string} string "成功"
// @Router /api/wechat/serve [get]
func (c *WechatController) HandleServerVerification(ctx *gin.Context) {
	// 获取微信公众号实例
	officialAccount := c.wechatService.GetOfficialAccount()

	// 处理微信服务器的验证请求
	_, err := officialAccount.Server.VerifyURL(ctx.Request)
	if err != nil {
		pkg.Error("微信服务器验证失败", zap.Error(err))
		ctx.String(500, "验证失败")
		return
	}

	// 验证成功，将响应写入Response
	ctx.String(200, ctx.Query("echostr"))
}

// handleBindWechatQrCode 处理绑定微信二维码
func (c *WechatController) handleBindWechatQrCode(ctx context.Context, sceneID, openID string) {
	// 构建Redis键
	redisKey := "qrcode:bind:" + sceneID

	pkg.Info("构建Redis键", zap.String("redisKey", redisKey))

	// 获取Redis中存储的二维码信息
	qrCodeInfoStr, err := c.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			pkg.Warn("二维码不存在或已过期", zap.String("sceneID", sceneID))
		} else {
			pkg.Error("获取二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
		}
		return
	}

	// 解析二维码信息
	var qrCodeInfo services.QrCodeInfo
	if err := json.Unmarshal([]byte(qrCodeInfoStr), &qrCodeInfo); err != nil {
		pkg.Error("解析二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
		return
	}

	// 更新二维码状态
	if qrCodeInfo.Status == services.QrCodeStatusPending {
		qrCodeInfo.Status = services.QrCodeStatusScanned
		qrCodeInfo.OpenID = openID
		qrCodeInfo.UpdatedAt = time.Now().Unix()
		// 保留原来的UserID字段，不进行修改

		// 重新序列化并保存到Redis
		newQrCodeInfoStr, err := json.Marshal(qrCodeInfo)
		if err != nil {
			pkg.Error("序列化二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}
		// 获取剩余的过期时间
		ttl, err := c.redisClient.TTL(ctx, redisKey).Result()
		if err != nil {
			pkg.Error("获取二维码过期时间失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}

		// 使用相同的过期时间更新Redis
		if err := c.redisClient.Set(ctx, redisKey, string(newQrCodeInfoStr), ttl).Err(); err != nil {
			pkg.Error("更新二维码信息失败", zap.String("sceneID", sceneID), zap.Error(err))
			return
		}

		pkg.Info("二维码状态已更新为已扫描",
			zap.String("sceneID", sceneID),
			zap.String("openID", openID),
			zap.String("status", string(qrCodeInfo.Status)),
		)
	} else {
		pkg.Info("二维码已处理过，当前状态",
			zap.String("sceneID", sceneID),
			zap.String("status", string(qrCodeInfo.Status)),
		)
	}
}
