package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"go.uber.org/zap"
)

// OpenAPIController OpenAPI控制器
type OpenAPIController struct {
	payService   *pkg.PayService
	orderService services.OrderService
	logger       *zap.Logger
}

// NewOpenAPIController 创建OpenAPI控制器
func NewOpenAPIController(
	payService *pkg.PayService,
	orderService services.OrderService,
	logger *zap.Logger,
) *OpenAPIController {
	return &OpenAPIController{
		payService:   payService,
		orderService: orderService,
		logger:       logger,
	}
}

// AlipayNotify 支付宝支付回调
// @Summary 支付宝支付回调
// @Description 处理支付宝支付回调通知，验证签名并处理订单状态
// @Tags OpenAPI
// @Accept application/x-www-form-urlencoded
// @Produce plain
// @Success 200 {string} string "success"
// @Failure 400 {string} string "fail"
// @Router /openapi/alipay [post]
func (c *OpenAPIController) AlipayNotify(ctx *gin.Context) {
	// 解析表单数据
	if err := ctx.Request.ParseForm(); err != nil {
		c.logger.Error("解析支付宝回调表单数据失败", zap.Error(err))
		ctx.String(http.StatusBadRequest, "fail")
		return
	}

	// 使用支付宝SDK验证签名并解析通知
	notification, err := c.payService.Client.DecodeNotification(ctx.Request.Form)
	if err != nil {
		c.logger.Error("支付宝回调签名验证失败", zap.Error(err))
		ctx.String(http.StatusBadRequest, "fail")
		return
	}

	c.logger.Info("收到支付宝回调",
		zap.String("out_trade_no", notification.OutTradeNo),
		zap.String("trade_status", string(notification.TradeStatus)))

	// 处理订单状态更新
	if err := c.handleOrderStatusUpdate(ctx, notification); err != nil {
		c.logger.Error("处理订单状态更新失败", zap.Error(err))
		// 即使处理失败，也要返回success给支付宝，避免重复回调
	}

	// 返回成功响应给支付宝
	alipay.ACKNotification(ctx.Writer)
}

// handleOrderStatusUpdate 处理订单状态更新
func (c *OpenAPIController) handleOrderStatusUpdate(ctx *gin.Context, notification *alipay.Notification) error {
	// 序列化回调通知为JSON字符串
	callbackBytes, err := json.Marshal(notification)
	if err != nil {
		c.logger.Error("序列化回调通知失败", zap.Error(err))
		// 即使序列化失败，也继续处理，只是不存储回调消息
		callbackBytes = []byte("{}")
	}
	callbackMessage := string(callbackBytes)

	// 调用订单服务处理支付宝回调
	return c.orderService.HandleAlipayCallback(ctx, notification.OutTradeNo, string(notification.TradeStatus), notification.TradeNo, callbackMessage)
}

// WechatPayNotify 微信支付回调
// @Summary 微信支付回调
// @Description 处理微信支付回调通知，验证签名并处理订单状态
// @Tags OpenAPI
// @Accept application/json
// @Produce plain
// @Success 200 {string} string "success"
// @Failure 400 {string} string "fail"
// @Router /openapi/wechatpay [post]
func (c *OpenAPIController) WechatPayNotify(ctx *gin.Context) {
	// 读取请求体
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		c.logger.Error("读取微信支付回调请求体失败", zap.Error(err))
		ctx.String(http.StatusBadRequest, "fail")
		return
	}

	c.logger.Info("收到微信支付回调", zap.String("body", string(body)))

	// 重新设置请求体，因为 ParseNotifyRequest 需要读取请求体
	ctx.Request.Body = io.NopCloser(bytes.NewReader(body))

	// 处理订单状态更新
	if err := c.handleWechatPayOrderStatusUpdate(ctx); err != nil {
		c.logger.Error("处理微信支付订单状态更新失败", zap.Error(err))
		// 即使处理失败，也要返回success给微信支付，避免重复回调
	}

	// 返回成功响应给微信支付
	ctx.String(http.StatusOK, "success")
}

// handleWechatPayOrderStatusUpdate 处理微信支付订单状态更新
func (c *OpenAPIController) handleWechatPayOrderStatusUpdate(ctx *gin.Context) error {
	// 创建通知处理器
	handler := c.payService.CreateNotifyHandler()

	// 解析微信支付回调通知
	transaction := new(payments.Transaction)
	_, err := handler.ParseNotifyRequest(context.Background(), ctx.Request, transaction)
	if err != nil {
		c.logger.Error("微信支付回调验签或解密失败", zap.Error(err))
		return err
	}

	c.logger.Info("收到微信支付回调",
		zap.String("out_trade_no", *transaction.OutTradeNo),
		zap.String("trade_state", *transaction.TradeState))

	// 序列化回调通知为JSON字符串
	callbackBytes, err := json.Marshal(transaction)
	if err != nil {
		c.logger.Error("序列化微信支付回调通知失败", zap.Error(err))
		// 即使序列化失败，也继续处理，只是不存储回调消息
		callbackBytes = []byte("{}")
	}
	callbackMessage := string(callbackBytes)

	// 调用订单服务处理微信支付回调
	return c.orderService.HandleWechatPayCallback(ctx, *transaction.OutTradeNo, *transaction.TradeState, *transaction.TransactionId, callbackMessage)
}
