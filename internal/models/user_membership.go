package models

import (
	"time"

	"github.com/avrilko/resume-server/internal/enum"
)

// UserMembership 用户会员关系表
type UserMembership struct {
	ID               uint                  `json:"id" gorm:"primaryKey"`
	UserID           uint                  `json:"user_id" gorm:"not null;index;comment:用户ID"`
	MembershipPlanID uint                  `json:"membership_plan_id" gorm:"not null;index;comment:会员套餐ID"`
	StartTime        time.Time             `json:"start_time" gorm:"type:datetime;not null;index;comment:开始时间"`
	EndTime          time.Time             `json:"end_time" gorm:"type:datetime;not null;index;comment:结束时间"`
	MembershipStatus enum.MembershipStatus `json:"membership_status" gorm:"type:tinyint(1);not null;default:2;index;comment:会员状态 1:已过期 2:生效中"`
	Base

	// 关联（非外键）
	User           User           `json:"user" gorm:"-"`
	MembershipPlan MembershipPlan `json:"membership_plan" gorm:"-"`
}

// TableName 指定表名
func (UserMembership) TableName() string {
	return "user_membership"
}

// CheckAndUpdateStatus 检查并更新会员状态
// 根据当前时间和结束时间判断会员是否已过期，并更新状态
func (m *UserMembership) CheckAndUpdateStatus() {
	now := time.Now()

	// 检查是否已过期
	if now.After(m.EndTime) {
		m.MembershipStatus = enum.MembershipStatusExpired
	} else {
		m.MembershipStatus = enum.MembershipStatusActive
	}
}
