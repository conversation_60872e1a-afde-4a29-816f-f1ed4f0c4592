package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// StringArray 字符串数组类型，用于JSON格式存储
type StringArray []string

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到StringArray
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = StringArray{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, a)
}

// Value 实现 driver.Valuer 接口，将StringArray转换为JSON存入数据库
func (a StringArray) Value() (driver.Value, error) {
	if a == nil {
		return "[]", nil
	}
	return json.Marshal(a)
}

// UintArray 无符号整数数组类型，用于JSON格式存储
type UintArray []uint

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到UintArray
func (a *UintArray) Scan(value interface{}) error {
	if value == nil {
		*a = UintArray{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, a)
}

// Value 实现 driver.Valuer 接口，将UintArray转换为JSON存入数据库
func (a UintArray) Value() (driver.Value, error) {
	if a == nil {
		return "[]", nil
	}
	return json.Marshal(a)
}

// JSONObject JSON对象类型，用于存储任意JSON对象
type JSONObject map[string]interface{}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到JSONObject
func (j *JSONObject) Scan(value interface{}) error {
	if value == nil {
		*j = JSONObject{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, j)
}

// Value 实现 driver.Valuer 接口，将JSONObject转换为JSON存入数据库
func (j JSONObject) Value() (driver.Value, error) {
	if j == nil {
		return "{}", nil
	}
	return json.Marshal(j)
}

// BasicInfoField 基本信息字段结构
type BasicInfoField struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

// CustomField 自定义字段结构
type CustomField struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Value string `json:"value"`
}

// BasicInfoItem 基本信息项目结构
type BasicInfoItem struct {
	Avatar               BasicInfoField `json:"avatar"`
	AvatarFilter         BasicInfoField `json:"avatar_filter"`
	Birth                BasicInfoField `json:"birth"`
	BirthType            BasicInfoField `json:"birth_type"`
	City                 BasicInfoField `json:"city"`
	CreatedAt            BasicInfoField `json:"created_at"`
	CustomizeFields      BasicInfoField `json:"customize_fields"`
	Email                BasicInfoField `json:"email"`
	Ethnicity            BasicInfoField `json:"ethnicity"`
	Gender               BasicInfoField `json:"gender"`
	Gitee                BasicInfoField `json:"gitee"`
	Github               BasicInfoField `json:"github"`
	Height               BasicInfoField `json:"height"`
	ID                   BasicInfoField `json:"id"`
	IntendedCity         BasicInfoField `json:"intended_city"`
	Job                  BasicInfoField `json:"job"`
	JobStatus            BasicInfoField `json:"job_status"`
	Marital              BasicInfoField `json:"marital"`
	MaxSalary            BasicInfoField `json:"max_salary"`
	Name                 BasicInfoField `json:"name"`
	Origin               BasicInfoField `json:"origin"`
	Phone                BasicInfoField `json:"phone"`
	PoliticalAffiliation BasicInfoField `json:"political_affiliation"`
	Site                 BasicInfoField `json:"site"`
	UpdatedAt            BasicInfoField `json:"updated_at"`
	Wechat               BasicInfoField `json:"wechat"`
	Weight               BasicInfoField `json:"weight"`
}

// BasicInfo 基本信息完整结构
type BasicInfo struct {
	ID         string        `json:"id"`
	Name       string        `json:"name"`
	Type       string        `json:"type"`
	IsVisible  bool          `json:"is_visible"`
	IsRequired bool          `json:"is_required"`
	SupportAI  bool          `json:"support_ai"`
	Index      int           `json:"index"`
	Item       BasicInfoItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到BasicInfo
func (b *BasicInfo) Scan(value interface{}) error {
	if value == nil {
		*b = BasicInfo{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, b)
}

// Value 实现 driver.Valuer 接口，将BasicInfo转换为JSON存入数据库
func (b BasicInfo) Value() (driver.Value, error) {
	return json.Marshal(b)
}

// EducationItem 教育经历项目结构
type EducationItem struct {
	ID          string         `json:"id"`
	SchoolName  BasicInfoField `json:"school_name"`
	CollegeName BasicInfoField `json:"college_name"`
	Major       BasicInfoField `json:"major"`
	Degree      BasicInfoField `json:"degree"`
	City        BasicInfoField `json:"city"`
	StartDate   BasicInfoField `json:"start_date"`
	EndDate     BasicInfoField `json:"end_date"`
	Description BasicInfoField `json:"description"`
	SchoolTags  BasicInfoField `json:"school_tags"`
	Index       int            `json:"index"`
}

// Education 教育经历完整结构
type Education struct {
	ID        string          `json:"id"`
	Name      string          `json:"name"`
	Type      string          `json:"type"`
	IsVisible bool            `json:"is_visible"`
	SupportAI bool            `json:"support_ai"`
	Index     int             `json:"index"`
	Item      []EducationItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Education
func (e *Education) Scan(value interface{}) error {
	if value == nil {
		*e = Education{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, e)
}

// Value 实现 driver.Valuer 接口，将Education转换为JSON存入数据库
func (e Education) Value() (driver.Value, error) {
	return json.Marshal(e)
}

// WorkItem 工作经历项目结构
type WorkItem struct {
	ID          string         `json:"id"`
	Company     BasicInfoField `json:"company"`
	Department  BasicInfoField `json:"department"`
	City        BasicInfoField `json:"city"`
	Job         BasicInfoField `json:"job"`
	StartMonth  BasicInfoField `json:"start_month"`
	EndMonth    BasicInfoField `json:"end_month"`
	Desc        BasicInfoField `json:"desc"`
	CompanyTags BasicInfoField `json:"company_tags"`
	JobTags     BasicInfoField `json:"job_tags"`
	Index       int            `json:"index"`
}

// Work 工作经历完整结构
type Work struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Type      string     `json:"type"`
	IsVisible bool       `json:"is_visible"`
	SupportAI bool       `json:"support_ai"`
	Index     int        `json:"index"`
	Item      []WorkItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Work
func (w *Work) Scan(value interface{}) error {
	if value == nil {
		*w = Work{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, w)
}

// Value 实现 driver.Valuer 接口，将Work转换为JSON存入数据库
func (w Work) Value() (driver.Value, error) {
	return json.Marshal(w)
}

// ProjectItem 项目经历项目结构
type ProjectItem struct {
	ID         string         `json:"id"`
	Name       BasicInfoField `json:"name"`
	Role       BasicInfoField `json:"role"`
	Company    BasicInfoField `json:"company"`
	StartMonth BasicInfoField `json:"start_month"`
	EndMonth   BasicInfoField `json:"end_month"`
	Desc       BasicInfoField `json:"desc"`
	Index      int            `json:"index"`
}

// Project 项目经历完整结构
type Project struct {
	ID        string        `json:"id"`
	Name      string        `json:"name"`
	Type      string        `json:"type"`
	IsVisible bool          `json:"is_visible"`
	SupportAI bool          `json:"support_ai"`
	Index     int           `json:"index"`
	Item      []ProjectItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Project
func (p *Project) Scan(value interface{}) error {
	if value == nil {
		*p = Project{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, p)
}

// Value 实现 driver.Valuer 接口，将Project转换为JSON存入数据库
func (p Project) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// ResearchItem 研究经历项目结构
type ResearchItem struct {
	ID         string         `json:"id"`
	Name       BasicInfoField `json:"name"`
	Role       BasicInfoField `json:"role"`
	Department BasicInfoField `json:"department"`
	City       BasicInfoField `json:"city"`
	StartMonth BasicInfoField `json:"start_month"`
	EndMonth   BasicInfoField `json:"end_month"`
	Desc       BasicInfoField `json:"desc"`
	Index      int            `json:"index"`
}

// Research 研究经历完整结构
type Research struct {
	ID        string         `json:"id"`
	Name      string         `json:"name"`
	Type      string         `json:"type"`
	IsVisible bool           `json:"is_visible"`
	SupportAI bool           `json:"support_ai"`
	Index     int            `json:"index"`
	Item      []ResearchItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Research
func (r *Research) Scan(value interface{}) error {
	if value == nil {
		*r = Research{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, r)
}

// Value 实现 driver.Valuer 接口，将Research转换为JSON存入数据库
func (r Research) Value() (driver.Value, error) {
	return json.Marshal(r)
}

// TeamItem 社团经历项目结构
type TeamItem struct {
	ID         string         `json:"id"`
	Name       BasicInfoField `json:"name"`
	Department BasicInfoField `json:"department"`
	Role       BasicInfoField `json:"role"`
	City       BasicInfoField `json:"city"`
	StartMonth BasicInfoField `json:"start_month"`
	EndMonth   BasicInfoField `json:"end_month"`
	Desc       BasicInfoField `json:"desc"`
	Index      int            `json:"index"`
}

// Team 社团经历完整结构
type Team struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Type      string     `json:"type"`
	IsVisible bool       `json:"is_visible"`
	SupportAI bool       `json:"support_ai"`
	Index     int        `json:"index"`
	Item      []TeamItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Team
func (t *Team) Scan(value interface{}) error {
	if value == nil {
		*t = Team{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, t)
}

// Value 实现 driver.Valuer 接口，将Team转换为JSON存入数据库
func (t Team) Value() (driver.Value, error) {
	return json.Marshal(t)
}

// PortfolioItem 作品集项目结构
type PortfolioItem struct {
	ID    string         `json:"id"`
	Name  BasicInfoField `json:"name"`
	URL   BasicInfoField `json:"url"`
	Index int            `json:"index"`
}

// Portfolio 作品集完整结构
type Portfolio struct {
	ID        string          `json:"id"`
	Name      string          `json:"name"`
	Type      string          `json:"type"`
	IsVisible bool            `json:"is_visible"`
	SupportAI bool            `json:"support_ai"`
	Index     int             `json:"index"`
	Item      []PortfolioItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Portfolio
func (p *Portfolio) Scan(value interface{}) error {
	if value == nil {
		*p = Portfolio{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, p)
}

// Value 实现 driver.Valuer 接口，将Portfolio转换为JSON存入数据库
func (p Portfolio) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// OtherItem 其他模块项目结构
type OtherItem struct {
	ID    string         `json:"id"`
	Name  BasicInfoField `json:"name"`
	Desc  BasicInfoField `json:"desc"`
	Index int            `json:"index"`
}

// Other 其他模块完整结构
type Other struct {
	ID        string      `json:"id"`
	Name      string      `json:"name"`
	Type      string      `json:"type"`
	IsVisible bool        `json:"is_visible"`
	SupportAI bool        `json:"support_ai"`
	Index     int         `json:"index"`
	Item      []OtherItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Other
func (o *Other) Scan(value interface{}) error {
	if value == nil {
		*o = Other{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, o)
}

// Value 实现 driver.Valuer 接口，将Other转换为JSON存入数据库
func (o Other) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// PersonalSummaryItem 个人总结项目结构
type PersonalSummaryItem struct {
	Summary BasicInfoField `json:"summary"`
}

// PersonalSummary 个人总结完整结构
type PersonalSummary struct {
	ID        string              `json:"id"`
	Name      string              `json:"name"`
	Type      string              `json:"type"`
	IsVisible bool                `json:"is_visible"`
	SupportAI bool                `json:"support_ai"`
	Index     int                 `json:"index"`
	Item      PersonalSummaryItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到PersonalSummary
func (ps *PersonalSummary) Scan(value interface{}) error {
	if value == nil {
		*ps = PersonalSummary{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, ps)
}

// Value 实现 driver.Valuer 接口，将PersonalSummary转换为JSON存入数据库
func (ps PersonalSummary) Value() (driver.Value, error) {
	return json.Marshal(ps)
}

// HonorValue 荣誉值结构
type HonorValue struct {
	Name  BasicInfoField `json:"name"`
	Desc  BasicInfoField `json:"desc"`
	Index int            `json:"index"`
}

// HonorsItem 荣誉栏项目结构
type HonorsItem struct {
	HonorWallLayout BasicInfoField `json:"honorWallLayout"`
	HonorWallStyle  BasicInfoField `json:"honorWallStyle"`
	Values          BasicInfoField `json:"values"`
}

// Honors 荣誉栏完整结构
type Honors struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Type      string     `json:"type"`
	IsVisible bool       `json:"is_visible"`
	SupportAI bool       `json:"support_ai"`
	Index     int        `json:"index"`
	Item      HonorsItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Honors
func (h *Honors) Scan(value interface{}) error {
	if value == nil {
		*h = Honors{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, h)
}

// Value 实现 driver.Valuer 接口，将Honors转换为JSON存入数据库
func (h Honors) Value() (driver.Value, error) {
	return json.Marshal(h)
}

// SkillValue 技能值结构
type SkillValue struct {
	Name  BasicInfoField `json:"name"`
	Score BasicInfoField `json:"score"`
	Desc  BasicInfoField `json:"desc"`
	Index int            `json:"index"`
}

// SkillsItem 技能条项目结构
type SkillsItem struct {
	SkillLayout BasicInfoField `json:"skillLayout"`
	SkillStyle  BasicInfoField `json:"skillStyle"`
	Values      BasicInfoField `json:"values"`
}

// Skills 技能条完整结构
type Skills struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Type      string     `json:"type"`
	IsVisible bool       `json:"is_visible"`
	SupportAI bool       `json:"support_ai"`
	Index     int        `json:"index"`
	Item      SkillsItem `json:"item"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Skills
func (s *Skills) Scan(value interface{}) error {
	if value == nil {
		*s = Skills{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, s)
}

// Value 实现 driver.Valuer 接口，将Skills转换为JSON存入数据库
func (s Skills) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// CustomModuleItem 自定义模块项目结构
type CustomModuleItem struct {
	ID         string         `json:"id"`
	Name       BasicInfoField `json:"name"`
	Role       BasicInfoField `json:"role"`
	StartMonth BasicInfoField `json:"start_month"`
	EndMonth   BasicInfoField `json:"end_month"`
	Desc       BasicInfoField `json:"desc"`
	Index      int            `json:"index"`
}

// CustomModule 自定义模块结构
type CustomModule struct {
	ID    string             `json:"id"`
	Name  string             `json:"name"`
	Index int                `json:"index"`
	Items []CustomModuleItem `json:"items"`
}

// CustomModules 自定义模块数组类型，用于JSON格式存储
type CustomModules []CustomModule

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到CustomModules
func (cm *CustomModules) Scan(value interface{}) error {
	if value == nil {
		*cm = CustomModules{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, cm)
}

// Value 实现 driver.Valuer 接口，将CustomModules转换为JSON存入数据库
func (cm CustomModules) Value() (driver.Value, error) {
	if cm == nil {
		return "[]", nil
	}
	return json.Marshal(cm)
}

// Slogan 简历标语结构
type Slogan struct {
	Title  BasicInfoField `json:"title"`
	Slogan BasicInfoField `json:"slogan"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到Slogan
func (s *Slogan) Scan(value interface{}) error {
	if value == nil {
		*s = Slogan{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, s)
}

// Value 实现 driver.Valuer 接口，将Slogan转换为JSON存入数据库
func (s Slogan) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// ResumeStyle 简历样式配置
type ResumeStyle struct {
	// 字体相关
	FontFamily string `json:"font_family"`
	FontSize   string `json:"font_size"`
	Color      string `json:"color"`
	FontGray   string `json:"font_gray"`

	// 主题色相关
	ResumeColor        string     `json:"resume_color"`
	ResumeColor2       string     `json:"resume_color2"`
	ColorCount         int        `json:"color_count"`
	PresetColorsSingle []string   `json:"preset_colors_single"`
	PresetColorsDual   [][]string `json:"preset_colors_dual"`

	// 布局相关
	ModuleSpacing string `json:"module_spacing"`
	LineSpacing   string `json:"line_spacing"`
	PageMargin    string `json:"page_margin"`
	BaseInfo      string `json:"base_info"`     // 'text' | 'icon' | 'simple'
	HeaderLayout  string `json:"header_layout"` // 'left' | 'center' | 'right'
	PaperStyle    string `json:"paper_style"`
	TitleStyle    string `json:"title_style"`
	TitleColor    string `json:"title_color"`
	AvatarLayout  string `json:"avatar_layout"` // 'left' | 'center' | 'right'
	LayoutMode    string `json:"layout_mode"`
	LeftBoxWidth  string `json:"left_box_width"`
	TitleRow      string `json:"title_row"`
	TitleAlign    string `json:"title_align"` // 'left' | 'center' | 'right' | 'justify'
	DateFormat    string `json:"date_format"`
	Separator     string `json:"separator"`
	DateAlign     string `json:"date_align"`   // 'left' | 'right'
	BadgeLayout   string `json:"badge_layout"` // 'left' | 'right'

	// 功能开关
	CanChangeHeaderLayout       bool `json:"can_change_header_layout"`
	CanChangeBaseInfo           bool `json:"can_change_base_info"`
	CanChangeAvatarLayout       bool `json:"can_change_avatar_layout"`
	CanChangeColor              bool `json:"can_change_color"`
	CanChangeTitleStyle         bool `json:"can_change_title_style"`
	CanChangeTitleAlign         bool `json:"can_change_title_align"`
	CanChangeBackgroundStyle    bool `json:"can_change_background_style"`
	CanChangeDateAlign          bool `json:"can_change_date_align"`
	CanChangeDateFormat         bool `json:"can_change_date_format"`
	CanChangePageMargin         bool `json:"can_change_page_margin"`
	CanChangeFontGray           bool `json:"can_change_font_gray"`
	CanChangeFontSize           bool `json:"can_change_font_size"`
	CanChangeFontFamily         bool `json:"can_change_font_family"`
	CanChangeModuleSpacing      bool `json:"can_change_module_spacing"`
	CanChangeLineSpacing        bool `json:"can_change_line_spacing"`
	CanChangeSkillsThreeColumns bool `json:"can_change_skills_three_columns"`
}

// Scan 实现 sql.Scanner 接口，从数据库读取JSON数据到ResumeStyle
func (rs *ResumeStyle) Scan(value interface{}) error {
	if value == nil {
		*rs = ResumeStyle{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言失败，无法将数据库值转换为[]byte")
	}

	return json.Unmarshal(bytes, rs)
}

// Value 实现 driver.Valuer 接口，将ResumeStyle转换为JSON存入数据库
func (rs ResumeStyle) Value() (driver.Value, error) {
	return json.Marshal(rs)
}
