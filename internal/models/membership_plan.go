package models

import (
	"github.com/avrilko/resume-server/internal/enum"
)

// MembershipPlan 用户会员套餐表
type MembershipPlan struct {
	ID              uint                  `json:"id" gorm:"primaryKey"`
	Name            string                `json:"name" gorm:"size:50;not null;default:'';comment:套餐名称"`
	ActualPrice     float64               `json:"actual_price" gorm:"type:decimal(10,2);not null;comment:实际价格(单位:元)"`
	OriginalPrice   float64               `json:"original_price" gorm:"type:decimal(10,2);not null;comment:原价(单位:元)"`
	Description     StringArray           `json:"description" gorm:"type:json;not null;default:(JSON_ARRAY());comment:描述信息(JSON格式的数组)"`
	ResumeLimit     int                   `json:"resume_limit" gorm:"type:int;not null;default:0;comment:简历数量限制 (0表示无限制)"`
	AiGenerateLimit int                   `json:"ai_generate_limit" gorm:"type:int;not null;default:0;comment:AI生成次数限制 (0表示无限制)"`
	AiRewriteLimit  int                   `json:"ai_rewrite_limit" gorm:"type:int;not null;default:0;comment:AI改写次数限制 (0表示无限制)"`
	AiOptimizeLimit int                   `json:"ai_optimize_limit" gorm:"type:int;not null;default:0;comment:AI简历优化次数限制 (0表示无限制)"`
	AiDiagnoseLimit int                   `json:"ai_diagnose_limit" gorm:"type:int;not null;default:0;comment:AI简历打分次数限制 (0表示无限制)"`
	AiOneClickLimit int                   `json:"ai_one_click_limit" gorm:"type:int;not null;default:0;comment:AI一键生成简历次数限制 (0表示无限制)"`
	Duration        string                `json:"duration" gorm:"size:50;not null;default:'';comment:套餐时长(格式:{年}-{月}-{日},如:1-0-0表示1年,0-1-0表示1个月,0-0-7表示7天,permanent表示永久)"`
	CornerImageUrl  string                `json:"corner_image_url" gorm:"size:255;default:'';comment:右上角图片地址"`
	DiscountTip     string                `json:"discount_tip" gorm:"size:100;default:'';comment:优惠提示(如:限时优惠,新用户专享)"`
	Visibility      enum.VisibilityStatus `json:"visibility" gorm:"type:tinyint(1);not null;default:1;comment:可见性 1:可见 2:隐藏"`
	Sort            int                   `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序(值越大越靠前)"`
	IsDefault       enum.IsDefault        `json:"is_default" gorm:"type:tinyint(1);not null;default:1;comment:是否默认 1:非默认 2:默认"`
	PlanType        enum.PlanType         `json:"plan_type" gorm:"type:tinyint(1);not null;default:1;index;comment:套餐类型 1:会员套餐 2:下载券套餐"`
	Base
}

// TableName 指定表名
func (MembershipPlan) TableName() string {
	return "membership_plan"
}
