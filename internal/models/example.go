package models

import "github.com/avrilko/resume-server/internal/enum"

// Example 示例表
type Example struct {
	ID              uint   `json:"id" gorm:"primaryKey"`
	TemplateID      uint   `json:"template_id" gorm:"not null;default:0;index;comment:模板ID"`
	MajorID         uint   `json:"major_id" gorm:"not null;default:0;index;comment:专业ID"`
	HotTemplateID   uint   `json:"hot_template_id" gorm:"not null;default:0;index;comment:热门模板ID"`
	Name            string `json:"name" gorm:"size:100;not null;default:'';comment:示例名称"`
	PreviewImageUrl string `json:"preview_image_url" gorm:"size:500;not null;default:'';comment:预览图链接"`
	UsageCount      int    `json:"usage_count" gorm:"type:int;not null;default:0;index;comment:使用人数"`
	Sort            int    `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序字段"`

	// 分类字段
	FirstCategoryID    uint            `json:"first_category_id" gorm:"not null;default:0;index;comment:一级分类ID"`
	FirstCategoryName  string          `json:"first_category_name" gorm:"size:100;not null;default:'';index;comment:一级分类名称"`
	SecondCategoryID   uint            `json:"second_category_id" gorm:"not null;default:0;index;comment:二级分类ID"`
	SecondCategoryName string          `json:"second_category_name" gorm:"size:100;not null;default:'';index;comment:二级分类名称"`
	ThirdCategoryID    uint            `json:"third_category_id" gorm:"not null;default:0;index;comment:三级分类ID"`
	ThirdCategoryName  string          `json:"third_category_name" gorm:"size:100;not null;default:'';index;comment:三级分类名称"`
	IsInternship       bool            `json:"is_internship" gorm:"not null;default:false;index;comment:是否是实习"`
	DataStatus         enum.DataStatus `json:"data_status" gorm:"type:tinyint(1);not null;default:1;index;comment:数据状态 1:没数据 2:处理中 3:有数据"`

	SeoTitle       string      `json:"seo_title" gorm:"size:255;not null;default:'';comment:SEO标题"`
	SeoKeywords    StringArray `json:"seo_keywords" gorm:"type:json;not null;default:(JSON_ARRAY());comment:SEO关键词(JSON格式的数组)"`
	SeoDescription string      `json:"seo_description" gorm:"size:500;not null;default:'';comment:SEO描述"`

	// URL相关字段
	SlugCn string `json:"slug_cn" gorm:"size:100;not null;default:'';index;comment:中文URL标识"`
	SlugEn string `json:"slug_en" gorm:"size:100;not null;default:'';index;comment:英文URL标识"`
	Path   string `json:"path" gorm:"size:200;not null;default:'';comment:完整路径"`

	// 简历内容字段（来自ResumeDetail，排除ResumeStyle、UserID、ResumeID）
	BasicInfo       BasicInfo       `json:"basic_info" gorm:"type:json;not null;default:(JSON_OBJECT());comment:基本信息(JSON格式的对象)"`
	Education       Education       `json:"education" gorm:"type:json;not null;default:(JSON_OBJECT());comment:教育经历(JSON格式的对象)"`
	Work            Work            `json:"work" gorm:"type:json;not null;default:(JSON_OBJECT());comment:工作经历(JSON格式的对象)"`
	Project         Project         `json:"project" gorm:"type:json;not null;default:(JSON_OBJECT());comment:项目经历(JSON格式的对象)"`
	Research        Research        `json:"research" gorm:"type:json;not null;default:(JSON_OBJECT());comment:研究经历(JSON格式的对象)"`
	Team            Team            `json:"team" gorm:"type:json;not null;default:(JSON_OBJECT());comment:社团经历(JSON格式的对象)"`
	Portfolio       Portfolio       `json:"portfolio" gorm:"type:json;not null;default:(JSON_OBJECT());comment:作品集(JSON格式的对象)"`
	Other           Other           `json:"other" gorm:"type:json;not null;default:(JSON_OBJECT());comment:其他模块(JSON格式的对象)"`
	PersonalSummary PersonalSummary `json:"personal_summary" gorm:"type:json;not null;default:(JSON_OBJECT());comment:个人总结(JSON格式的对象)"`
	Honors          Honors          `json:"honors" gorm:"type:json;not null;default:(JSON_OBJECT());comment:荣誉栏(JSON格式的对象)"`
	Skills          Skills          `json:"skills" gorm:"type:json;not null;default:(JSON_OBJECT());comment:技能条(JSON格式的对象)"`
	CustomModules   CustomModules   `json:"custom_modules" gorm:"type:json;not null;default:(JSON_ARRAY());comment:自定义模块(JSON格式的数组)"`
	Slogan          Slogan          `json:"slogan" gorm:"type:json;not null;default:(JSON_OBJECT());comment:简历标语(JSON格式的对象)"`

	Base

	// 关联（非外键）
	Template Template `json:"template" gorm:"-"`
}

// TableName 指定表名
func (Example) TableName() string {
	return "example"
}
