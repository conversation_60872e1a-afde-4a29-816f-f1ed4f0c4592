package models

import (
	"github.com/avrilko/resume-server/internal/enum"
)

// Order 订单表
type Order struct {
	ID               uint               `json:"id" gorm:"primaryKey"`
	UserID           uint               `json:"user_id" gorm:"not null;index;default:0;comment:用户ID"`
	MembershipPlanID uint               `json:"membership_plan_id" gorm:"not null;index;default:0;comment:套餐ID"`
	PaymentMethod    enum.PaymentMethod `json:"payment_method" gorm:"type:tinyint(1);not null;default:1;index;comment:支付方式 1:微信支付 2:支付宝"`
	Amount           float64            `json:"amount" gorm:"type:decimal(10,2);not null;default:0.00;comment:支付金额(单位:元)"`
	PaymentStatus    enum.PaymentStatus `json:"payment_status" gorm:"type:tinyint(1);not null;default:1;index;comment:支付状态 1:待支付 2:支付处理中 3:支付成功 4:支付失败 5:支付超时"`
	OrderNo          string             `json:"order_no" gorm:"size:64;not null;unique;index;default:'';comment:订单号"`
	FailReason       string             `json:"fail_reason" gorm:"size:255;default:'';comment:支付失败原因"`
	Title            string             `json:"title" gorm:"size:100;not null;default:'';comment:订单标题"`
	CallbackMessage  string             `json:"callback_message" gorm:"size:2048;not null;default:'';comment:回调消息"`
	CodeURL          string             `json:"code_url" gorm:"size:255;not null;default:'';comment:支付二维码链接"`
	BdVid            string             `json:"bd_vid" gorm:"size:255;not null;default:'';comment:百度投放ID"`
	Base

	// 关联（非外键）
	User           User           `json:"user" gorm:"-"`
	MembershipPlan MembershipPlan `json:"membership_plan" gorm:"-"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "order"
}
