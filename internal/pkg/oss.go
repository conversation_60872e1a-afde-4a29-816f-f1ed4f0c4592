package pkg

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	appconfig "github.com/avrilko/resume-server/config"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.uber.org/zap"
)

// OSSService OSS服务接口
type OSSService interface {
	// UploadBytes 上传文件字节数据
	UploadBytes(ctx context.Context, fileBytes []byte, objectName string, contentType string) (string, error)
	// DeleteFile 删除文件
	DeleteFile(ctx context.Context, objectName string) error
	// GetObjectURL 获取对象的URL
	GetObjectURL(objectName string) string
}

// ossService OSS服务实现
type ossService struct {
	client     *cos.Client
	bucketName string
	selfUrl    string
	logger     *zap.Logger
}

// NewOSSService 创建OSS服务
func NewOSSService(cfg *appconfig.Config, logger *zap.Logger) (OSSService, error) {
	// 记录S3配置信息用于调试
	logger.Info("腾讯云COS配置信息",
		zap.String("AccessKeyID", cfg.S3.AccessKeyID),
		zap.String("AccessKeySecret", "***隐藏***"),
		zap.String("Endpoint", cfg.S3.Endpoint),
		zap.String("BucketName", cfg.S3.BucketName),
		zap.String("Region", cfg.S3.Region),
		zap.String("SelfUrl", cfg.S3.SelfUrl),
	)

	// 验证必要的配置
	if cfg.S3.AccessKeyID == "" {
		return nil, fmt.Errorf("S3 AccessKeyID cannot be empty")
	}
	if cfg.S3.AccessKeySecret == "" {
		return nil, fmt.Errorf("S3 AccessKeySecret cannot be empty")
	}
	if cfg.S3.Endpoint == "" {
		return nil, fmt.Errorf("S3 endpoint cannot be empty")
	}
	if cfg.S3.BucketName == "" {
		return nil, fmt.Errorf("S3 BucketName cannot be empty")
	}

	// 解析存储桶URL
	u, err := url.Parse(cfg.S3.Endpoint)
	if err != nil {
		return nil, fmt.Errorf("invalid S3 endpoint URL: %w", err)
	}

	// 创建腾讯云COS客户端
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  cfg.S3.AccessKeyID,
			SecretKey: cfg.S3.AccessKeySecret,
		},
	})

	return &ossService{
		client:     client,
		bucketName: cfg.S3.BucketName,
		selfUrl:    cfg.S3.SelfUrl,
		logger:     logger,
	}, nil
}

// UploadBytes 上传文件字节数据
func (s *ossService) UploadBytes(ctx context.Context, fileBytes []byte, objectName string, contentType string) (string, error) {
	// 上传文件
	_, err := s.client.Object.Put(ctx, objectName, strings.NewReader(string(fileBytes)), &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType: contentType,
		},
		ACLHeaderOptions: &cos.ACLHeaderOptions{
			XCosACL: "public-read",
		},
	})
	if err != nil {
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	// 返回文件访问URL
	fileUrl := s.GetObjectURL(objectName)

	s.logger.Info("文件上传成功",
		zap.String("bucket", s.bucketName),
		zap.String("objectName", objectName),
		zap.String("url", fileUrl),
	)

	return fileUrl, nil
}

// GetObjectURL 获取对象的URL
func (s *ossService) GetObjectURL(objectName string) string {
	return fmt.Sprintf("%s/%s", s.selfUrl, objectName)
}

// DeleteFile 删除文件
func (s *ossService) DeleteFile(ctx context.Context, objectName string) error {
	_, err := s.client.Object.Delete(ctx, objectName)
	if err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}

	s.logger.Info("文件删除成功",
		zap.String("bucket", s.bucketName),
		zap.String("objectName", objectName),
	)

	return nil
}
