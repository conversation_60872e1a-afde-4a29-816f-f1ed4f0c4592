package pkg

import (
	"fmt"
	"log"
	"os"

	"github.com/avrilko/resume-server/config"
	"github.com/meilisearch/meilisearch-go"
)

// MeiliSearchService MeiliSearch服务
type MeiliSearchService struct {
	client meilisearch.ServiceManager
}

// SearchResult 搜索结果结构
type SearchResult struct {
	ID     string `json:"id"`
	Type   int    `json:"type"`    // 1为position 2为example
	Name   string `json:"name"`    // position的名称或者example表name
	SlugCn string `json:"slug_cn"` // slug_cn字段
	URL    string `json:"url"`     // position为/jianli/{slug_cn} example为/jianli/{id}.html
}

// SearchDocument 搜索文档结构
type SearchDocument struct {
	ID     string `json:"id"`
	Type   int    `json:"type"`    // 1为position 2为example
	Name   string `json:"name"`    // position的名称或者example表name
	SlugCn string `json:"slug_cn"` // slug_cn字段
	URL    string `json:"url"`     // position为/jianli/{slug_cn} example为/jianli/{id}.html
}

// SearchResponse 搜索响应结构
type SearchResponse struct {
	Hits           []SearchResult `json:"hits"`
	Query          string         `json:"query"`
	ProcessingTime int64          `json:"processingTimeMs"`
	Limit          int            `json:"limit"`
	Offset         int            `json:"offset"`
	EstimatedTotal int64          `json:"estimatedTotalHits"`
}

// NewMeiliSearchService 创建MeiliSearch服务实例
func NewMeiliSearchService(cfg *config.Config) *MeiliSearchService {
	// 从配置文件获取MeiliSearch配置
	host := cfg.MeiliSearch.Host
	if host == "" {
		// 如果配置文件中没有，则从环境变量获取
		host = os.Getenv("MEILISEARCH_HOST")
	}

	apiKey := cfg.MeiliSearch.APIKey
	if apiKey == "" {
		// 如果配置文件中没有，则从环境变量获取
		apiKey = os.Getenv("MEILISEARCH_API_KEY")
	}

	// 创建MeiliSearch客户端
	client := meilisearch.New(host, meilisearch.WithAPIKey(apiKey))

	return &MeiliSearchService{
		client: client,
	}
}

// Search 搜索职位关键字，最多返回10条
func (s *MeiliSearchService) Search(keyword string) (*SearchResponse, error) {
	if keyword == "" {
		return &SearchResponse{
			Hits:           []SearchResult{},
			Query:          keyword,
			ProcessingTime: 0,
			Limit:          10,
			Offset:         0,
			EstimatedTotal: 0,
		}, nil
	}

	// 获取positions索引
	index := s.client.Index("positions")

	// 构建搜索请求
	searchRequest := &meilisearch.SearchRequest{
		Query:  keyword,
		Limit:  10,
		Offset: 0,
		// 移除 AttributesToRetrieve 以返回所有字段
		AttributesToHighlight: []string{"name", "slug_cn"},
		HighlightPreTag:       "<mark>",
		HighlightPostTag:      "</mark>",
	}

	// 执行搜索
	searchResult, err := index.Search(keyword, searchRequest)
	if err != nil {
		log.Printf("MeiliSearch搜索失败: %v", err)
		return nil, fmt.Errorf("搜索失败: %v", err)
	}

	// 转换搜索结果
	var hits []SearchResult
	for _, hit := range searchResult.Hits {
		// 将interface{}转换为map[string]interface{}
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		result := SearchResult{}

		if id, ok := hitMap["id"].(string); ok {
			result.ID = id
		}
		if typeVal, ok := hitMap["type"].(float64); ok {
			result.Type = int(typeVal)
		}
		if name, ok := hitMap["name"].(string); ok {
			result.Name = name
		}
		if slugCn, ok := hitMap["slug_cn"].(string); ok {
			result.SlugCn = slugCn
		}
		if url, ok := hitMap["url"].(string); ok {
			result.URL = url
		}

		hits = append(hits, result)
	}

	response := &SearchResponse{
		Hits:           hits,
		Query:          keyword,
		ProcessingTime: searchResult.ProcessingTimeMs,
		Limit:          10,
		Offset:         0,
		EstimatedTotal: searchResult.EstimatedTotalHits,
	}

	return response, nil
}

// ClearAllDocuments 清空positions索引中的所有文档
func (s *MeiliSearchService) ClearAllDocuments() error {
	// 获取positions索引
	index := s.client.Index("positions")

	// 删除所有文档
	task, err := index.DeleteAllDocuments()
	if err != nil {
		log.Printf("MeiliSearch清空所有文档失败: %v", err)
		return fmt.Errorf("清空所有文档失败: %v", err)
	}

	log.Printf("MeiliSearch清空所有文档任务ID: %d", task.TaskUID)
	return nil
}

// SetSearchableAttributes 设置可搜索字段
func (s *MeiliSearchService) SetSearchableAttributes(attributes []string) error {
	// 获取positions索引
	index := s.client.Index("positions")

	// 设置可搜索字段
	task, err := index.UpdateSearchableAttributes(&attributes)
	if err != nil {
		log.Printf("MeiliSearch设置可搜索字段失败: %v", err)
		return fmt.Errorf("设置可搜索字段失败: %v", err)
	}

	log.Printf("MeiliSearch设置可搜索字段任务ID: %d, 字段: %v", task.TaskUID, attributes)
	return nil
}

// AddDocuments 批量添加文档到MeiliSearch
func (s *MeiliSearchService) AddDocuments(documents []SearchDocument) error {
	if len(documents) == 0 {
		return nil
	}

	// 获取positions索引
	index := s.client.Index("positions")

	// 批量添加文档
	task, err := index.AddDocuments(documents)
	if err != nil {
		log.Printf("MeiliSearch批量添加文档失败: %v", err)
		return fmt.Errorf("批量添加文档失败: %v", err)
	}

	log.Printf("MeiliSearch批量添加文档任务ID: %d, 文档数量: %d", task.TaskUID, len(documents))
	return nil
}

// HealthCheck 健康检查
func (s *MeiliSearchService) HealthCheck() error {
	// 尝试获取索引信息来检查连接
	_, err := s.client.GetIndex("positions")
	if err != nil {
		return fmt.Errorf("MeiliSearch连接失败: %v", err)
	}
	return nil
}
