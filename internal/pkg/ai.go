package pkg

import (
	"context"
	"errors"
	"fmt"
	"io"

	"github.com/avrilko/resume-server/config"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/utils"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"go.uber.org/zap"
)

// AIService AI服务接口
type AIService interface {
	// ChatCompletion 单轮对话
	ChatCompletion(ctx context.Context, messages []*ChatMessage) (*ChatResponse, error)

	// ChatCompletionStream 流式对话
	ChatCompletionStream(ctx context.Context, messages []*ChatMessage) (*ChatStreamResponse, error)

	// ChatWithHistory 多轮对话
	ChatWithHistory(ctx context.Context, messages []*ChatMessage, history []*ChatMessage) (*ChatResponse, error)

	// ChatWithHistoryStream 多轮流式对话
	ChatWithHistoryStream(ctx context.Context, messages []*ChatMessage, history []*ChatMessage) (*ChatStreamResponse, error)
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role    string `json:"role"`    // system, user, assistant
	Content string `json:"content"` // 消息内容
}

// ChatResponse 聊天响应
type ChatResponse struct {
	Content      string                 `json:"content"`       // 回复内容
	Usage        *Usage                 `json:"usage"`         // 使用量统计
	Model        string                 `json:"model"`         // 使用的模型
	RequestID    string                 `json:"request_id"`    // 请求ID
	FinishReason string                 `json:"finish_reason"` // 结束原因
	Extra        map[string]interface{} `json:"extra"`         // 额外信息
}

// ChatStreamResponse 流式聊天响应
type ChatStreamResponse struct {
	Stream chan *StreamChunk // 流式数据通道
	Close  func()            // 关闭函数
}

// StreamChunk 流式数据块
type StreamChunk struct {
	Content      string                 `json:"content"`       // 增量内容
	Done         bool                   `json:"done"`          // 是否完成
	Usage        *Usage                 `json:"usage"`         // 使用量统计
	Model        string                 `json:"model"`         // 使用的模型
	RequestID    string                 `json:"request_id"`    // 请求ID
	FinishReason string                 `json:"finish_reason"` // 结束原因
	Error        error                  `json:"error"`         // 错误信息
	Extra        map[string]interface{} `json:"extra"`         // 额外信息
}

// Usage 使用量统计
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`     // 输入token数
	CompletionTokens int `json:"completion_tokens"` // 输出token数
	TotalTokens      int `json:"total_tokens"`      // 总token数
}

// aiService AI服务实现
type aiService struct {
	client  *arkruntime.Client
	modelID string
	logger  *zap.Logger
	config  *config.Config
}

// NewAIService 创建AI服务
func NewAIService(cfg *config.Config, logger *zap.Logger) (AIService, error) {
	if cfg.AI.APIKey == "" {
		return nil, fmt.Errorf("AI API Key 未配置")
	}

	if cfg.AI.ModelID == "" {
		return nil, fmt.Errorf("AI Model ID 未配置")
	}

	// 创建客户端
	client := arkruntime.NewClientWithApiKey(cfg.AI.APIKey)

	return &aiService{
		client:  client,
		modelID: cfg.AI.ModelID,
		logger:  logger,
		config:  cfg,
	}, nil
}

// ChatCompletion 单轮对话
func (s *aiService) ChatCompletion(ctx context.Context, messages []*ChatMessage) (*ChatResponse, error) {
	if len(messages) == 0 {
		return nil, fmt.Errorf("消息不能为空")
	}

	// 转换消息格式
	chatMessages := s.convertMessages(messages)

	// 构建请求
	req := model.CreateChatCompletionRequest{
		Model:    s.modelID,
		Messages: chatMessages,
		// 关闭深度思考功能（针对豆包思维链模型）
		Thinking: &model.Thinking{
			Type: model.ThinkingTypeDisabled,
		},
	}

	// 发送请求
	resp, err := s.client.CreateChatCompletion(ctx, req)
	if err != nil {
		s.logger.Error("AI聊天请求失败", zap.Error(err))
		return nil, fmt.Errorf("AI聊天请求失败: %w", err)
	}

	// 构建响应
	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("AI响应为空")
	}

	choice := resp.Choices[0]
	response := &ChatResponse{
		Content:      *choice.Message.Content.StringValue,
		Model:        resp.Model,
		RequestID:    resp.ID,
		FinishReason: string(choice.FinishReason),
	}

	// 添加使用量统计
	response.Usage = &Usage{
		PromptTokens:     resp.Usage.PromptTokens,
		CompletionTokens: resp.Usage.CompletionTokens,
		TotalTokens:      resp.Usage.TotalTokens,
	}

	return response, nil
}

// ChatCompletionStream 流式对话
func (s *aiService) ChatCompletionStream(ctx context.Context, messages []*ChatMessage) (*ChatStreamResponse, error) {
	if len(messages) == 0 {
		return nil, fmt.Errorf("消息不能为空")
	}

	// 转换消息格式
	chatMessages := s.convertMessages(messages)

	// 构建请求
	req := model.CreateChatCompletionRequest{
		Model:    s.modelID,
		Messages: chatMessages,
		// 关闭深度思考功能（针对豆包思维链模型）
		Thinking: &model.Thinking{
			Type: model.ThinkingTypeDisabled,
		},
	}

	// 发送流式请求
	stream, err := s.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		s.logger.Error("AI流式聊天请求失败", zap.Error(err))
		return nil, fmt.Errorf("AI流式聊天请求失败: %w", err)
	}

	// 创建响应通道
	streamChan := make(chan *StreamChunk, 100)

	// 启动goroutine处理流式数据
	go s.handleStream(stream, streamChan)

	return &ChatStreamResponse{
		Stream: streamChan,
		Close: func() {
			stream.Close()
			close(streamChan)
		},
	}, nil
}

// ChatWithHistory 多轮对话
func (s *aiService) ChatWithHistory(ctx context.Context, messages []*ChatMessage, history []*ChatMessage) (*ChatResponse, error) {
	// 合并历史消息和当前消息
	allMessages := make([]*ChatMessage, 0, len(history)+len(messages))
	allMessages = append(allMessages, history...)
	allMessages = append(allMessages, messages...)

	return s.ChatCompletion(ctx, allMessages)
}

// ChatWithHistoryStream 多轮流式对话
func (s *aiService) ChatWithHistoryStream(ctx context.Context, messages []*ChatMessage, history []*ChatMessage) (*ChatStreamResponse, error) {
	// 合并历史消息和当前消息
	allMessages := make([]*ChatMessage, 0, len(history)+len(messages))
	allMessages = append(allMessages, history...)
	allMessages = append(allMessages, messages...)

	return s.ChatCompletionStream(ctx, allMessages)
}

// convertMessages 转换消息格式
func (s *aiService) convertMessages(messages []*ChatMessage) []*model.ChatCompletionMessage {
	chatMessages := make([]*model.ChatCompletionMessage, len(messages))

	for i, msg := range messages {
		var role string
		switch msg.Role {
		case "system":
			role = model.ChatMessageRoleSystem
		case "user":
			role = model.ChatMessageRoleUser
		case "assistant":
			role = model.ChatMessageRoleAssistant
		default:
			role = model.ChatMessageRoleUser
		}

		chatMessages[i] = &model.ChatCompletionMessage{
			Role: role,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(msg.Content),
			},
		}
	}

	return chatMessages
}

// handleStream 处理流式数据
func (s *aiService) handleStream(stream *utils.ChatCompletionStreamReader, streamChan chan *StreamChunk) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("处理AI流式数据时发生panic", zap.Any("panic", r))
			streamChan <- &StreamChunk{
				Error: fmt.Errorf("处理流式数据时发生错误: %v", r),
				Done:  true,
			}
		}
	}()

	for {
		recv, err := stream.Recv()
		if err == io.EOF {
			// 流结束
			streamChan <- &StreamChunk{
				Done: true,
			}
			return
		}

		if err != nil {
			// 处理错误
			var apiErr *model.APIError
			if errors.As(err, &apiErr) {
				s.logger.Error("AI流式响应错误",
					zap.String("code", apiErr.Code),
					zap.String("message", apiErr.Message),
					zap.String("type", apiErr.Type))
			} else {
				s.logger.Error("AI流式响应错误", zap.Error(err))
			}

			streamChan <- &StreamChunk{
				Error: err,
				Done:  true,
			}
			return
		}

		// 处理正常数据
		if len(recv.Choices) > 0 {
			choice := recv.Choices[0]
			chunk := &StreamChunk{
				Content:      choice.Delta.Content,
				Model:        recv.Model,
				RequestID:    recv.ID,
				FinishReason: string(choice.FinishReason),
			}

			// 添加使用量统计
			if recv.Usage != nil {
				chunk.Usage = &Usage{
					PromptTokens:     recv.Usage.PromptTokens,
					CompletionTokens: recv.Usage.CompletionTokens,
					TotalTokens:      recv.Usage.TotalTokens,
				}
			}

			streamChan <- chunk
		}
	}
}
