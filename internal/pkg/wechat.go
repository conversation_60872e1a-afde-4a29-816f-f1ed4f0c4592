package pkg

import (
	"context"
	"fmt"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/qrCode/response"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount"
	"github.com/avrilko/resume-server/config"
)

// WeChatService 微信服务结构体
type WeChatService struct {
	officialAccount *officialAccount.OfficialAccount
}

// NewWeChatService 从配置创建微信服务
func NewWeChatService(conf *config.Config) (*WeChatService, error) {
	// 从配置中获取Redis配置
	redisAddr := fmt.Sprintf("%s:%d", conf.Redis.Host, conf.Redis.Port)

	// 调用基础构造函数
	return NewWeChatServiceWithParams(
		conf.WeChat.AppID,
		conf.WeChat.AppSecret,
		conf.WeChat.Token,
		"", // AESKey为空
		redisAddr,
		conf.Redis.Password,
		conf.Redis.DB,
	)
}

// NewWeChatServiceWithParams 创建微信服务，使用传入的配置
func NewWeChatServiceWithParams(appID, appSecret, token, aesKey string, redisAddr, redisPassword string, redisDB int) (*WeChatService, error) {
	// 创建公众号实例
	app, err := officialAccount.NewOfficialAccount(&officialAccount.UserConfig{
		AppID:        appID,
		Secret:       appSecret,
		Token:        token,
		AESKey:       aesKey,
		HttpDebug:    true, // 默认开启HTTP调试
		Debug:        true, // 默认开启调试模式
		ResponseType: "",   // 默认响应类型为空
		OAuth: officialAccount.OAuth{
			Callback: "",                          // 默认回调地址为空
			Scopes:   []string{"snsapi_userinfo"}, // 默认授权范围
		},
		Log: officialAccount.Log{
			Level:  "debug", // 默认日志级别为debug
			File:   "",      // 默认日志文件为空
			Stdout: true,    // 默认输出到标准输出
		},
		Cache: kernel.NewRedisClient(&kernel.UniversalOptions{
			Addrs:    []string{redisAddr},
			Password: redisPassword,
			DB:       redisDB,
		}),
	})

	if err != nil {
		return nil, err
	}

	return &WeChatService{
		officialAccount: app,
	}, nil
}

// CreateTempQrCode 创建临时二维码
// sceneValue: 场景值(整数或字符串)
// expireSeconds: 有效期，单位为秒，最大不超过2592000（30天）
func (s *WeChatService) CreateTempQrCode(ctx context.Context, sceneValue interface{}, expireSeconds int) (*response.ResponseQRCodeCreate, error) {
	if expireSeconds <= 0 {
		// 默认30分钟有效期
		expireSeconds = 1800
	} else if expireSeconds > 2592000 {
		// 最大不超过30天
		expireSeconds = 2592000
	}

	return s.officialAccount.QRCode.Temporary(ctx, sceneValue, expireSeconds)
}

// GetQrCodeUrl 根据ticket获取二维码图片URL
func (s *WeChatService) GetQrCodeUrl(ticket string) string {
	return s.officialAccount.QRCode.URL(ticket)
}

// CreateTempQrCodeAndGetUrl 一步创建临时二维码并返回URL
func (s *WeChatService) CreateTempQrCodeAndGetUrl(ctx context.Context, sceneValue interface{}, expireSeconds int) (string, error) {
	resp, err := s.CreateTempQrCode(ctx, sceneValue, expireSeconds)
	if err != nil {
		return "", err
	}

	return s.GetQrCodeUrl(resp.Ticket), nil
}

// GetOfficialAccount 获取公众号实例
func (s *WeChatService) GetOfficialAccount() *officialAccount.OfficialAccount {
	return s.officialAccount
}
