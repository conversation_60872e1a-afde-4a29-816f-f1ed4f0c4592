package pkg

import (
	"context"
	"fmt"
	"time"

	"github.com/avrilko/resume-server/config"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisClient 全局Redis客户端实例
var RedisClient *redis.Client

// InitRedis 初始化Redis连接
func InitRedis(cfg *config.Config) error {
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		// 连接池配置
		PoolSize:     10,
		MinIdleConns: 5,
		// 连接超时配置
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := RedisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("连接Redis失败: %w", err)
	}

	Info("Redis连接成功", zap.String("host", cfg.Redis.Host), zap.Int("port", cfg.Redis.Port))
	return nil
}

// GetRedis 获取Redis客户端
func GetRedis() *redis.Client {
	return RedisClient
}

// NewRedisClient 初始化并返回Redis客户端（用于依赖注入）
func NewRedisClient(cfg *config.Config) (*redis.Client, error) {
	// 如果已经初始化，则直接返回
	if RedisClient != nil {
		return RedisClient, nil
	}

	err := InitRedis(cfg)
	if err != nil {
		return nil, err
	}
	return RedisClient, nil
}

// CloseRedis 关闭Redis连接
func CloseRedis() error {
	if RedisClient != nil {
		if err := RedisClient.Close(); err != nil {
			return fmt.Errorf("关闭Redis连接失败: %w", err)
		}
		Info("Redis连接已关闭")
	}
	return nil
}
