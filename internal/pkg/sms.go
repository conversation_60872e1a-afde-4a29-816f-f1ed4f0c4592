package pkg

import (
	"encoding/json"
	"fmt"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	util "github.com/alibabacloud-go/tea-utils/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/avrilko/resume-server/config"
	"go.uber.org/zap"
)

// SMSService 短信服务接口
type SMSService interface {
	// SendSMS 发送短信
	SendSMS(phone, templateCode string, templateParam map[string]string) error

	// SendLoginCode 发送登录验证码
	SendLoginCode(phone, code string) error
}

// AliyunSMSService 阿里云短信服务实现
type AliyunSMSService struct {
	client        *dysmsapi.Client
	accessKeyID   string
	accessSecret  string
	signName      string
	loginTemplate string
	endpoint      string
}

// NewSMSService 创建短信服务
func NewSMSService(cfg *config.Config) (SMSService, error) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(cfg.SMS.AccessKeyID),
		AccessKeySecret: tea.String(cfg.SMS.AccessKeySecret),
		Endpoint:        tea.String(cfg.SMS.Endpoint),
	}

	client, err := dysmsapi.NewClient(config)
	if err != nil {
		Error("初始化阿里云短信客户端失败", zap.Error(err))
		return nil, err
	}

	return &AliyunSMSService{
		client:        client,
		accessKeyID:   cfg.SMS.AccessKeyID,
		accessSecret:  cfg.SMS.AccessKeySecret,
		signName:      cfg.SMS.SignName,
		loginTemplate: cfg.SMS.LoginTemplateCode,
		endpoint:      cfg.SMS.Endpoint,
	}, nil
}

// SendSMS 发送短信
func (s *AliyunSMSService) SendSMS(phone, templateCode string, templateParam map[string]string) error {
	// 将模板参数序列化为JSON字符串
	paramJSON, err := json.Marshal(templateParam)
	if err != nil {
		Error("短信模板参数序列化失败", zap.Error(err))
		return err
	}

	sendSmsRequest := &dysmsapi.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(s.signName),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String(string(paramJSON)),
	}

	runtime := &util.RuntimeOptions{}
	response, err := s.client.SendSmsWithOptions(sendSmsRequest, runtime)
	if err != nil {
		Error("发送短信请求失败", zap.Error(err), zap.String("phone", phone))
		return err
	}

	if *response.Body.Code != "OK" {
		Error("发送短信失败",
			zap.String("code", *response.Body.Code),
			zap.String("message", *response.Body.Message),
			zap.String("phone", phone))
		return fmt.Errorf("短信发送失败: %s", *response.Body.Message)
	}

	Info("短信发送成功",
		zap.String("phone", phone),
		zap.String("bizId", *response.Body.BizId),
		zap.String("requestId", *response.Body.RequestId))
	return nil
}

// SendLoginCode 发送登录验证码
func (s *AliyunSMSService) SendLoginCode(phone, code string) error {
	templateParam := map[string]string{
		"code": code,
	}
	return s.SendSMS(phone, s.loginTemplate, templateParam)
}
