//go:generate wire
//go:build wireinject
// +build wireinject

package wire

import (
	"fmt"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/controllers"
	"github.com/avrilko/resume-server/internal/cron"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/routes"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Application 应用程序结构体
type Application struct {
	Router      *routes.Router
	CronService cron.CronService
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.App.Debug {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return nil, fmt.Errorf("创建日志记录器失败: %w", err)
	}

	return logger, nil
}

var BaseSet = wire.NewSet(
	config.LoadConfig,
	ProvideLogger,
)

// BuildApplication 构建应用程序
func BuildApplication() (*routes.Router, error) {
	wire.Build(
		BaseSet,
		repository.RepositorySet,
		services.ServiceSet,
		pkg.PkgSet,
		controllers.ControllerSet,
		routes.NewRouter,
	)
	return nil, nil
}

// BuildApplicationWithCron 构建包含定时任务的应用程序
func BuildApplicationWithCron() (*Application, error) {
	wire.Build(
		BaseSet,
		repository.RepositorySet,
		services.ServiceSet,
		pkg.PkgSet,
		cron.CronSet,
		controllers.ControllerSet,
		routes.NewRouter,
		wire.Struct(new(Application), "*"),
	)
	return nil, nil
}

// ScriptService 脚本服务接口（在scripts包中定义）
type ScriptService interface {
	GetConfig() *config.Config
	GetLogger() *zap.Logger
	GetDB() *gorm.DB
	GetRedis() *redis.Client
	GetUserService() services.UserService
	GetResumeService() services.ResumeService
	GetPositionService() services.PositionService
	GetAIService() services.AIService
	GetOrderService() services.OrderService
	GetMembershipPlanService() services.MembershipPlanService
	GetExampleService() services.ExampleService
	GetUploadService() services.UploadService
	GetCodeService() services.CodeService
	GetEmailCodeService() services.EmailCodeService
	GetQrCodeService() services.QrCodeService
	GetJWTService() pkg.JWTService
	GetSMSService() pkg.SMSService
	GetOSSService() pkg.OSSService
	GetEmailService() pkg.EmailService
	GetChromeService() pkg.ChromeService
	GetMeiliSearchService() *pkg.MeiliSearchService
	GetPayService() *pkg.PayService
}

// BuildScriptService 构建脚本服务
func BuildScriptService() (ScriptService, error) {
	wire.Build(
		BaseSet,
		repository.RepositorySet,
		services.ServiceSet,
		pkg.PkgSet,
		NewScriptService,
	)
	return nil, nil
}

// NewScriptService 创建脚本服务的构造函数
func NewScriptService(
	config *config.Config,
	logger *zap.Logger,
	db *gorm.DB,
	redis *redis.Client,
	userService services.UserService,
	resumeService services.ResumeService,
	positionService services.PositionService,
	aiService services.AIService,
	orderService services.OrderService,
	membershipPlanService services.MembershipPlanService,
	exampleService services.ExampleService,
	uploadService services.UploadService,
	codeService services.CodeService,
	emailCodeService services.EmailCodeService,
	qrCodeService services.QrCodeService,
	jwtService pkg.JWTService,
	smsService pkg.SMSService,
	ossService pkg.OSSService,
	emailService pkg.EmailService,
	chromeService pkg.ChromeService,
	meiliSearchService *pkg.MeiliSearchService,
	payService *pkg.PayService,
) ScriptService {
	return &scriptServiceImpl{
		config:                config,
		logger:                logger,
		db:                    db,
		redis:                 redis,
		userService:           userService,
		resumeService:         resumeService,
		positionService:       positionService,
		aiService:             aiService,
		orderService:          orderService,
		membershipPlanService: membershipPlanService,
		exampleService:        exampleService,
		uploadService:         uploadService,
		codeService:           codeService,
		emailCodeService:      emailCodeService,
		qrCodeService:         qrCodeService,
		jwtService:            jwtService,
		smsService:            smsService,
		ossService:            ossService,
		emailService:          emailService,
		chromeService:         chromeService,
		meiliSearchService:    meiliSearchService,
		payService:            payService,
	}
}

// scriptServiceImpl 脚本服务实现
type scriptServiceImpl struct {
	config *config.Config
	logger *zap.Logger
	db     *gorm.DB
	redis  *redis.Client

	userService           services.UserService
	resumeService         services.ResumeService
	positionService       services.PositionService
	aiService             services.AIService
	orderService          services.OrderService
	membershipPlanService services.MembershipPlanService
	exampleService        services.ExampleService
	uploadService         services.UploadService
	codeService           services.CodeService
	emailCodeService      services.EmailCodeService
	qrCodeService         services.QrCodeService

	jwtService         pkg.JWTService
	smsService         pkg.SMSService
	ossService         pkg.OSSService
	emailService       pkg.EmailService
	chromeService      pkg.ChromeService
	meiliSearchService *pkg.MeiliSearchService
	payService         *pkg.PayService
}

func (s *scriptServiceImpl) GetConfig() *config.Config                    { return s.config }
func (s *scriptServiceImpl) GetLogger() *zap.Logger                       { return s.logger }
func (s *scriptServiceImpl) GetDB() *gorm.DB                              { return s.db }
func (s *scriptServiceImpl) GetRedis() *redis.Client                      { return s.redis }
func (s *scriptServiceImpl) GetUserService() services.UserService         { return s.userService }
func (s *scriptServiceImpl) GetResumeService() services.ResumeService     { return s.resumeService }
func (s *scriptServiceImpl) GetPositionService() services.PositionService { return s.positionService }
func (s *scriptServiceImpl) GetAIService() services.AIService             { return s.aiService }
func (s *scriptServiceImpl) GetOrderService() services.OrderService       { return s.orderService }
func (s *scriptServiceImpl) GetMembershipPlanService() services.MembershipPlanService {
	return s.membershipPlanService
}
func (s *scriptServiceImpl) GetExampleService() services.ExampleService { return s.exampleService }
func (s *scriptServiceImpl) GetUploadService() services.UploadService   { return s.uploadService }
func (s *scriptServiceImpl) GetCodeService() services.CodeService       { return s.codeService }
func (s *scriptServiceImpl) GetEmailCodeService() services.EmailCodeService {
	return s.emailCodeService
}
func (s *scriptServiceImpl) GetQrCodeService() services.QrCodeService { return s.qrCodeService }
func (s *scriptServiceImpl) GetJWTService() pkg.JWTService            { return s.jwtService }
func (s *scriptServiceImpl) GetSMSService() pkg.SMSService            { return s.smsService }
func (s *scriptServiceImpl) GetOSSService() pkg.OSSService            { return s.ossService }
func (s *scriptServiceImpl) GetEmailService() pkg.EmailService        { return s.emailService }
func (s *scriptServiceImpl) GetChromeService() pkg.ChromeService      { return s.chromeService }
func (s *scriptServiceImpl) GetMeiliSearchService() *pkg.MeiliSearchService {
	return s.meiliSearchService
}
func (s *scriptServiceImpl) GetPayService() *pkg.PayService { return s.payService }
