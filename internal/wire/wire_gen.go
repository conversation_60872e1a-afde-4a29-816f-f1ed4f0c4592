// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/controllers/api"
	"github.com/avrilko/resume-server/internal/cron"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/repository"
	"github.com/avrilko/resume-server/internal/routes"
	"github.com/avrilko/resume-server/internal/services"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// BuildApplication 构建应用程序
func BuildApplication() (*routes.Router, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	ossService, err := pkg.NewOSSService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	uploadService := services.NewUploadService(ossService, logger)
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	weChatService, err := pkg.NewWeChatService(configConfig)
	if err != nil {
		return nil, err
	}
	jwtService := pkg.NewJWTService(configConfig)
	codeService := services.NewCodeService(client, smsService, weChatService, configConfig, userRepository, jwtService)
	emailService := pkg.NewEmailService(configConfig, logger)
	emailCodeService := services.NewEmailCodeService(client, emailService, configConfig, logger)
	userService := services.NewUserService(userRepository, uploadService, codeService, emailCodeService, client, jwtService, configConfig, logger)
	userDownloadCouponRepository := repository.NewUserDownloadCouponRepository(db)
	userDownloadCouponService := services.NewUserDownloadCouponService(userDownloadCouponRepository, logger)
	qrCodeService := services.NewQrCodeService(client, weChatService, configConfig, userRepository, jwtService)
	userController := api.NewUserController(userService, userDownloadCouponService, jwtService, configConfig, client, ossService, codeService, weChatService, qrCodeService)
	authController := api.NewAuthController(userService, userRepository, codeService, emailCodeService, qrCodeService, jwtService, weChatService, configConfig, client)
	wechatController := api.NewWechatController(weChatService, client, userService)
	commonController := api.NewCommonController()
	uploadController := api.NewUploadController(uploadService, logger)
	membershipPlanRepository := repository.NewMembershipPlanRepository(db)
	membershipPlanService := services.NewMembershipPlanService(membershipPlanRepository, client, logger)
	membershipPlanController := api.NewMembershipPlanController(membershipPlanService, logger)
	orderRepository := repository.NewOrderRepository(db)
	userMembershipRepository := repository.NewUserMembershipRepository(db)
	payService, err := pkg.NewPayService(configConfig)
	if err != nil {
		return nil, err
	}
	orderService := services.NewOrderService(orderRepository, membershipPlanRepository, userMembershipRepository, userRepository, userDownloadCouponService, payService, configConfig, logger)
	orderController := api.NewOrderController(orderService, logger)
	openAPIController := api.NewOpenAPIController(payService, orderService, logger)
	positionRepository := repository.NewPositionRepository(db)
	exampleRepository := repository.NewExampleRepository(db)
	categoryService := services.NewCategoryService(db, positionRepository, exampleRepository, client)
	categoryController := api.NewCategoryController(categoryService)
	categoryRepository := repository.NewCategoryRepository(db)
	meiliSearchService := pkg.NewMeiliSearchService(configConfig)
	positionService := services.NewPositionService(positionRepository, categoryRepository, exampleRepository, meiliSearchService, client, logger)
	positionController := api.NewPositionController(positionService, logger)
	resumeRepository := repository.NewResumeRepository(db)
	resumeDraftRepository := repository.NewResumeDraftRepository(db)
	templateRepository := repository.NewTemplateRepository(db)
	chromeService := pkg.NewChromeService()
	resumeService := services.NewResumeService(resumeRepository, resumeDraftRepository, templateRepository, chromeService, ossService, emailService, userService, userDownloadCouponService, client, logger, configConfig)
	resumeController := api.NewResumeController(resumeService, client, logger)
	aiService, err := pkg.NewAIService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	aiCallRecordRepository := repository.NewAICallRecordRepository(db)
	webParserService := pkg.NewWebParserService(configConfig, logger)
	targetPositionRepository := repository.NewTargetPositionRepository(db, logger)
	resumeScoreRepository := repository.NewResumeScoreRepository(db)
	membershipValidationService := services.NewMembershipValidationService(userRepository, userMembershipRepository, membershipPlanRepository, resumeRepository, aiCallRecordRepository)
	servicesAIService := services.NewAIService(aiService, resumeService, aiCallRecordRepository, uploadService, webParserService, resumeRepository, resumeDraftRepository, templateRepository, exampleRepository, targetPositionRepository, resumeScoreRepository, membershipValidationService, logger)
	aiController := api.NewAIController(servicesAIService, membershipValidationService, logger)
	exampleService := services.NewExampleService(exampleRepository, templateRepository, positionRepository, resumeService, membershipValidationService, client, logger, db)
	exampleController := api.NewExampleController(exampleService, membershipValidationService, logger)
	templateService := services.NewTemplateService(templateRepository, resumeRepository, logger)
	templateController := api.NewTemplateController(templateService, logger)
	targetPositionService := services.NewTargetPositionService(targetPositionRepository, logger)
	resumeScoreService := services.NewResumeScoreService(resumeScoreRepository, resumeRepository, targetPositionRepository, logger)
	targetPositionController := api.NewTargetPositionController(targetPositionService, resumeScoreService, logger)
	router := routes.NewRouter(configConfig, userController, authController, wechatController, commonController, uploadController, membershipPlanController, orderController, openAPIController, categoryController, positionController, resumeController, aiController, exampleController, templateController, targetPositionController, jwtService, userService, userRepository, client)
	return router, nil
}

// BuildApplicationWithCron 构建包含定时任务的应用程序
func BuildApplicationWithCron() (*Application, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	ossService, err := pkg.NewOSSService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	uploadService := services.NewUploadService(ossService, logger)
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	weChatService, err := pkg.NewWeChatService(configConfig)
	if err != nil {
		return nil, err
	}
	jwtService := pkg.NewJWTService(configConfig)
	codeService := services.NewCodeService(client, smsService, weChatService, configConfig, userRepository, jwtService)
	emailService := pkg.NewEmailService(configConfig, logger)
	emailCodeService := services.NewEmailCodeService(client, emailService, configConfig, logger)
	userService := services.NewUserService(userRepository, uploadService, codeService, emailCodeService, client, jwtService, configConfig, logger)
	userDownloadCouponRepository := repository.NewUserDownloadCouponRepository(db)
	userDownloadCouponService := services.NewUserDownloadCouponService(userDownloadCouponRepository, logger)
	qrCodeService := services.NewQrCodeService(client, weChatService, configConfig, userRepository, jwtService)
	userController := api.NewUserController(userService, userDownloadCouponService, jwtService, configConfig, client, ossService, codeService, weChatService, qrCodeService)
	authController := api.NewAuthController(userService, userRepository, codeService, emailCodeService, qrCodeService, jwtService, weChatService, configConfig, client)
	wechatController := api.NewWechatController(weChatService, client, userService)
	commonController := api.NewCommonController()
	uploadController := api.NewUploadController(uploadService, logger)
	membershipPlanRepository := repository.NewMembershipPlanRepository(db)
	membershipPlanService := services.NewMembershipPlanService(membershipPlanRepository, client, logger)
	membershipPlanController := api.NewMembershipPlanController(membershipPlanService, logger)
	orderRepository := repository.NewOrderRepository(db)
	userMembershipRepository := repository.NewUserMembershipRepository(db)
	payService, err := pkg.NewPayService(configConfig)
	if err != nil {
		return nil, err
	}
	orderService := services.NewOrderService(orderRepository, membershipPlanRepository, userMembershipRepository, userRepository, userDownloadCouponService, payService, configConfig, logger)
	orderController := api.NewOrderController(orderService, logger)
	openAPIController := api.NewOpenAPIController(payService, orderService, logger)
	positionRepository := repository.NewPositionRepository(db)
	exampleRepository := repository.NewExampleRepository(db)
	categoryService := services.NewCategoryService(db, positionRepository, exampleRepository, client)
	categoryController := api.NewCategoryController(categoryService)
	categoryRepository := repository.NewCategoryRepository(db)
	meiliSearchService := pkg.NewMeiliSearchService(configConfig)
	positionService := services.NewPositionService(positionRepository, categoryRepository, exampleRepository, meiliSearchService, client, logger)
	positionController := api.NewPositionController(positionService, logger)
	resumeRepository := repository.NewResumeRepository(db)
	resumeDraftRepository := repository.NewResumeDraftRepository(db)
	templateRepository := repository.NewTemplateRepository(db)
	chromeService := pkg.NewChromeService()
	resumeService := services.NewResumeService(resumeRepository, resumeDraftRepository, templateRepository, chromeService, ossService, emailService, userService, userDownloadCouponService, client, logger, configConfig)
	resumeController := api.NewResumeController(resumeService, client, logger)
	aiService, err := pkg.NewAIService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	aiCallRecordRepository := repository.NewAICallRecordRepository(db)
	webParserService := pkg.NewWebParserService(configConfig, logger)
	targetPositionRepository := repository.NewTargetPositionRepository(db, logger)
	resumeScoreRepository := repository.NewResumeScoreRepository(db)
	membershipValidationService := services.NewMembershipValidationService(userRepository, userMembershipRepository, membershipPlanRepository, resumeRepository, aiCallRecordRepository)
	servicesAIService := services.NewAIService(aiService, resumeService, aiCallRecordRepository, uploadService, webParserService, resumeRepository, resumeDraftRepository, templateRepository, exampleRepository, targetPositionRepository, resumeScoreRepository, membershipValidationService, logger)
	aiController := api.NewAIController(servicesAIService, membershipValidationService, logger)
	exampleService := services.NewExampleService(exampleRepository, templateRepository, positionRepository, resumeService, membershipValidationService, client, logger, db)
	exampleController := api.NewExampleController(exampleService, membershipValidationService, logger)
	templateService := services.NewTemplateService(templateRepository, resumeRepository, logger)
	templateController := api.NewTemplateController(templateService, logger)
	targetPositionService := services.NewTargetPositionService(targetPositionRepository, logger)
	resumeScoreService := services.NewResumeScoreService(resumeScoreRepository, resumeRepository, targetPositionRepository, logger)
	targetPositionController := api.NewTargetPositionController(targetPositionService, resumeScoreService, logger)
	router := routes.NewRouter(configConfig, userController, authController, wechatController, commonController, uploadController, membershipPlanController, orderController, openAPIController, categoryController, positionController, resumeController, aiController, exampleController, templateController, targetPositionController, jwtService, userService, userRepository, client)
	cronService := cron.NewCronService(configConfig, logger, orderRepository, userMembershipRepository, userRepository, client)
	application := &Application{
		Router:      router,
		CronService: cronService,
	}
	return application, nil
}

// BuildScriptService 构建脚本服务
func BuildScriptService() (ScriptService, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	ossService, err := pkg.NewOSSService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	uploadService := services.NewUploadService(ossService, logger)
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	weChatService, err := pkg.NewWeChatService(configConfig)
	if err != nil {
		return nil, err
	}
	jwtService := pkg.NewJWTService(configConfig)
	codeService := services.NewCodeService(client, smsService, weChatService, configConfig, userRepository, jwtService)
	emailService := pkg.NewEmailService(configConfig, logger)
	emailCodeService := services.NewEmailCodeService(client, emailService, configConfig, logger)
	userService := services.NewUserService(userRepository, uploadService, codeService, emailCodeService, client, jwtService, configConfig, logger)
	resumeRepository := repository.NewResumeRepository(db)
	resumeDraftRepository := repository.NewResumeDraftRepository(db)
	templateRepository := repository.NewTemplateRepository(db)
	chromeService := pkg.NewChromeService()
	userDownloadCouponRepository := repository.NewUserDownloadCouponRepository(db)
	userDownloadCouponService := services.NewUserDownloadCouponService(userDownloadCouponRepository, logger)
	resumeService := services.NewResumeService(resumeRepository, resumeDraftRepository, templateRepository, chromeService, ossService, emailService, userService, userDownloadCouponService, client, logger, configConfig)
	positionRepository := repository.NewPositionRepository(db)
	categoryRepository := repository.NewCategoryRepository(db)
	exampleRepository := repository.NewExampleRepository(db)
	meiliSearchService := pkg.NewMeiliSearchService(configConfig)
	positionService := services.NewPositionService(positionRepository, categoryRepository, exampleRepository, meiliSearchService, client, logger)
	aiService, err := pkg.NewAIService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	aiCallRecordRepository := repository.NewAICallRecordRepository(db)
	webParserService := pkg.NewWebParserService(configConfig, logger)
	targetPositionRepository := repository.NewTargetPositionRepository(db, logger)
	resumeScoreRepository := repository.NewResumeScoreRepository(db)
	userMembershipRepository := repository.NewUserMembershipRepository(db)
	membershipPlanRepository := repository.NewMembershipPlanRepository(db)
	membershipValidationService := services.NewMembershipValidationService(userRepository, userMembershipRepository, membershipPlanRepository, resumeRepository, aiCallRecordRepository)
	servicesAIService := services.NewAIService(aiService, resumeService, aiCallRecordRepository, uploadService, webParserService, resumeRepository, resumeDraftRepository, templateRepository, exampleRepository, targetPositionRepository, resumeScoreRepository, membershipValidationService, logger)
	orderRepository := repository.NewOrderRepository(db)
	payService, err := pkg.NewPayService(configConfig)
	if err != nil {
		return nil, err
	}
	orderService := services.NewOrderService(orderRepository, membershipPlanRepository, userMembershipRepository, userRepository, userDownloadCouponService, payService, configConfig, logger)
	membershipPlanService := services.NewMembershipPlanService(membershipPlanRepository, client, logger)
	exampleService := services.NewExampleService(exampleRepository, templateRepository, positionRepository, resumeService, membershipValidationService, client, logger, db)
	qrCodeService := services.NewQrCodeService(client, weChatService, configConfig, userRepository, jwtService)
	scriptService := NewScriptService(configConfig, logger, db, client, userService, resumeService, positionService, servicesAIService, orderService, membershipPlanService, exampleService, uploadService, codeService, emailCodeService, qrCodeService, jwtService, smsService, ossService, emailService, chromeService, meiliSearchService, payService)
	return scriptService, nil
}

// wire.go:

// Application 应用程序结构体
type Application struct {
	Router      *routes.Router
	CronService cron.CronService
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.App.Debug {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return nil, fmt.Errorf("创建日志记录器失败: %w", err)
	}

	return logger, nil
}

var BaseSet = wire.NewSet(config.LoadConfig, ProvideLogger)

// ScriptService 脚本服务接口（在scripts包中定义）
type ScriptService interface {
	GetConfig() *config.Config
	GetLogger() *zap.Logger
	GetDB() *gorm.DB
	GetRedis() *redis.Client
	GetUserService() services.UserService
	GetResumeService() services.ResumeService
	GetPositionService() services.PositionService
	GetAIService() services.AIService
	GetOrderService() services.OrderService
	GetMembershipPlanService() services.MembershipPlanService
	GetExampleService() services.ExampleService
	GetUploadService() services.UploadService
	GetCodeService() services.CodeService
	GetEmailCodeService() services.EmailCodeService
	GetQrCodeService() services.QrCodeService
	GetJWTService() pkg.JWTService
	GetSMSService() pkg.SMSService
	GetOSSService() pkg.OSSService
	GetEmailService() pkg.EmailService
	GetChromeService() pkg.ChromeService
	GetMeiliSearchService() *pkg.MeiliSearchService
	GetPayService() *pkg.PayService
}

// NewScriptService 创建脚本服务的构造函数
func NewScriptService(config2 *config.Config,
	logger *zap.Logger,
	db *gorm.DB, redis2 *redis.Client,
	userService services.UserService,
	resumeService services.ResumeService,
	positionService services.PositionService,
	aiService services.AIService,
	orderService services.OrderService,
	membershipPlanService services.MembershipPlanService,
	exampleService services.ExampleService,
	uploadService services.UploadService,
	codeService services.CodeService,
	emailCodeService services.EmailCodeService,
	qrCodeService services.QrCodeService,
	jwtService pkg.JWTService,
	smsService pkg.SMSService,
	ossService pkg.OSSService,
	emailService pkg.EmailService,
	chromeService pkg.ChromeService,
	meiliSearchService *pkg.MeiliSearchService,
	payService *pkg.PayService,
) ScriptService {
	return &scriptServiceImpl{
		config:                config2,
		logger:                logger,
		db:                    db,
		redis:                 redis2,
		userService:           userService,
		resumeService:         resumeService,
		positionService:       positionService,
		aiService:             aiService,
		orderService:          orderService,
		membershipPlanService: membershipPlanService,
		exampleService:        exampleService,
		uploadService:         uploadService,
		codeService:           codeService,
		emailCodeService:      emailCodeService,
		qrCodeService:         qrCodeService,
		jwtService:            jwtService,
		smsService:            smsService,
		ossService:            ossService,
		emailService:          emailService,
		chromeService:         chromeService,
		meiliSearchService:    meiliSearchService,
		payService:            payService,
	}
}

// scriptServiceImpl 脚本服务实现
type scriptServiceImpl struct {
	config *config.Config
	logger *zap.Logger
	db     *gorm.DB
	redis  *redis.Client

	userService           services.UserService
	resumeService         services.ResumeService
	positionService       services.PositionService
	aiService             services.AIService
	orderService          services.OrderService
	membershipPlanService services.MembershipPlanService
	exampleService        services.ExampleService
	uploadService         services.UploadService
	codeService           services.CodeService
	emailCodeService      services.EmailCodeService
	qrCodeService         services.QrCodeService

	jwtService         pkg.JWTService
	smsService         pkg.SMSService
	ossService         pkg.OSSService
	emailService       pkg.EmailService
	chromeService      pkg.ChromeService
	meiliSearchService *pkg.MeiliSearchService
	payService         *pkg.PayService
}

func (s *scriptServiceImpl) GetConfig() *config.Config { return s.config }

func (s *scriptServiceImpl) GetLogger() *zap.Logger { return s.logger }

func (s *scriptServiceImpl) GetDB() *gorm.DB { return s.db }

func (s *scriptServiceImpl) GetRedis() *redis.Client { return s.redis }

func (s *scriptServiceImpl) GetUserService() services.UserService { return s.userService }

func (s *scriptServiceImpl) GetResumeService() services.ResumeService { return s.resumeService }

func (s *scriptServiceImpl) GetPositionService() services.PositionService { return s.positionService }

func (s *scriptServiceImpl) GetAIService() services.AIService { return s.aiService }

func (s *scriptServiceImpl) GetOrderService() services.OrderService { return s.orderService }

func (s *scriptServiceImpl) GetMembershipPlanService() services.MembershipPlanService {
	return s.membershipPlanService
}

func (s *scriptServiceImpl) GetExampleService() services.ExampleService { return s.exampleService }

func (s *scriptServiceImpl) GetUploadService() services.UploadService { return s.uploadService }

func (s *scriptServiceImpl) GetCodeService() services.CodeService { return s.codeService }

func (s *scriptServiceImpl) GetEmailCodeService() services.EmailCodeService {
	return s.emailCodeService
}

func (s *scriptServiceImpl) GetQrCodeService() services.QrCodeService { return s.qrCodeService }

func (s *scriptServiceImpl) GetJWTService() pkg.JWTService { return s.jwtService }

func (s *scriptServiceImpl) GetSMSService() pkg.SMSService { return s.smsService }

func (s *scriptServiceImpl) GetOSSService() pkg.OSSService { return s.ossService }

func (s *scriptServiceImpl) GetEmailService() pkg.EmailService { return s.emailService }

func (s *scriptServiceImpl) GetChromeService() pkg.ChromeService { return s.chromeService }

func (s *scriptServiceImpl) GetMeiliSearchService() *pkg.MeiliSearchService {
	return s.meiliSearchService
}

func (s *scriptServiceImpl) GetPayService() *pkg.PayService { return s.payService }
