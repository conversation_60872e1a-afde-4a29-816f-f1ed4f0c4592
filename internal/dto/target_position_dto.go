package dto

// CreateTargetPositionRequest 创建目标岗位请求DTO
type CreateTargetPositionRequest struct {
	PositionName   string `json:"position_name" binding:"required,max=100" example:"前端开发工程师" label:"岗位名称"`
	CompanyName    string `json:"company_name" binding:"required,max=100" example:"阿里巴巴" label:"公司名称"`
	JobSource      string `json:"job_source" binding:"required,max=50" example:"Boss直聘" label:"岗位来源"`
	JobDescription string `json:"job_description" binding:"required" example:"负责前端页面开发..." label:"岗位职责描述/要求"`
}

// UpdateTargetPositionRequest 更新目标岗位请求DTO
type UpdateTargetPositionRequest struct {
	ID             uint   `uri:"id" binding:"required" example:"1" label:"岗位ID"`
	PositionName   string `json:"position_name" binding:"required,max=100" example:"前端开发工程师" label:"岗位名称"`
	CompanyName    string `json:"company_name" binding:"required,max=100" example:"阿里巴巴" label:"公司名称"`
	JobSource      string `json:"job_source" binding:"required,max=50" example:"Boss直聘" label:"岗位来源"`
	JobDescription string `json:"job_description" binding:"required" example:"负责前端页面开发..." label:"岗位职责描述/要求"`
}

// DeleteTargetPositionRequest 删除目标岗位请求DTO
type DeleteTargetPositionRequest struct {
	ID uint `uri:"id" binding:"required" example:"1" label:"岗位ID"`
}

// GetTargetPositionRequest 获取目标岗位详情请求DTO
type GetTargetPositionRequest struct {
	ID uint `uri:"id" binding:"required" example:"1" label:"岗位ID"`
}

// GetResumeScoreRequest 获取简历评分详情请求DTO
type GetResumeScoreRequest struct {
	ID uint `uri:"id" binding:"required" example:"1" label:"评分记录ID"`
}

// GetMyResumeScoresRequest 获取我的简历评分列表请求DTO
type GetMyResumeScoresRequest struct {
	Page  int `form:"page" binding:"omitempty,min=1" example:"1" label:"页码"`
	Limit int `form:"limit" binding:"omitempty,min=1,max=100" example:"10" label:"每页数量"`
}

// DeleteResumeScoreRequest 删除简历评分请求DTO
type DeleteResumeScoreRequest struct {
	ID uint `uri:"id" binding:"required" example:"1" label:"评分记录ID"`
}
