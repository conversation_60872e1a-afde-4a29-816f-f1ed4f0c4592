package dto

import "github.com/avrilko/resume-server/internal/models"

// GetResumeDetailRequest 获取简历详情请求DTO
type GetResumeDetailRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// GetResumeDraftDetailRequest 获取简历草稿详情请求DTO
type GetResumeDraftDetailRequest struct {
	DraftID string `uri:"draft_id" binding:"required,numeric,min=1" json:"draft_id" example:"1"`
}

// SaveResumeRequest 保存简历请求DTO
type SaveResumeRequest struct {
	ResumeID        string                 `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
	CompletionRate  string                 `json:"completion_rate" binding:"required" example:"75%"`
	BasicInfo       models.BasicInfo       `json:"basic_info" binding:"required"`
	Education       models.Education       `json:"education" binding:"required"`
	Work            models.Work            `json:"work" binding:"required"`
	Project         models.Project         `json:"project" binding:"required"`
	Research        models.Research        `json:"research" binding:"required"`
	Team            models.Team            `json:"team" binding:"required"`
	Portfolio       models.Portfolio       `json:"portfolio" binding:"required"`
	Other           models.Other           `json:"other" binding:"required"`
	PersonalSummary models.PersonalSummary `json:"personal_summary" binding:"required"`
	Honors          models.Honors          `json:"honors" binding:"required"`
	Skills          models.Skills          `json:"skills" binding:"required"`
	CustomModules   models.CustomModules   `json:"custom_modules" binding:"required"`
	Slogan          models.Slogan          `json:"slogan" binding:"required"`
	ResumeStyle     models.ResumeStyle     `json:"resume_style" binding:"required"`
}

// UpdateResumeNameRequest 修改简历名称请求DTO
type UpdateResumeNameRequest struct {
	ResumeID   string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
	ResumeName string `json:"resume_name" binding:"required,min=1,max=100" example:"我的新简历"`
}

// DownloadResumePDFRequest 下载简历PDF请求DTO
type DownloadResumePDFRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// ShareResumeByEmailRequest 邮件分享简历请求DTO
type ShareResumeByEmailRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
	Email    string `json:"email" binding:"required,email" example:"<EMAIL>"`
	FileName string `json:"file_name" binding:"required,min=1,max=100" example:"张三的简历"`
}

// GetResumeBasicInfoRequest 获取简历基本信息请求DTO
type GetResumeBasicInfoRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// DeleteResumeRequest 删除简历请求DTO
type DeleteResumeRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// CopyResumeRequest 复制简历请求DTO
type CopyResumeRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// PermanentlyDeleteResumeRequest 物理删除简历请求DTO
type PermanentlyDeleteResumeRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// RestoreResumeRequest 恢复简历请求DTO
type RestoreResumeRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
}

// ApplyDraftRequest 应用草稿请求DTO
type ApplyDraftRequest struct {
	ResumeID string `uri:"resume_id" binding:"required,numeric,min=1" json:"resume_id" example:"1"`
	DraftID  uint   `json:"draft_id" binding:"required,min=1" example:"1"`
}
