package dto

// GetTemplateListRequest 获取模板列表请求DTO
type GetTemplateListRequest struct {
	Page     int `form:"page" binding:"omitempty,min=1" example:"1"`       // 页码，默认为1
	PageSize int `form:"page_size" binding:"omitempty,min=1" example:"20"` // 每页条数，默认为20
}

// UseTemplateRequest 使用模板请求DTO
type UseTemplateRequest struct {
	TemplateID uint `json:"template_id" binding:"required,min=1" example:"1"` // 模板ID
	ResumeID   uint `json:"resume_id" binding:"required,min=1" example:"1"`   // 简历ID
}
