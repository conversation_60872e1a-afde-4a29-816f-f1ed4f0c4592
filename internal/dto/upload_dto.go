package dto

import (
	"mime/multipart"
)

// UploadAvatarRequest 上传头像请求DTO
type UploadAvatarRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required" swaggerignore:"true"` // 文件
}

// UploadAttachmentRequest 上传附件请求DTO
type UploadAttachmentRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required" swaggerignore:"true"` // 文件
}

// ParseFileRequest 文件解析请求DTO
type ParseFileRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required" swaggerignore:"true"` // 文件
}
