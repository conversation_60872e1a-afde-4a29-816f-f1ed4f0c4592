package dto

// GetExampleByIdRequest 根据ID获取示例详情请求DTO
type GetExampleByIdRequest struct {
	ID uint `uri:"id" binding:"required,min=1" example:"1" label:"示例ID"`
}

// GetExampleTdkByIdRequest 根据ID获取示例TDK请求DTO
type GetExampleTdkByIdRequest struct {
	ID uint `uri:"id" binding:"required,min=1" example:"1" label:"示例ID"`
}

// UseExampleRequest 使用例子请求DTO
type UseExampleRequest struct {
	ExampleID uint `json:"example_id" binding:"required,min=1" example:"1" label:"例子ID"`
}
