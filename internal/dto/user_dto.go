package dto

import "mime/multipart"

// SendSMSCodeRequest 发送短信验证码请求DTO
type SendSMSCodeRequest struct {
	Phone string `json:"phone" binding:"required" example:"13812345678" label:"手机号码"`
}

// LoginCodeRequest 验证码登录请求DTO
type LoginCodeRequest struct {
	Phone string `json:"phone" binding:"required" example:"13812345678" label:"手机号码"`
	Code  string `json:"code" binding:"required" example:"123456" label:"验证码"`
}

// QrCodeStatusRequest 二维码状态查询请求DTO
type QrCodeStatusRequest struct {
	SceneId string `json:"scene_id" binding:"required" example:"login_1234567890_123456" label:"场景ID"`
}

// UpdateUsernameRequest 修改用户名请求DTO
type UpdateUsernameRequest struct {
	Username string `json:"username" binding:"required,min=2,max=20" example:"新用户名" label:"用户名"`
}

// UpdateAvatarRequest 修改用户头像请求DTO（文件上传）
type UpdateAvatarRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required" swaggerignore:"true"` // 头像文件
}

// BindPhoneRequest 绑定手机号请求DTO
type BindPhoneRequest struct {
	Phone string `json:"phone" binding:"required" example:"13812345678" label:"手机号码"`
	Code  string `json:"code" binding:"required" example:"123456" label:"验证码"`
}

// BindEmailRequest 绑定邮箱请求DTO
type BindEmailRequest struct {
	Email string `json:"email" binding:"required,email" example:"<EMAIL>" label:"邮箱地址"`
	Code  string `json:"code" binding:"required" example:"123456" label:"验证码"`
}

// CheckBindWechatQrCodeStatusRequest 检查绑定微信二维码状态请求DTO
type CheckBindWechatQrCodeStatusRequest struct {
	SceneId string `json:"scene_id" binding:"required" example:"bind_1234567890_123456789" label:"二维码场景值ID"`
}
