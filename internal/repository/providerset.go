package repository

import (
	"github.com/google/wire"
)

// RepositorySet 仓储层依赖注入集合
var RepositorySet = wire.NewSet(
	NewUserRepository,
	NewMembershipPlanRepository,
	NewOrderRepository,
	NewUserMembershipRepository,
	NewPositionRepository,
	NewCategoryRepository,
	NewResumeRepository,
	NewResumeDraftRepository,
	NewAICallRecordRepository,
	NewTemplateRepository,
	NewExampleRepository,
	NewTargetPositionRepository,
	NewResumeScoreRepository,
	NewUserDownloadCouponRepository,
	// 未来可以在这里添加其他仓储的构造函数
)
