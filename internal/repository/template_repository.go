package repository

import (
	"context"

	"github.com/avrilko/resume-server/internal/models"
	"gorm.io/gorm"
)

// TemplateRepository 模板仓库接口
type TemplateRepository interface {
	// GetByID 根据ID获取模板
	GetByID(ctx context.Context, id uint) (*models.Template, error)

	// GetAll 获取所有模板
	GetAll(ctx context.Context) ([]*models.Template, error)

	// GetTemplatesPaginated 分页获取模板列表
	GetTemplatesPaginated(ctx context.Context, page, pageSize int) ([]*models.Template, int64, error)

	// Create 创建模板
	Create(ctx context.Context, template *models.Template) error

	// Update 更新模板
	Update(ctx context.Context, template *models.Template) error

	// Delete 删除模板
	Delete(ctx context.Context, id uint) error

	// GetRandomTemplate 随机获取一个模板
	GetRandomTemplate(ctx context.Context) (*models.Template, error)
}

// templateRepository 模板仓库实现
type templateRepository struct {
	db *gorm.DB
}

// NewTemplateRepository 创建模板仓库
func NewTemplateRepository(db *gorm.DB) TemplateRepository {
	return &templateRepository{
		db: db,
	}
}

// GetByID 根据ID获取模板
func (r *templateRepository) GetByID(ctx context.Context, id uint) (*models.Template, error) {
	var template models.Template
	if err := r.db.WithContext(ctx).First(&template, id).Error; err != nil {
		return nil, err
	}
	return &template, nil
}

// GetAll 获取所有模板
func (r *templateRepository) GetAll(ctx context.Context) ([]*models.Template, error) {
	var templates []*models.Template
	if err := r.db.WithContext(ctx).Order("sort ASC").Find(&templates).Error; err != nil {
		return nil, err
	}
	return templates, nil
}

// GetTemplatesPaginated 分页获取模板列表
func (r *templateRepository) GetTemplatesPaginated(ctx context.Context, page, pageSize int) ([]*models.Template, int64, error) {
	var templates []*models.Template
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&models.Template{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按排序字段升序排列
	if err := r.db.WithContext(ctx).
		Order("sort ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// Create 创建模板
func (r *templateRepository) Create(ctx context.Context, template *models.Template) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// Update 更新模板
func (r *templateRepository) Update(ctx context.Context, template *models.Template) error {
	return r.db.WithContext(ctx).Save(template).Error
}

// Delete 删除模板
func (r *templateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Template{}, id).Error
}

// GetRandomTemplate 随机获取一个模板
func (r *templateRepository) GetRandomTemplate(ctx context.Context) (*models.Template, error) {
	var template models.Template

	// 使用 ORDER BY RAND() 随机获取一个模板
	if err := r.db.WithContext(ctx).
		Where("deleted_at IS NULL"). // 确保模板未被删除
		Order("RAND()").
		First(&template).Error; err != nil {
		return nil, err
	}

	return &template, nil
}
