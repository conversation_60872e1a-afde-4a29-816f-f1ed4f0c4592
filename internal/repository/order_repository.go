package repository

import (
	"context"
	"time"

	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/models"
	"gorm.io/gorm"
)

// OrderRepository 订单仓库接口
type OrderRepository interface {
	// Create 创建订单
	Create(ctx context.Context, order *models.Order) error

	// GetByID 根据ID获取订单
	GetByID(ctx context.Context, id uint) (*models.Order, error)

	// GetByOrderNo 根据订单号获取订单
	GetByOrderNo(ctx context.Context, orderNo string) (*models.Order, error)

	// Update 更新订单
	Update(ctx context.Context, order *models.Order) error

	// GetPendingOrderByUserAndPlan 获取用户指定套餐的待支付订单
	// 如果存在多个，返回最新创建的一个
	GetPendingOrderByUserAndPlan(ctx context.Context, userID, planID uint, paymentMethod enum.PaymentMethod, minutes int) (*models.Order, error)

	// GetTimeoutPendingOrders 获取超时的待支付订单
	GetTimeoutPendingOrders(ctx context.Context, timeoutMinutes int) ([]*models.Order, error)

	// BatchUpdatePaymentStatus 批量更新订单支付状态
	BatchUpdatePaymentStatus(ctx context.Context, orderIDs []uint, status enum.PaymentStatus, failReason string) error

	// GetUserOrdersPaginated 分页获取用户订单列表
	GetUserOrdersPaginated(ctx context.Context, userID uint, page, pageSize int, paymentStatuses []enum.PaymentStatus) ([]*models.Order, int64, error)
}

// orderRepository 订单仓库实现
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository 创建订单仓库
func NewOrderRepository(db *gorm.DB) OrderRepository {
	return &orderRepository{
		db: db,
	}
}

// Create 创建订单
func (r *orderRepository) Create(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Create(order).Error
}

// GetByID 根据ID获取订单
func (r *orderRepository) GetByID(ctx context.Context, id uint) (*models.Order, error) {
	var order models.Order
	err := r.db.WithContext(ctx).First(&order, id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByOrderNo 根据订单号获取订单
func (r *orderRepository) GetByOrderNo(ctx context.Context, orderNo string) (*models.Order, error) {
	var order models.Order
	err := r.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// Update 更新订单
func (r *orderRepository) Update(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Save(order).Error
}

// GetPendingOrderByUserAndPlan 获取用户指定套餐的待支付订单
// 如果存在多个，返回最新创建的一个
func (r *orderRepository) GetPendingOrderByUserAndPlan(ctx context.Context, userID, planID uint, paymentMethod enum.PaymentMethod, minutes int) (*models.Order, error) {
	var order models.Order

	// 计算时间阈值，只查询最近minutes分钟内创建的订单
	timeThreshold := time.Now().Add(-time.Duration(minutes) * time.Minute)

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND membership_plan_id = ? AND payment_method = ? AND payment_status = ? AND created_at > ?",
			userID, planID, paymentMethod, enum.PaymentStatusPending, timeThreshold).
		Order("created_at DESC").
		First(&order).Error

	if err != nil {
		return nil, err
	}

	return &order, nil
}

// GetTimeoutPendingOrders 获取超时的待支付订单
func (r *orderRepository) GetTimeoutPendingOrders(ctx context.Context, timeoutMinutes int) ([]*models.Order, error) {
	var orders []*models.Order

	// 计算超时时间点
	timeoutTime := time.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)

	err := r.db.WithContext(ctx).
		Where("payment_status = ? AND created_at < ?", enum.PaymentStatusPending, timeoutTime).
		Find(&orders).Error

	if err != nil {
		return nil, err
	}

	return orders, nil
}

// BatchUpdatePaymentStatus 批量更新订单支付状态
func (r *orderRepository) BatchUpdatePaymentStatus(ctx context.Context, orderIDs []uint, status enum.PaymentStatus, failReason string) error {
	if len(orderIDs) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"payment_status": status,
		"updated_at":     time.Now(),
	}

	if failReason != "" {
		updates["fail_reason"] = failReason
	}

	return r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("id IN ?", orderIDs).
		Updates(updates).Error
}

// GetUserOrdersPaginated 分页获取用户订单列表
func (r *orderRepository) GetUserOrdersPaginated(ctx context.Context, userID uint, page, pageSize int, paymentStatuses []enum.PaymentStatus) ([]*models.Order, int64, error) {
	var orders []*models.Order
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&models.Order{}).Where("user_id = ?", userID)

	// 如果指定了支付状态，添加状态筛选条件
	if len(paymentStatuses) > 0 {
		query = query.Where("payment_status IN ?", paymentStatuses)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按创建时间倒序排列（最新的在前面）
	if err := query.
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&orders).Error; err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}
