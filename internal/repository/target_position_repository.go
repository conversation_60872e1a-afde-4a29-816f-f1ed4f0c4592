package repository

import (
	"context"

	"github.com/avrilko/resume-server/internal/models"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TargetPositionRepository 目标岗位仓储接口
type TargetPositionRepository interface {
	Create(ctx context.Context, targetPosition *models.TargetPosition) error
	GetByID(ctx context.Context, id uint) (*models.TargetPosition, error)
	GetByUserIDAndID(ctx context.Context, userID, id uint) (*models.TargetPosition, error)
	Update(ctx context.Context, targetPosition *models.TargetPosition) error
	Delete(ctx context.Context, id uint) error
	DeleteByUserIDAndID(ctx context.Context, userID, id uint) error
	GetAllByUserID(ctx context.Context, userID uint) ([]*models.TargetPosition, int64, error)
}

// targetPositionRepository 目标岗位仓储实现
type targetPositionRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewTargetPositionRepository 创建目标岗位仓储
func NewTargetPositionRepository(db *gorm.DB, logger *zap.Logger) TargetPositionRepository {
	return &targetPositionRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建目标岗位
func (r *targetPositionRepository) Create(ctx context.Context, targetPosition *models.TargetPosition) error {
	return r.db.WithContext(ctx).Create(targetPosition).Error
}

// GetByID 根据ID获取目标岗位
func (r *targetPositionRepository) GetByID(ctx context.Context, id uint) (*models.TargetPosition, error) {
	var targetPosition models.TargetPosition
	err := r.db.WithContext(ctx).First(&targetPosition, id).Error
	if err != nil {
		return nil, err
	}
	return &targetPosition, nil
}

// GetByUserIDAndID 根据用户ID和岗位ID获取目标岗位
func (r *targetPositionRepository) GetByUserIDAndID(ctx context.Context, userID, id uint) (*models.TargetPosition, error) {
	var targetPosition models.TargetPosition
	err := r.db.WithContext(ctx).Where("user_id = ? AND id = ?", userID, id).First(&targetPosition).Error
	if err != nil {
		return nil, err
	}
	return &targetPosition, nil
}

// Update 更新目标岗位
func (r *targetPositionRepository) Update(ctx context.Context, targetPosition *models.TargetPosition) error {
	return r.db.WithContext(ctx).Save(targetPosition).Error
}

// Delete 删除目标岗位
func (r *targetPositionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.TargetPosition{}, id).Error
}

// DeleteByUserIDAndID 根据用户ID和岗位ID删除目标岗位
func (r *targetPositionRepository) DeleteByUserIDAndID(ctx context.Context, userID, id uint) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND id = ?", userID, id).Delete(&models.TargetPosition{}).Error
}

// GetAllByUserID 根据用户ID获取所有目标岗位
func (r *targetPositionRepository) GetAllByUserID(ctx context.Context, userID uint) ([]*models.TargetPosition, int64, error) {
	var targetPositions []*models.TargetPosition
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).Model(&models.TargetPosition{}).Where("user_id = ?", userID).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取列表，按创建时间倒序
	err = r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&targetPositions).Error
	if err != nil {
		return nil, 0, err
	}

	return targetPositions, total, nil
}
