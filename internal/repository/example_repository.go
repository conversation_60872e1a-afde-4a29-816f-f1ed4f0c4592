package repository

import (
	"context"

	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/models"
	"gorm.io/gorm"
)

// ExampleRepository 示例仓库接口
type ExampleRepository interface {
	// GetFirstWithoutAIData 获取第一个没有AI数据的示例
	GetFirstWithoutAIData(ctx context.Context) (*models.Example, error)

	// UpdateExampleData 更新示例数据
	UpdateExampleData(ctx context.Context, example *models.Example) error

	// UpdateDataStatus 更新数据状态
	UpdateDataStatus(ctx context.Context, id uint, status enum.DataStatus) error

	// GetByID 根据ID获取示例详情
	GetByID(ctx context.Context, id uint) (*models.Example, error)

	// GetExamplesWithoutPreviewImage 获取预览图为空的示例列表
	GetExamplesWithoutPreviewImage(ctx context.Context) ([]*models.Example, error)

	// UpdatePreviewImageUrl 更新示例预览图URL
	UpdatePreviewImageUrl(ctx context.Context, id uint, previewImageUrl string) error

	// GetAllExamples 获取所有示例数据
	GetAllExamples(ctx context.Context) ([]*models.Example, error)

	// GetRandomExamples 随机获取指定数量的示例
	GetRandomExamples(ctx context.Context, limit int) ([]*models.Example, error)

	// GetRandomExamplesByInternship 根据is_internship字段随机获取指定数量的示例
	GetRandomExamplesByInternship(ctx context.Context, isInternship bool, limit int) ([]*models.Example, error)
}

// exampleRepository 示例仓库实现
type exampleRepository struct {
	db *gorm.DB
}

// NewExampleRepository 创建示例仓库
func NewExampleRepository(db *gorm.DB) ExampleRepository {
	return &exampleRepository{
		db: db,
	}
}

// GetFirstWithoutAIData 获取第一个没有AI数据的示例
func (r *exampleRepository) GetFirstWithoutAIData(ctx context.Context) (*models.Example, error) {
	var example models.Example

	err := r.db.WithContext(ctx).
		Where("data_status = ? ", enum.DataStatusNoData).
		Order("sort ASC").
		First(&example).Error
	if err != nil {
		return nil, err
	}
	return &example, nil
}

// UpdateExampleData 更新示例数据
func (r *exampleRepository) UpdateExampleData(ctx context.Context, example *models.Example) error {
	return r.db.WithContext(ctx).Save(example).Error
}

// UpdateDataStatus 更新数据状态
func (r *exampleRepository) UpdateDataStatus(ctx context.Context, id uint, status enum.DataStatus) error {
	return r.db.WithContext(ctx).
		Model(&models.Example{}).
		Where("id = ?", id).
		Update("data_status", status).Error
}

// GetByID 根据ID获取示例详情
func (r *exampleRepository) GetByID(ctx context.Context, id uint) (*models.Example, error) {
	var example models.Example
	err := r.db.WithContext(ctx).
		First(&example, id).Error
	if err != nil {
		return nil, err
	}
	return &example, nil
}

// GetExamplesWithoutPreviewImage 获取预览图为空的示例列表
func (r *exampleRepository) GetExamplesWithoutPreviewImage(ctx context.Context) ([]*models.Example, error) {
	var examples []*models.Example
	err := r.db.WithContext(ctx).
		Where("preview_image_url = '' OR preview_image_url IS NULL").
		Order("sort ASC").
		Find(&examples).Error
	if err != nil {
		return nil, err
	}
	return examples, nil
}

// UpdatePreviewImageUrl 更新示例预览图URL
func (r *exampleRepository) UpdatePreviewImageUrl(ctx context.Context, id uint, previewImageUrl string) error {
	return r.db.WithContext(ctx).
		Model(&models.Example{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"preview_image_url": previewImageUrl,
			"updated_at":        gorm.Expr("NOW()"),
		}).Error
}

// GetAllExamples 获取所有示例数据
func (r *exampleRepository) GetAllExamples(ctx context.Context) ([]*models.Example, error) {
	var examples []*models.Example
	err := r.db.WithContext(ctx).
		Order("sort ASC").
		Find(&examples).Error
	if err != nil {
		return nil, err
	}
	return examples, nil
}

// GetRandomExamples 随机获取指定数量的示例
func (r *exampleRepository) GetRandomExamples(ctx context.Context, limit int) ([]*models.Example, error) {
	var examples []*models.Example
	err := r.db.WithContext(ctx).
		Order("RAND()").
		Limit(limit).
		Find(&examples).Error
	if err != nil {
		return nil, err
	}
	return examples, nil
}

// GetRandomExamplesByInternship 根据is_internship字段随机获取指定数量的示例
func (r *exampleRepository) GetRandomExamplesByInternship(ctx context.Context, isInternship bool, limit int) ([]*models.Example, error) {
	var examples []*models.Example
	err := r.db.WithContext(ctx).
		Where("is_internship = ?", isInternship).
		Order("RAND()").
		Limit(limit).
		Find(&examples).Error
	if err != nil {
		return nil, err
	}
	return examples, nil
}
