package repository

import (
	"context"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/enum"
	"github.com/avrilko/resume-server/internal/models"
	"github.com/avrilko/resume-server/internal/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Create(user *models.User) error
	GetByID(id uint) (*models.User, error)
	GetByPhone(phone string) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	GetByOpenID(openID string) (*models.User, error)
	GetByFingerPrint(fingerPrint string) (*models.User, error)
	FirstOrCreateByFingerPrint(fingerPrint string, bdVid string, channel string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uint) error
	List(page, pageSize int) ([]*models.User, int64, error)
	UpdateAvatar(userID uint, avatarURL string) error
	ExistsByUsername(username string) (bool, error)
	UpdateUsername(userID uint, username string) error
	// BatchUpdateUserType 批量更新用户类型
	BatchUpdateUserType(userIDs []uint, userType enum.UserType) error
}

// userRepository 用户仓储实现
type userRepository struct {
	db     *gorm.DB
	config *config.Config
	logger *zap.Logger
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *gorm.DB, cfg *config.Config, logger *zap.Logger) UserRepository {
	return &userRepository{
		db:     db,
		config: cfg,
		logger: logger,
	}
}

// Create 创建用户
func (r *userRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id uint) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByPhone 根据手机号获取用户
func (r *userRepository) GetByPhone(phone string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByOpenID 根据微信OpenID获取用户
func (r *userRepository) GetByOpenID(openID string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("open_id = ?", openID).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

// Delete 删除用户
func (r *userRepository) Delete(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

// List 获取用户列表
func (r *userRepository) List(page, pageSize int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	offset := (page - 1) * pageSize

	// 获取总数
	if err := r.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := r.db.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// ExistsByUsername 检查用户名是否已存在
func (r *userRepository) ExistsByUsername(username string) (bool, error) {
	var count int64
	err := r.db.Model(&models.User{}).Where("username = ?", username).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateUsername 更新用户名
func (r *userRepository) UpdateUsername(userID uint, username string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("username", username).Error
}

// UpdateAvatar 更新用户头像
func (r *userRepository) UpdateAvatar(userID uint, avatarURL string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("avatar", avatarURL).Error
}

// GetByFingerPrint 根据浏览器指纹获取用户
func (r *userRepository) GetByFingerPrint(fingerPrint string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("finger_print = ?", fingerPrint).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// FirstOrCreateByFingerPrint 根据浏览器指纹获取用户，如果不存在则创建游客用户
func (r *userRepository) FirstOrCreateByFingerPrint(fingerPrint string, bdVid string, channel string) (*models.User, error) {
	var user models.User

	// 先尝试查询
	result := r.db.Where("finger_print = ?", fingerPrint).First(&user)
	if result.Error == nil {
		// 找到了用户，检查是否需要更新bd_vid和channel
		needUpdate := false
		if user.BdVid == "" && bdVid != "" {
			user.BdVid = bdVid
			needUpdate = true
		}
		if user.Channel == "" && channel != "" {
			user.Channel = channel
			needUpdate = true
		}

		if needUpdate {
			if err := r.db.Save(&user).Error; err != nil {
				r.logger.Error("更新用户信息失败",
					zap.String("fingerPrint", fingerPrint),
					zap.String("bd_vid", bdVid),
					zap.String("channel", channel),
					zap.Uint("user_id", user.ID),
					zap.Error(err))
			} else if user.BdVid != "" {
				// 异步调用百度转化跟踪API（转化类型为25，表示现有用户转化）
				r.uploadBaiduConversionDataAsync(user.BdVid, user.ID, "existing_user")
			}
		}
		return &user, nil
	}

	// 如果是记录不存在错误，创建新用户
	if result.Error == gorm.ErrRecordNotFound {
		// 生成随机用户名
		username, err := utils.GenerateRandomNickname()
		if err != nil {
			// 如果生成用户名失败，使用备用格式
			username = utils.GenerateFallbackUsername()
		}

		// 生成随机头像
		avatar := utils.GenerateRandomAvatar()

		// 创建游客用户，设置随机用户名和头像
		newUser := &models.User{
			FingerPrint: fingerPrint,
			Username:    username,
			Avatar:      avatar,
			Status:      enum.UserStatusEnabled,
			UserType:    enum.UserTypeGuest, // 设置为游客类型
			BdVid:       bdVid,              // 设置百度投放ID
			Channel:     channel,            // 设置渠道
		}

		// 创建用户
		if err := r.db.Create(newUser).Error; err != nil {
			return nil, err
		}

		// 异步调用百度转化跟踪API（转化类型为4，表示新用户注册）
		r.uploadBaiduConversionDataAsync(bdVid, newUser.ID, "new_user")

		return newUser, nil
	}

	// 其他错误直接返回
	return nil, result.Error
}

// BatchUpdateUserType 批量更新用户类型
func (r *userRepository) BatchUpdateUserType(userIDs []uint, userType enum.UserType) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.Model(&models.User{}).
		Where("id IN ?", userIDs).
		Update("user_type", userType).Error
}

// uploadBaiduConversionDataAsync 异步上传百度转化数据
func (r *userRepository) uploadBaiduConversionDataAsync(bdVid string, userID uint, userType string) {
	if bdVid == "" {
		return
	}

	go func() {
		ctx := context.Background()
		err := utils.UploadBaiduConversionData(ctx, bdVid, r.config.FrontendDomain, 4, r.logger)
		if err != nil {
			r.logger.Error("上传百度转化数据失败",
				zap.String("bd_vid", bdVid),
				zap.Uint("user_id", userID),
				zap.String("type", userType),
				zap.Error(err))
		} else {
			r.logger.Info("成功上传百度转化数据",
				zap.String("bd_vid", bdVid),
				zap.Uint("user_id", userID),
				zap.String("type", userType),
				zap.Int("new_type", 4))
		}
	}()
}
