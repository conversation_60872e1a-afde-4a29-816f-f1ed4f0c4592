package repository

import (
	"context"

	"github.com/avrilko/resume-server/internal/models"
	"gorm.io/gorm"
)

// ResumeRepository 简历仓库接口
type ResumeRepository interface {
	// GetResumeWithDetailByID 根据简历ID获取简历及其详情
	GetResumeWithDetailByID(ctx context.Context, resumeID uint) (*models.Resume, *models.ResumeDetail, *models.Template, error)

	// GetResumeByID 根据简历ID获取简历基本信息
	GetResumeByID(ctx context.Context, resumeID uint) (*models.Resume, error)

	// GetResumeDetailByResumeID 根据简历ID获取简历详情
	GetResumeDetailByResumeID(ctx context.Context, resumeID uint) (*models.ResumeDetail, error)

	// CheckResumeOwnership 检查简历是否属于指定用户
	CheckResumeOwnership(ctx context.Context, resumeID, userID uint) (bool, error)

	// CheckDeletedResumeOwnership 检查已删除简历是否属于指定用户
	CheckDeletedResumeOwnership(ctx context.Context, resumeID, userID uint) (*models.Resume, error)

	// SaveResumeDetail 保存简历详情
	SaveResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error

	// UpdateResumeUpdatedAt 更新简历的更新时间
	UpdateResumeUpdatedAt(ctx context.Context, resumeID uint) error

	// UpdateResumeCompletionRate 更新简历完成度
	UpdateResumeCompletionRate(ctx context.Context, resumeID uint, completionRate string) error

	// UpdateResumeName 更新简历名称
	UpdateResumeName(ctx context.Context, resumeID uint, resumeName string) error

	// UpdateResumeTemplateID 更新简历模板ID
	UpdateResumeTemplateID(ctx context.Context, resumeID uint, templateID uint) error

	// UpdateResumeDetailStyle 更新简历详情样式
	UpdateResumeDetailStyle(ctx context.Context, resumeID uint, resumeStyle models.ResumeStyle) error

	// UpdateResumePreviewImage 更新简历预览图URL
	UpdateResumePreviewImage(ctx context.Context, resumeID uint, previewImageUrl string) error

	// GetResumesByUserID 根据用户ID获取所有简历（按更新时间排序）
	GetResumesByUserID(ctx context.Context, userID uint) ([]*models.Resume, error)

	// GetDeletedResumesByUserID 根据用户ID获取所有已删除的简历（回收站）
	GetDeletedResumesByUserID(ctx context.Context, userID uint) ([]*models.Resume, error)

	// DeleteResumeByID 根据简历ID删除简历及其详情
	DeleteResumeByID(ctx context.Context, resumeID uint) error

	// PermanentlyDeleteResumeByID 根据简历ID物理删除简历及其详情
	PermanentlyDeleteResumeByID(ctx context.Context, resumeID uint) error

	// RestoreResumeByID 根据简历ID恢复简历及其详情
	RestoreResumeByID(ctx context.Context, resumeID uint) error

	// CreateResume 创建新简历
	CreateResume(ctx context.Context, resume *models.Resume) error

	// CreateResumeDetail 创建新简历详情
	CreateResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error

	// CountByUserID 根据用户ID统计简历数量
	CountByUserID(ctx context.Context, userID uint) (int, error)
}

// resumeRepository 简历仓库实现
type resumeRepository struct {
	db *gorm.DB
}

// NewResumeRepository 创建简历仓库
func NewResumeRepository(db *gorm.DB) ResumeRepository {
	return &resumeRepository{
		db: db,
	}
}

// GetResumeWithDetailByID 根据简历ID获取简历及其详情
func (r *resumeRepository) GetResumeWithDetailByID(ctx context.Context, resumeID uint) (*models.Resume, *models.ResumeDetail, *models.Template, error) {
	var resume models.Resume
	var resumeDetail models.ResumeDetail
	var template models.Template

	// 获取简历基本信息
	if err := r.db.WithContext(ctx).First(&resume, resumeID).Error; err != nil {
		return nil, nil, nil, err
	}

	// 获取简历详情
	if err := r.db.WithContext(ctx).Where("resume_id = ?", resumeID).First(&resumeDetail).Error; err != nil {
		return nil, nil, nil, err
	}

	// 获取模板信息
	if err := r.db.WithContext(ctx).First(&template, resume.TemplateID).Error; err != nil {
		return nil, nil, nil, err
	}

	return &resume, &resumeDetail, &template, nil
}

// GetResumeByID 根据简历ID获取简历基本信息
func (r *resumeRepository) GetResumeByID(ctx context.Context, resumeID uint) (*models.Resume, error) {
	var resume models.Resume
	if err := r.db.WithContext(ctx).First(&resume, resumeID).Error; err != nil {
		return nil, err
	}
	return &resume, nil
}

// GetResumeDetailByResumeID 根据简历ID获取简历详情
func (r *resumeRepository) GetResumeDetailByResumeID(ctx context.Context, resumeID uint) (*models.ResumeDetail, error) {
	var resumeDetail models.ResumeDetail
	if err := r.db.WithContext(ctx).Where("resume_id = ?", resumeID).First(&resumeDetail).Error; err != nil {
		return nil, err
	}
	return &resumeDetail, nil
}

// CheckResumeOwnership 检查简历是否属于指定用户
func (r *resumeRepository) CheckResumeOwnership(ctx context.Context, resumeID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ? AND user_id = ?", resumeID, userID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CheckDeletedResumeOwnership 检查已删除简历是否属于指定用户
func (r *resumeRepository) CheckDeletedResumeOwnership(ctx context.Context, resumeID, userID uint) (*models.Resume, error) {
	var resume models.Resume
	err := r.db.WithContext(ctx).Unscoped().
		Where("id = ? AND user_id = ?", resumeID, userID).
		First(&resume).Error
	if err != nil {
		return nil, err
	}
	return &resume, nil
}

// SaveResumeDetail 保存简历详情
func (r *resumeRepository) SaveResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error {
	return r.db.WithContext(ctx).Save(resumeDetail).Error
}

// UpdateResumeUpdatedAt 更新简历的更新时间
func (r *resumeRepository) UpdateResumeUpdatedAt(ctx context.Context, resumeID uint) error {
	return r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ?", resumeID).
		Update("updated_at", gorm.Expr("NOW()")).Error
}

// UpdateResumeName 更新简历名称
func (r *resumeRepository) UpdateResumeName(ctx context.Context, resumeID uint, resumeName string) error {
	return r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ?", resumeID).
		Updates(map[string]interface{}{
			"resume_name": resumeName,
			"updated_at":  gorm.Expr("NOW()"),
		}).Error
}

// UpdateResumeTemplateID 更新简历模板ID
func (r *resumeRepository) UpdateResumeTemplateID(ctx context.Context, resumeID uint, templateID uint) error {
	return r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ?", resumeID).
		Updates(map[string]interface{}{
			"template_id": templateID,
			"updated_at":  gorm.Expr("NOW()"),
		}).Error
}

// UpdateResumeDetailStyle 更新简历详情样式
func (r *resumeRepository) UpdateResumeDetailStyle(ctx context.Context, resumeID uint, resumeStyle models.ResumeStyle) error {
	return r.db.WithContext(ctx).Model(&models.ResumeDetail{}).
		Where("resume_id = ?", resumeID).
		Updates(map[string]interface{}{
			"resume_style": resumeStyle,
			"updated_at":   gorm.Expr("NOW()"),
		}).Error
}

// UpdateResumePreviewImage 更新简历预览图URL
func (r *resumeRepository) UpdateResumePreviewImage(ctx context.Context, resumeID uint, previewImageUrl string) error {
	return r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ?", resumeID).
		Updates(map[string]interface{}{
			"preview_image_url": previewImageUrl,
			"updated_at":        gorm.Expr("NOW()"),
		}).Error
}

// GetResumesByUserID 根据用户ID获取所有简历（按更新时间排序）
func (r *resumeRepository) GetResumesByUserID(ctx context.Context, userID uint) ([]*models.Resume, error) {
	var resumes []*models.Resume
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Find(&resumes).Error
	if err != nil {
		return nil, err
	}
	return resumes, nil
}

// GetDeletedResumesByUserID 根据用户ID获取所有已删除的简历（回收站）
func (r *resumeRepository) GetDeletedResumesByUserID(ctx context.Context, userID uint) ([]*models.Resume, error) {
	var resumes []*models.Resume
	err := r.db.WithContext(ctx).Unscoped().
		Where("user_id = ? AND deleted_at IS NOT NULL", userID).
		Order("deleted_at DESC").
		Find(&resumes).Error
	if err != nil {
		return nil, err
	}
	return resumes, nil
}

// DeleteResumeByID 根据简历ID删除简历及其详情
func (r *resumeRepository) DeleteResumeByID(ctx context.Context, resumeID uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除简历详情
		if err := tx.Where("resume_id = ?", resumeID).Delete(&models.ResumeDetail{}).Error; err != nil {
			return err
		}

		// 再删除简历主表
		if err := tx.Delete(&models.Resume{}, resumeID).Error; err != nil {
			return err
		}

		return nil
	})
}

// PermanentlyDeleteResumeByID 根据简历ID物理删除简历及其详情
func (r *resumeRepository) PermanentlyDeleteResumeByID(ctx context.Context, resumeID uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先物理删除简历详情
		if err := tx.Unscoped().Where("resume_id = ?", resumeID).Delete(&models.ResumeDetail{}).Error; err != nil {
			return err
		}

		// 再物理删除简历主表
		if err := tx.Unscoped().Delete(&models.Resume{}, resumeID).Error; err != nil {
			return err
		}

		return nil
	})
}

// RestoreResumeByID 根据简历ID恢复简历及其详情
func (r *resumeRepository) RestoreResumeByID(ctx context.Context, resumeID uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先恢复简历主表
		if err := tx.Unscoped().Model(&models.Resume{}).Where("id = ?", resumeID).Update("deleted_at", nil).Error; err != nil {
			return err
		}

		// 再恢复简历详情
		if err := tx.Unscoped().Model(&models.ResumeDetail{}).Where("resume_id = ?", resumeID).Update("deleted_at", nil).Error; err != nil {
			return err
		}

		return nil
	})
}

// CreateResume 创建新简历
func (r *resumeRepository) CreateResume(ctx context.Context, resume *models.Resume) error {
	return r.db.WithContext(ctx).Create(resume).Error
}

// CreateResumeDetail 创建新简历详情
func (r *resumeRepository) CreateResumeDetail(ctx context.Context, resumeDetail *models.ResumeDetail) error {
	return r.db.WithContext(ctx).Create(resumeDetail).Error
}

// UpdateResumeCompletionRate 更新简历完成度
func (r *resumeRepository) UpdateResumeCompletionRate(ctx context.Context, resumeID uint, completionRate string) error {
	return r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("id = ?", resumeID).
		Updates(map[string]interface{}{
			"completion_rate": completionRate,
			"updated_at":      gorm.Expr("NOW()"),
		}).Error
}

// CountByUserID 根据用户ID统计简历数量
func (r *resumeRepository) CountByUserID(ctx context.Context, userID uint) (int, error) {
	var count int64

	if err := r.db.WithContext(ctx).Model(&models.Resume{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Count(&count).Error; err != nil {
		return 0, err
	}

	return int(count), nil
}
