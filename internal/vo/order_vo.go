package vo

import (
	"time"

	"github.com/avrilko/resume-server/internal/enum"
)

// OrderResponse 订单响应
type OrderResponse struct {
	OrderNo   string  `json:"order_no" example:"RS00011234567890"`          // 订单号
	Title     string  `json:"title" example:"【熊猫简历】购买会员套餐-月度会员"`            // 订单标题
	Amount    float64 `json:"amount" example:"29.9"`                        // 订单金额
	CodeURL   string  `json:"code_url" example:"https://qr.alipay.com/xxx"` // 支付二维码链接
	PackageID uint    `json:"package_id" example:"1"`                       // 套餐ID
}

// OrderListItem 订单列表项响应
type OrderListItem struct {
	ID               uint               `json:"id" example:"1"`                            // 订单ID
	OrderNo          string             `json:"order_no" example:"AL00011234567890"`       // 订单号
	Title            string             `json:"title" example:"【熊猫简历】购买会员套餐-月度会员"`         // 订单标题
	Amount           float64            `json:"amount" example:"29.9"`                     // 订单金额
	PaymentMethod    enum.PaymentMethod `json:"payment_method" example:"2"`                // 支付方式：1微信支付 2支付宝
	PaymentStatus    enum.PaymentStatus `json:"payment_status" example:"1"`                // 支付状态：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时
	PaymentStatusStr string             `json:"payment_status_str" example:"待支付"`          // 支付状态文字描述
	FailReason       string             `json:"fail_reason" example:""`                    // 支付失败原因
	CreatedAt        time.Time          `json:"created_at" example:"2023-01-01T12:00:00Z"` // 创建时间
	MembershipPlan   MembershipPlanInfo `json:"membership_plan"`                           // 套餐信息
}

// MembershipPlanInfo 套餐信息（用于订单列表）
type MembershipPlanInfo struct {
	ID   uint   `json:"id" example:"1"`      // 套餐ID
	Name string `json:"name" example:"月度会员"` // 套餐名称
}

// OrderStatusResponse 订单状态响应
type OrderStatusResponse struct {
	OrderNo       string  `json:"order_no" example:"AL00011234567890"`  // 订单号
	PaymentStatus int     `json:"payment_status" example:"1"`           // 支付状态(1:待支付 2:支付处理中 3:支付成功 4:支付失败 5:支付超时)
	Amount        float64 `json:"amount" example:"29.9"`                // 订单金额
	ChannelName   string  `json:"channel_name" example:"百度推广"`          // 用户渠道名称
	FailReason    string  `json:"fail_reason,omitempty" example:"支付超时"` // 支付失败原因，仅当支付状态为失败或超时时有值
}

// OrderListResponse 订单列表响应（用于Swagger文档）
type OrderListResponse struct {
	Total    int64           `json:"total" example:"100"`    // 总记录数
	Page     int             `json:"page" example:"1"`       // 当前页码
	PageSize int             `json:"page_size" example:"10"` // 每页条数
	List     []OrderListItem `json:"list"`                   // 订单列表
}
