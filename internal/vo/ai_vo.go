package vo

// ScoreResumeResponse AI简历打分响应
type ScoreResumeResponse struct {
	// 语言与表达评分
	LanguageExpression ScoreDimension `json:"language_expression"`
	// 信息完整性评分
	InformationCompleteness ScoreDimension `json:"information_completeness"`
	// 内容相关性评分
	ContentRelevance ScoreDimension `json:"content_relevance"`
	// 简历专业性评分
	Professionalism ScoreDimension `json:"professionalism"`
	// 总体评分
	OverallScore float64 `json:"overall_score" example:"85.5"`
	// 总体评价
	OverallComment string `json:"overall_comment" example:"简历整体质量较高，建议在项目经历部分增加更多量化数据"`
}

// ScoreDimension 评分维度
type ScoreDimension struct {
	// 评分 (0-100)
	Score float64 `json:"score" example:"85.5"`
	// 详细评价项目
	Details []ScoreDetailItem `json:"details"`
}

// ScoreDetailItem 详细评分项
type ScoreDetailItem struct {
	// 小标题
	Title string `json:"title" example:"语言表达清晰度"`
	// 评价
	Comment string `json:"comment" example:"语言表达清晰，逻辑性强，但部分描述可以更加简洁"`
}
