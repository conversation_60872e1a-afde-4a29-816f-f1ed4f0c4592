package vo

// CategoryTagResponse 分类标签响应结构
type CategoryTagResponse struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

// CategoryItemResponse 分类项响应结构
type CategoryItemResponse struct {
	ID     uint   `json:"id"`
	Name   string `json:"name"`
	SlugCn string `json:"slug_cn"`
	SlugEn string `json:"slug_en"`
}

// CategoryGroupResponse 分类分组响应结构
type CategoryGroupResponse struct {
	Name         string                 `json:"name"`
	CategoryType int                    `json:"category_type"`
	Children     []CategoryItemResponse `json:"children"`
}

// CategoryListResponse 分类列表响应结构
type CategoryListResponse []CategoryGroupResponse

// CategoryDetailResponse 分类详情响应结构
type CategoryDetailResponse struct {
	CategoryType        int                                     `json:"category_type"`
	SelectData          CategoryItemWithChildrenResponse        `json:"select_data"`
	HasData             bool                                    `json:"has_data"`             // 是否查到数据
	Examples            *PaginatedList[ExampleListItemResponse] `json:"examples,omitempty"`   // 示例数据（仅职位分类时有值）
	Tags                []CategoryTagResponse                   `json:"tags"`                 // 分类标签
	MoreRecommendations []ExampleListItemResponse               `json:"more_recommendations"` // 更多推荐
	Description         string                                  `json:"description"`          // 描述
}

// CategoryItemWithChildrenResponse 带有完整层级结构的分类项响应
type CategoryItemWithChildrenResponse struct {
	ID       uint                               `json:"id"`
	Name     string                             `json:"name"`
	SlugCn   string                             `json:"slug_cn"`
	SlugEn   string                             `json:"slug_en"`
	Children []CategoryItemWithChildrenResponse `json:"children,omitempty"`
}

// CategoryTdkResponse 分类TDK响应VO
type CategoryTdkResponse struct {
	ID          uint   `json:"id" example:"1"`
	Title       string `json:"title" example:"软件工程师简历模板"`
	Description string `json:"description" example:"专业的软件工程师简历模板，适合后端开发岗位"`
	Keywords    any    `json:"keywords"`
}
