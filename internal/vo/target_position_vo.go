package vo

import (
	"time"
)

// TargetPositionResponse 目标岗位响应视图对象
type TargetPositionResponse struct {
	ID             uint      `json:"id" example:"1"`
	UserID         uint      `json:"user_id" example:"1"`
	PositionName   string    `json:"position_name" example:"前端开发工程师"`
	CompanyName    string    `json:"company_name" example:"阿里巴巴"`
	JobSource      string    `json:"job_source" example:"Boss直聘"`
	JobDescription string    `json:"job_description" example:"负责前端页面开发..."`
	CreatedAt      time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt      time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// TargetPositionListResponse 目标岗位列表响应视图对象
type TargetPositionListResponse struct {
	List  []TargetPositionResponse `json:"list"`
	Total int64                    `json:"total" example:"10"`
}

// CreateTargetPositionResponse 创建目标岗位响应视图对象
type CreateTargetPositionResponse struct {
	ID uint `json:"id" example:"1"`
}

// ResumeScoreDetailResponse 简历评分详情响应视图对象
type ResumeScoreDetailResponse struct {
	ID                             uint              `json:"id" example:"1"`
	UserID                         uint              `json:"user_id" example:"1"`
	ResumeID                       uint              `json:"resume_id" example:"1"`
	ResumeName                     string            `json:"resume_name" example:"我的简历"`
	TargetPositionID               uint              `json:"target_position_id" example:"1"`
	TargetPositionName             string            `json:"target_position_name" example:"前端开发工程师"`
	LanguageExpressionScore        float64           `json:"language_expression_score" example:"85.5"`
	LanguageExpressionDetails      []ScoreDetailItem `json:"language_expression_details"`
	InformationCompletenessScore   float64           `json:"information_completeness_score" example:"82.0"`
	InformationCompletenessDetails []ScoreDetailItem `json:"information_completeness_details"`
	ContentRelevanceScore          float64           `json:"content_relevance_score" example:"78.0"`
	ContentRelevanceDetails        []ScoreDetailItem `json:"content_relevance_details"`
	ProfessionalismScore           float64           `json:"professionalism_score" example:"88.0"`
	ProfessionalismDetails         []ScoreDetailItem `json:"professionalism_details"`
	OverallScore                   float64           `json:"overall_score" example:"83.4"`
	OverallComment                 string            `json:"overall_comment" example:"简历整体质量较高..."`
	CreatedAt                      time.Time         `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt                      time.Time         `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// ResumeScoreListItem 简历评分列表项视图对象
type ResumeScoreListItem struct {
	ID           uint      `json:"id" example:"1"`
	ResumeID     uint      `json:"resume_id" example:"1"`
	ResumeName   string    `json:"resume_name" example:"我的简历"`
	OverallScore float64   `json:"overall_score" example:"83.4"`
	CreatedAt    time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
}

// ResumeScoreListResponse 简历评分列表响应视图对象
type ResumeScoreListResponse struct {
	List  []ResumeScoreListItem `json:"list"`
	Total int64                 `json:"total" example:"10"`
}
