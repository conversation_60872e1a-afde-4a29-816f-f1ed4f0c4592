package vo

// QrCodeLoginResponse 登录二维码响应
type QrCodeLoginResponse struct {
	// 二维码图片URL
	QrCodeUrl string `json:"qr_code_url" example:"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxxxx"`
	// 场景值ID，前端需要保存此ID用于后续查询扫码状态
	SceneId string `json:"scene_id" example:"login_123456789"`
}

// QrCodeStatusResponse 二维码状态响应
type QrCodeStatusResponse struct {
	// 二维码状态：PENDING(待扫描)、SCANNED(已扫描)、EXPIRED(已过期)
	Status string `json:"status" example:"SCANNED"`
	// 令牌，仅当状态为SCANNED时返回
	Token string `json:"token,omitempty" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}
