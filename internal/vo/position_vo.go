package vo

// PositionItemResponse 职位项响应结构
type PositionItemResponse struct {
	ID       uint                   `json:"id"`
	Name     string                 `json:"name"`
	SlugCn   string                 `json:"slug_cn"`
	SlugEn   string                 `json:"slug_en"`
	Children []PositionItemResponse `json:"children,omitempty"`
}

// PositionListResponse 职位列表响应结构
type PositionListResponse []PositionItemResponse

// SearchResultItem 搜索结果项
type SearchResultItem struct {
	ID     string `json:"id"`      // 文档ID，格式：position_123 或 example_456
	Type   int    `json:"type"`    // 1为position 2为example
	Name   string `json:"name"`    // position的名称或者example表name
	SlugCn string `json:"slug_cn"` // slug_cn字段
	URL    string `json:"url"`     // position为/jianli/{slug_cn} example为/jianli/{id}.html
}

// SearchResponse 搜索响应结构
type SearchResponse struct {
	Hits           []SearchResultItem `json:"hits"`               // 搜索结果列表
	Query          string             `json:"query"`              // 搜索关键词
	ProcessingTime int64              `json:"processingTimeMs"`   // 处理时间（毫秒）
	Limit          int                `json:"limit"`              // 返回条数限制
	Offset         int                `json:"offset"`             // 偏移量
	EstimatedTotal int64              `json:"estimatedTotalHits"` // 预估总数
}
