package vo

import (
	"time"

	"github.com/avrilko/resume-server/internal/models"
)

// HotResumeRecommendation 热门简历推荐项
type HotResumeRecommendation struct {
	Name       string `json:"name" example:"软件工程师简历模板"`   // 简历名称
	Link       string `json:"link" example:"/jianli/123"` // 简历链接
	UsageCount int    `json:"usage_count" example:"1250"` // 使用人数
}

// ExampleDetailResponse 示例详情响应VO
type ExampleDetailResponse struct {
	ID            uint   `json:"id" example:"1"`
	TemplateID    uint   `json:"template_id" example:"1"`
	ComponentName string `json:"component_name" example:"ResumeTemplate1"`
	UsageCount    int    `json:"usage_count" example:"1250"`           // 使用人数
	Desc          string `json:"desc" example:"专业的软件工程师简历模板，适合后端开发岗位"` // 描述

	// 简历内容字段
	BasicInfo       models.BasicInfo       `json:"basic_info"`
	Education       models.Education       `json:"education"`
	Work            models.Work            `json:"work"`
	Project         models.Project         `json:"project"`
	Research        models.Research        `json:"research"`
	Team            models.Team            `json:"team"`
	Portfolio       models.Portfolio       `json:"portfolio"`
	Other           models.Other           `json:"other"`
	PersonalSummary models.PersonalSummary `json:"personal_summary"`
	Honors          models.Honors          `json:"honors"`
	Skills          models.Skills          `json:"skills"`
	CustomModules   models.CustomModules   `json:"custom_modules"`
	Slogan          models.Slogan          `json:"slogan"`
	ResumeStyle     models.ResumeStyle     `json:"resume_style"`

	// 分类标签和推荐字段
	Tags                     []CategoryTagResponse     `json:"tags"`                       // 分类标签
	MoreRecommendations      []ExampleListItemResponse `json:"more_recommendations"`       // 更多推荐
	HotResumeRecommendations []HotResumeRecommendation `json:"hot_resume_recommendations"` // 热门简历推荐

	// 时间字段
	CreatedAt time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// ExampleTdkResponse 示例TDK响应VO
type ExampleTdkResponse struct {
	ID          uint               `json:"id" example:"1"`
	Title       string             `json:"title" example:"软件工程师简历模板"`
	Description string             `json:"description" example:"专业的软件工程师简历模板，适合后端开发岗位"`
	Keywords    models.StringArray `json:"keywords" example:"[\"软件工程师\",\"简历模板\",\"后端开发\"]"`
}

// UseExampleResponse 使用例子响应VO
type UseExampleResponse struct {
	ResumeID uint `json:"resume_id" example:"123"`
}

// ExampleListItemResponse 示例列表项响应VO
type ExampleListItemResponse struct {
	ID              uint     `json:"id" example:"1"`                                                // 示例ID
	PreviewImageUrl string   `json:"preview_image_url" example:"https://example.com/preview/1.jpg"` // 预览图链接
	TemplateID      uint     `json:"template_id" example:"1"`                                       // 模板ID
	Tags            []string `json:"tags" example:"['简洁','专业','技术']"`                               // 模板标签
	Name            string   `json:"name" example:"软件工程师简历模板"`                                      // 模板名称
	UsageCount      int      `json:"usage_count" example:"1250"`                                    // 使用人数
}

// ExampleListSwaggerResponse 示例列表响应VO（用于Swagger文档）
type ExampleListSwaggerResponse struct {
	Total    int64                     `json:"total" example:"100"`    // 总记录数
	Page     int                       `json:"page" example:"1"`       // 当前页码
	PageSize int                       `json:"page_size" example:"40"` // 每页条数
	List     []ExampleListItemResponse `json:"list"`                   // 示例列表
}
