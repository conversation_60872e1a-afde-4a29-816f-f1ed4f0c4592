# Speed Fox Server 项目开发规范

## 目录结构及命名规则

### 顶层目录

- **`main.go`**: 应用入口，包含主程序
- **`config/`**: 应用配置相关代码
- **`internal/`**: 内部包代码，不对外暴露
- **`scripts/`**: 辅助脚本文件
- **`tests/`**: 测试文件
- **`.env`和`.env.example`**: 环境变量配置
- **`.air.toml`**: Air 热重载配置

### internal 目录结构

- **`models/`**: 数据模型定义
- **`dto/`**: 数据传输对象(Data Transfer Object)
- **`vo/`**: 视图对象(View Object)
- **`controllers/`**: 控制器，处理 HTTP 请求
- **`services/`**: 业务逻辑层
- **`repository/`**: 数据访问层
- **`middleware/`**: HTTP 中间件
- **`routes/`**: 路由定义
- **`pkg/`**: 共享工具包(日志、验证器等)
- **`exception/`**: 错误和异常处理
- **`response/`**: 响应处理
- **`wire/`**: 依赖注入相关
- **`enum/`**: 枚举类型定义，集中管理常量和状态值

## 命名规则

### 文件命名

- 使用蛇形命名法(snake_case): `user_controller.go`, `user_service.go`
- 包名与目录名一致: `package controllers` in `controllers/user_controller.go`

### 结构体和接口命名

- 使用大驼峰命名法(PascalCase): `UserController`, `UserService`
- 接口名通常以动词或能力描述: `JWTService`, `UserRepository`

### 函数和方法命名

- 公开函数/方法使用大驼峰命名法(PascalCase): `RegisterUserRoutes`, `NewUserController`
- 私有函数/方法使用小驼峰命名法(camelCase): `convertToUserResponse`

### 变量命名

- 局部变量使用小驼峰命名法: `userService`, `jwtService`
- 常量使用大写蛇形命名法: `JWT_SECRET`

## API 接口开发规范

### 依赖注入文件规范

每个主要目录都应该包含一个 `providerset.go` 文件，用于管理该目录下的依赖注入：

1. **controllers/providerset.go**

   ```go
   package controllers

   import "go.uber.org/wire"

   // ControllerProviderSet 控制器提供者集合
   var ControllerProviderSet = wire.NewSet(
       NewUserController,
       NewAuthController,
       // ... 其他控制器
   )
   ```

2. **services/providerset.go**

   ```go
   package services

   import "go.uber.org/wire"

   // ServiceProviderSet 服务层提供者集合
   var ServiceProviderSet = wire.NewSet(
       NewUserService,
       NewCodeService,
       // ... 其他服务
   )
   ```

3. **repository/providerset.go**

   ```go
   package repository

   import "go.uber.org/wire"

   // RepositoryProviderSet 数据访问层提供者集合
   var RepositoryProviderSet = wire.NewSet(
       NewUserRepository,
       // ... 其他仓储
   )
   ```

4. **pkg/providerset.go**

   ```go
   package pkg

   import "go.uber.org/wire"

   // InfraProviderSet 基础设施提供者集合
   var InfraProviderSet = wire.NewSet(
       NewJWTService,
       NewOSSService,
       NewSMSService,
       // ... 其他基础设施服务
   )
   ```

5. **wire/wire.go**

   ```go
   package wire

   import (
       "github.com/avrilko/resume-server/internal/controllers"
       "github.com/avrilko/resume-server/internal/services"
       "github.com/avrilko/resume-server/internal/repository"
       "github.com/avrilko/resume-server/internal/pkg"
       "go.uber.org/wire"
   )

   // InitializeApplication 初始化应用程序
   func InitializeApplication() (*Application, error) {
       wire.Build(
           controllers.ControllerProviderSet,
           services.ServiceProviderSet,
           repository.RepositoryProviderSet,
           pkg.InfraProviderSet,
           // ... 其他依赖
           wire.Struct(new(Application), "*"),
       )
       return nil, nil
   }
   ```

依赖注入文件命名规范：

1. 文件名统一使用 `providerset.go`
2. 提供者集合变量名使用 `XXXProviderSet` 格式
3. 每个目录的 `providerset.go` 只负责管理本目录下的依赖
4. 在 `wire.go` 中统一组装所有依赖

最佳实践：

1. 保持提供者集合的单一职责
2. 相关的依赖应该组织在同一个提供者集合中
3. 避免循环依赖
4. 使用 `wire.Build` 时注意依赖注入的顺序
5. 为每个服务提供接口定义
6. 在 `wire.go` 中使用 `wire.Struct` 而不是手动构造函数
7. 使用 `//go:build wireinject` 标记 `wire.go` 文件

### API 架构

采用分层架构:

1. 控制器层(Controller): 处理 HTTP 请求
2. 服务层(Service): 实现业务逻辑
3. 数据访问层(Repository): 数据库操作
4. 模型层(Model): 数据模型定义

#### 控制器与服务层的职责分工

**控制器(Controller)职责**：

- 接收并验证 HTTP 请求参数
- 调用适当的服务层方法处理业务逻辑
- 处理服务层返回的结果并构造 HTTP 响应
- 捕获服务层抛出的异常并转换为 HTTP 响应
- 记录关键错误日志
- 处理 HTTP 状态码、头信息等

控制器应保持轻量化，不包含复杂业务逻辑。

**服务层(Service)职责**：

- 实现所有业务规则和流程
- 协调多个仓储层的操作
- 处理事务和数据一致性
- 数据验证和转换
- 业务实体间的关系处理
- 处理复杂算法和业务流程
- 多步骤操作的协调
- 外部服务调用的封装

对于非常复杂的业务逻辑，可以进一步将服务层细分：

1. **核心领域服务**：实现特定领域的业务逻辑，如`OrderService`、`PaymentService`
2. **应用服务**：编排多个领域服务，处理跨领域的流程
3. **基础设施服务**：提供底层技术功能，如`CacheService`、`FileStorageService`
4. **第三方集成服务**：封装外部 API 调用，如`SMSService`、`PaymentGatewayService`

### 请求和响应流程

1. 请求经过路由系统到达对应控制器
2. 通过中间件进行请求预处理(验证、身份认证等)
3. 控制器调用服务层处理业务逻辑
4. 服务层调用仓储层进行数据操作
5. 控制器将处理结果转换为视图对象(VO)返回

### 接口开发步骤

1. **定义 DTO (Data Transfer Object)**

   ```go
   // 请求DTO示例
   type RegisterRequest struct {
       Username string `json:"username" binding:"required,min=3,max=50" example:"johndoe" label:"用户名"`
       Password string `json:"password" binding:"required,min=6,max=20" example:"password123" label:"密码"`
       Email    string `json:"email" binding:"required,email" example:"<EMAIL>" label:"邮箱"`
   }
   ```

2. **定义模型 (Model)**

   ```go
   // 数据模型示例
   type User struct {
       ID        uint      `json:"id" gorm:"primaryKey"`
       Username  string    `json:"username" gorm:"size:50;not null;unique;comment:用户名"`
       Password  string    `json:"-" gorm:"size:255;not null;comment:密码"`
       Email     string    `json:"email" gorm:"size:100;unique;comment:邮箱"`
       // ...其他字段
   }
   ```

3. **定义视图对象 (VO)**

   ```go
   // 响应VO示例
   type UserResponse struct {
       ID        uint   `json:"id" example:"1"`
       Username  string `json:"username" example:"johndoe"`
       Email     string `json:"email" example:"<EMAIL>"`
       // ...其他字段
   }
   ```

4. **实现数据访问层 (Repository)**

   ```go
   // Repository示例
   type UserRepository interface {
       Create(user *models.User) error
       GetByID(id uint) (*models.User, error)
       // ...其他方法
   }
   ```

5. **实现业务逻辑层 (Service)**

   ```go
   // Service示例
   type UserService interface {
       Register(username, password, email string) (*models.User, error)
       Login(username, password string) (*models.User, error)
       // ...其他方法
   }
   ```

6. **实现控制器 (Controller)**

   ```go
   // Controller示例
   func (c *UserController) Register(ctx *gin.Context, req dto.RegisterRequest) {
       user, err := c.userService.Register(req.Username, req.Password, req.Email)
       if err != nil {
           pkg.Error("用户注册失败", zap.Error(err))
           response.ThrowError(ctx, exception.ErrInvalidParam.WithDetail(err.Error()))
           return
       }
       response.SuccessJSON(ctx, "注册成功", convertToUserResponse(user))
   }
   ```

7. **注册路由**
   ```go
   // 路由示例
   userGroup.POST("/register", middleware.WithValidation(userController.Register))
   ```

### 错误处理

1. 使用`exception`包定义标准错误

   ```go
   // 预定义错误示例
   var ErrUserNotFound = New(http.StatusNotFound, 40401, "用户不存在")
   ```

2. 使用`response.ThrowError`抛出错误

   ```go
   response.ThrowError(ctx, exception.ErrUserNotFound)
   ```

3. 可以通过`WithDetail`和`WithMessage`添加错误详情
   ```go
   response.ThrowError(ctx, exception.ErrInvalidParam.WithDetail(err.Error()))
   ```

### 中间件应用

1. 验证中间件：自动绑定和验证请求数据

   ```go
   userGroup.POST("/register", middleware.WithValidation(userController.Register))
   ```

2. 身份认证中间件：验证 JWT 令牌

   ```go
   authGroup.Use(middleware.JWTAuthMiddleware(jwtService))
   ```

3. 错误处理中间件：统一捕获和处理错误
   ```go
   router.Use(middleware.ErrorHandler())
   ```

## 最佳实践

1. **依赖注入**：使用 Wire 框架实现依赖注入，解耦组件
2. **统一响应格式**：使用`response`包提供的函数返回统一格式的响应
3. **规范日志**：使用`pkg`包中的日志函数记录关键操作和错误
4. **参数验证**：使用标签定义验证规则，由中间件自动验证
5. **错误处理**：使用`exception`包定义标准错误，保持一致的错误处理方式
6. **接口文档**：使用标签和示例值为 API 提供清晰的文档
7. **Swagger API 文档**：使用 Swagger 注解自动生成 API 文档
   ```go
   // @Summary 用户注册
   // @Description 创建新用户账号
   // @Tags 用户管理
   // @Accept json
   // @Produce json
   // @Param request body dto.RegisterRequest true "注册信息"
   // @Success 200 {object} vo.SuccessResponse{data=vo.UserResponse} "注册成功"
   // @Failure 400 {object} vo.ErrorResponse "参数错误"
   // @Router /users/register [post]
   func (c *UserController) Register(ctx *gin.Context, req dto.RegisterRequest) {
       // 实现细节...
   }
   ```
   - 使用`swag init -g main.go`命令生成文档
   - 开发时通过`http://localhost:8080/swagger/index.html`访问 API 文档
   - 使用`example`标签为字段提供示例值，增强文档可读性
   - 为每个接口提供详细的参数说明和响应示例
   - 使用`Tags`对 API 进行分组管理
8. **使用泛型**：对于通用数据结构，优先使用泛型设计以提高代码复用性

   ```go
   // 使用泛型定义分页列表结构
   type PaginatedList[T any] struct {
       Total    int64 `json:"total" example:"100"`
       Page     int   `json:"page" example:"1"`
       PageSize int   `json:"page_size" example:"10"`
       List     []T   `json:"list"`
   }

   // 使用泛型创建用户列表
   userList := NewPaginatedList(userResponses, total, page, pageSize)
   ```

   - 对于需要在多个地方重复使用的数据结构，考虑使用泛型设计
   - 在 Swagger 文档中使用`{data=vo.PaginatedList[vo.UserResponse]}`格式指定泛型类型
   - 为泛型结构提供工厂方法，如`NewPaginatedList`，便于创建实例
   - 当需要对现有非泛型结构进行兼容时，提供转换方法并标记原结构为已弃用

9. **枚举类型管理**：使用`enum`包集中管理枚举类型，提高代码可读性和一致性

   ```go
   // enum/payment_status.go
   package enum

   // PaymentStatus 支付状态枚举
   type PaymentStatus int

   const (
       // PaymentStatusPending 待支付
       PaymentStatusPending PaymentStatus = iota
       // PaymentStatusSuccess 支付成功
       PaymentStatusSuccess
       // PaymentStatusFailed 支付失败
       PaymentStatusFailed
       // PaymentStatusRefunded 已退款
       PaymentStatusRefunded
   )

   // String 返回人类可读的字符串表示
   func (s PaymentStatus) String() string {
       switch s {
       case PaymentStatusPending:
           return "待支付"
       case PaymentStatusSuccess:
           return "支付成功"
       case PaymentStatusFailed:
           return "支付失败"
       case PaymentStatusRefunded:
           return "已退款"
       default:
           return "未知状态"
       }
   }

   // IsValid 检查是否为有效的枚举值
   func (s PaymentStatus) IsValid() bool {
       return s >= PaymentStatusPending && s <= PaymentStatusRefunded
   }
   ```

   - 每个领域的枚举应单独放在一个文件中，使用蛇形命名法
   - 枚举类型名使用大驼峰命名法，枚举值使用类型名作为前缀
   - 为每个枚举类型实现必要的方法：`String()`、`IsValid()`等

10. **避免控制器中的复杂逻辑**：保持控制器简洁，将复杂业务逻辑移至服务层

    **不推荐**：控制器中包含复杂业务逻辑

    ```go
    // 控制器中包含复杂业务逻辑 - 不推荐
    func (c *OrderController) CompleteOrder(ctx *gin.Context, req dto.CompleteOrderRequest) {
        order, err := c.orderRepo.GetByID(req.OrderID)
        if err != nil {
            response.ThrowError(ctx, exception.ErrOrderNotFound)
            return
        }

        // 检查订单状态
        if order.Status != enum.OrderStatusPending {
            response.ThrowError(ctx, exception.ErrInvalidOrderStatus)
            return
        }

        // 扣减库存逻辑...
        // 更新订单状态逻辑...

        response.SuccessJSON(ctx, "订单完成", nil)
    }
    ```

    **推荐**：控制器保持简洁，复杂逻辑在服务层

    ```go
    // 控制器 - 保持简洁
    func (c *OrderController) CompleteOrder(ctx *gin.Context, req dto.CompleteOrderRequest) {
        // 调用服务层处理复杂业务逻辑
        err := c.orderService.CompleteOrder(req.OrderID)
        if err != nil {
            // 错误已经包含了业务含义，直接传递
            response.ThrowError(ctx, err)
            return
        }

        response.SuccessJSON(ctx, "订单完成", nil)
    }

    // 服务层 - 实现复杂业务逻辑
    func (s *orderService) CompleteOrder(orderID uint) error {
        // 获取订单
        // 检查订单状态
        // 使用事务确保数据一致性
        // 扣减库存
        // 更新订单状态
        // 发送通知
        // ...

        return nil
    }
    ```
