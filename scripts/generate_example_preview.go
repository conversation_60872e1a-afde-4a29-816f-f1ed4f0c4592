package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"runtime"
	"sync"
	"time"

	"github.com/avrilko/resume-server/internal/pkg"
	"go.uber.org/zap"
)

// 生成示例预览图脚本：查询example表中preview_image_url为空的数据，调用GenerateElementImage生成预览图并上传到OSS
//
// 使用方法：
// 1. 确保 .env 文件已正确配置（数据库、OSS、前端域名等）
// 2. 在项目根目录运行：./scripts/run_generate_example_preview.sh
// 3. 或者在项目根目录运行：go run scripts/generate_example_preview.go scripts/script_base.go
// 4. 自定义线程数：go run scripts/generate_example_preview.go scripts/script_base.go -workers=10
// 5. 限制处理数量：go run scripts/generate_example_preview.go scripts/script_base.go -workers=5 -limit=100
//
// 脚本功能：
// - 查询 example 表中 preview_image_url 为空的记录
// - 为每个示例生成预览页面 URL：{frontendDomain}/gen/{id}
// - 调用 Chrome 服务截取 elementID 为 "gen" 的元素图片
// - 上传图片到 OSS，文件名格式：resume_preview_随机数.png
// - 更新 example 表的 preview_image_url 字段
func main() {
	// 解析命令行参数
	var workers = flag.Int("workers", runtime.NumCPU(), "并发工作线程数")
	var limit = flag.Int("limit", 0, "限制处理的示例数量，0表示不限制")
	flag.Parse()

	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		log.Fatalf("初始化脚本基础失败: %v", err)
	}
	defer scriptBase.Close()

	scriptBase.Info("开始执行生成示例预览图脚本",
		zap.Int("workers", *workers),
		zap.Int("limit", *limit))

	// 获取脚本服务
	scriptService := scriptBase.GetScriptService()

	// 获取所需的服务
	chromeService := scriptService.GetChromeService()
	ossService := scriptService.GetOSSService()
	config := scriptService.GetConfig()

	// 获取数据库连接，直接查询预览图为空的示例
	db := scriptService.GetDB()

	// 查询预览图为空的示例
	type ExampleItem struct {
		ID uint `json:"id"`
	}
	var examples []ExampleItem

	err = db.WithContext(scriptBase.Context).
		Table("example").
		Select("id").
		Where("preview_image_url = '' OR preview_image_url IS NULL").
		Order("sort ASC").
		Find(&examples).Error

	if err != nil {
		scriptBase.Fatal("查询示例失败", zap.Error(err))
		return
	}

	if len(examples) == 0 {
		scriptBase.Info("没有找到需要生成预览图的示例")
		return
	}

	// 应用限制
	if *limit > 0 && len(examples) > *limit {
		examples = examples[:*limit]
		scriptBase.Info("应用数量限制", zap.Int("limited_count", len(examples)))
	}

	scriptBase.Info("找到需要生成预览图的示例", zap.Int("count", len(examples)))

	// 创建工作队列和结果统计
	exampleChan := make(chan ExampleItem, len(examples))
	var wg sync.WaitGroup
	var mu sync.Mutex
	successCount := 0
	failCount := 0

	// 将示例放入队列
	for _, example := range examples {
		exampleChan <- example
	}
	close(exampleChan)

	// 启动工作协程
	for i := 0; i < *workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for example := range exampleChan {
				scriptBase.Info("开始处理示例",
					zap.Uint("example_id", example.ID),
					zap.Int("worker_id", workerID))

				// 构建预览页面URL
				previewURL := fmt.Sprintf("%s/make/gen/%d/", config.FrontendDomain, example.ID)

				// 调用Chrome服务生成指定元素的图片（使用JPEG压缩）
				imageOptions := &pkg.ImageOptions{
					Format:  "jpeg",
					Quality: 40, // 高质量JPEG
					Scale:   1.0,
				}
				imageBytes, err := chromeService.GenerateElementImageWithOptions(previewURL, "gen", "", "xxxxx", imageOptions)
				if err != nil {
					scriptBase.Error("生成示例预览图失败",
						zap.Uint("example_id", example.ID),
						zap.String("preview_url", previewURL),
						zap.Error(err))
					mu.Lock()
					failCount++
					mu.Unlock()
					continue
				}

				scriptBase.Info("成功生成预览图",
					zap.Uint("example_id", example.ID),
					zap.Int("image_size", len(imageBytes)))

				// 生成随机文件名（JPEG格式）
				randomSuffix := rand.Intn(999999)
				fileName := fmt.Sprintf("example_preview_%06d.jpg", randomSuffix)
				objectName := fmt.Sprintf("gen/%s", fileName)

				// 上传图片到OSS
				imageURL, err := ossService.UploadBytes(scriptBase.Context, imageBytes, objectName, "image/jpeg")
				if err != nil {
					scriptBase.Error("上传示例预览图到OSS失败",
						zap.Uint("example_id", example.ID),
						zap.String("object_name", objectName),
						zap.Error(err))
					mu.Lock()
					failCount++
					mu.Unlock()
					continue
				}

				// 更新示例表的预览图URL
				err = db.WithContext(scriptBase.Context).
					Table("example").
					Where("id = ?", example.ID).
					Updates(map[string]interface{}{
						"preview_image_url": imageURL,
						"updated_at":        time.Now(),
					}).Error

				if err != nil {
					scriptBase.Error("更新示例预览图URL失败",
						zap.Uint("example_id", example.ID),
						zap.String("image_url", imageURL),
						zap.Error(err))
					mu.Lock()
					failCount++
					mu.Unlock()
					continue
				}

				scriptBase.Info("成功更新示例预览图URL",
					zap.Uint("example_id", example.ID),
					zap.String("image_url", imageURL))

				mu.Lock()
				successCount++
				mu.Unlock()
			}
		}(i)
	}

	// 等待所有工作协程完成
	wg.Wait()

	scriptBase.Info("脚本执行完成",
		zap.Int("total", len(examples)),
		zap.Int("success", successCount),
		zap.Int("failed", failCount))

	if failCount > 0 {
		scriptBase.Warn("部分示例处理失败", zap.Int("failed_count", failCount))
	}
}
