//go:build ignore

package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/avrilko/resume-server/config"
	"github.com/avrilko/resume-server/internal/pkg"
	"github.com/avrilko/resume-server/internal/wire"
	"go.uber.org/zap"
)

// 独立的MeiliSearch数据初始化脚本
func main() {
	fmt.Println("🚀 开始执行MeiliSearch数据初始化脚本...")

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化日志
	if err := pkg.InitLogger(cfg); err != nil {
		log.Fatalf("❌ 初始化日志失败: %v", err)
	}

	// 初始化验证器
	if err := pkg.InitValidator(); err != nil {
		log.Fatalf("❌ 初始化验证器失败: %v", err)
	}

	// 初始化数据库
	if err := pkg.InitDB(cfg); err != nil {
		log.Fatalf("❌ 初始化数据库失败: %v", err)
	}

	// 初始化Redis
	if err := pkg.InitRedis(cfg); err != nil {
		log.Fatalf("❌ 初始化Redis失败: %v", err)
	}

	// 构建脚本服务
	scriptService, err := wire.BuildScriptService()
	if err != nil {
		log.Fatalf("❌ 构建脚本服务失败: %v", err)
	}

	// 获取logger和positionService
	logger := scriptService.GetLogger()
	positionService := scriptService.GetPositionService()

	logger.Info("开始执行MeiliSearch数据初始化脚本")

	// 创建上下文
	ctx := context.Background()

	// 调用初始化方法
	err = positionService.InitializeMeiliSearchData(ctx)
	if err != nil {
		logger.Error("MeiliSearch数据初始化失败", zap.Error(err))
		fmt.Printf("❌ MeiliSearch数据初始化失败: %v\n", err)
		
		// 清理资源
		pkg.CloseRedis()
		if logger != nil {
			logger.Sync()
		}
		os.Exit(1)
	}

	logger.Info("MeiliSearch数据初始化完成")
	fmt.Println("✅ MeiliSearch数据初始化成功完成！")

	// 清理资源
	if err := pkg.CloseRedis(); err != nil {
		log.Printf("关闭Redis连接失败: %v", err)
	}

	if logger != nil {
		logger.Sync()
	}
}
