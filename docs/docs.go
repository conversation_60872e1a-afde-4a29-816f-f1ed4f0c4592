// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/ai/batch-validate-privilege": {
            "post": {
                "description": "批量校验用户是否可以使用多项权益，返回权限校验结果、弹窗类型、弹窗标题和描述文案",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "批量权限校验",
                "parameters": [
                    {
                        "description": "批量权限校验请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchValidatePrivilegeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "校验成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_BatchValidatePrivilegeResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/generate-resume": {
            "post": {
                "description": "根据用户提供的话术，使用AI生成完整的简历数据，包括基本信息、教育经历、工作经历、项目经历和个人总结",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "AI生成简历",
                "parameters": [
                    {
                        "description": "AI生成简历请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.GenerateResumeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "生成成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-dto_GenerateResumeResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/optimize-resume": {
            "post": {
                "description": "根据简历ID，使用AI优化简历内容，将优化后的内容保存到简历草稿表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "AI优化简历",
                "parameters": [
                    {
                        "description": "AI优化简历请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.OptimizeResumeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "优化成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-dto_OptimizeResumeResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/parse-file": {
            "post": {
                "description": "上传文件并解析内容，支持pdf、txt、csv、docx、doc、xlsx、xls、pptx、ppt、md、mobi、epub格式，最大10MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "文件解析",
                "parameters": [
                    {
                        "type": "file",
                        "description": "要解析的文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解析成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_ParseFileResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/prompt": {
            "post": {
                "description": "根据简历模块和提示词类型，使用AI生成相应的内容，支持流式传输",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "AI提示词处理",
                "parameters": [
                    {
                        "description": "AI提示词请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PromptRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "AI生成的内容（流式传输）",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/records": {
            "get": {
                "description": "根据简历ID分页获取AI调用记录列表，返回枚举的字符串值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "获取AI调用记录列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "AI调用记录列表",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/ai/score-resume": {
            "post": {
                "description": "根据简历ID和目标岗位ID，使用AI对简历进行四维度打分评估：语言与表达、信息完整性、内容相关性、简历专业性。每个维度包含总分和详细评价项。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/AI助手"
                ],
                "summary": "AI简历打分",
                "parameters": [
                    {
                        "description": "AI简历打分请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ScoreResumeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "打分成功，返回四维度评分结果",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_ScoreResumeResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "403": {
                        "description": "权益不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "404": {
                        "description": "简历或目标岗位不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/auth/email/bind-email-code": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "向指定邮箱发送绑定邮箱验证码，验证码有效期5分钟，同一邮箱1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送绑定邮箱验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendEmailCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/email/code": {
            "post": {
                "description": "向指定邮箱发送验证码，验证码有效期5分钟，同一邮箱号码1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送邮件验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendEmailCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/email/login": {
            "post": {
                "description": "用户使用邮箱和验证码登录并获取令牌，如果用户不存在则自动注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "邮箱验证码登录",
                "parameters": [
                    {
                        "description": "邮箱验证码登录请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.LoginEmailCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TokenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "验证码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/login-code": {
            "post": {
                "description": "用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "验证码登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.LoginCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TokenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "验证码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/login/qrcode": {
            "get": {
                "description": "获取微信扫码登录的二维码，返回二维码图片URL和场景值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "获取登录二维码",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.QrCodeLoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/login/qrcode/status": {
            "post": {
                "description": "检查微信扫码登录的二维码状态，如果已扫码并且用户存在则返回token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "检查二维码状态",
                "parameters": [
                    {
                        "description": "二维码状态请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.QrCodeStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.QrCodeStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/sms/bind-phone-code": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "向指定手机号发送绑定手机号验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送绑定手机号验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendSMSCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/sms/code": {
            "post": {
                "description": "向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送短信验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendSMSCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/categories": {
            "get": {
                "description": "获取所有分类数据，按分类类型分组返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/分类管理"
                ],
                "summary": "获取所有分类数据",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.CategoryGroupResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/categories/tdk/{slug}": {
            "get": {
                "description": "先查询分类表slug_cn，存在则返回分类TDK数据，不存在则查询职位表TDK数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/分类管理"
                ],
                "summary": "根据slug获取分类TDK信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "分类或职位的slug",
                        "name": "slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.CategoryTdkResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "分类不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/categories/{slug}": {
            "get": {
                "description": "先查询分类表slug_cn，存在则返回分类数据，不存在则查询职位表并返回完整层级结构",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/分类管理"
                ],
                "summary": "根据slug获取分类详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "分类或职位的slug",
                        "name": "slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.CategoryDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "分类不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/categories/{slug}/examples": {
            "get": {
                "description": "根据分类或职位的slug获取对应的示例列表，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/分类管理"
                ],
                "summary": "根据slug获取示例列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "分类或职位的slug",
                        "name": "slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 40,
                        "description": "每页条数，默认为40",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ExampleListSwaggerResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "分类不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/enums": {
            "get": {
                "description": "获取系统中定义的枚举列表，返回枚举的code和name，方便前端下拉组件使用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取枚举列表",
                "responses": {
                    "200": {
                        "description": "获取成功\" {\"code\":0,\"message\":\"获取枚举列表成功\",\"data\":{\"account_status\":[{\"code\":0,\"name\":\"正常\"},{\"code\":1,\"name\":\"异常\"}],\"notice_status\":[{\"code\":0,\"name\":\"待处理\"},{\"code\":1,\"name\":\"已接受\"},{\"code\":2,\"name\":\"已拒绝\"}]}}",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_SimpleEnumsResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/download-coupon-plans": {
            "get": {
                "description": "获取所有可见的下载券套餐，只返回基本信息（ID、名称、价格），按照排序字段降序排列（值越大越靠前）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/会员套餐"
                ],
                "summary": "获取所有可见的下载券套餐",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.DownloadCouponPlanListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/homepage": {
            "get": {
                "description": "随机获取16个示例用于首页展示，包含预览图、标签、名称等信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "获取首页示例列表",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.ExampleListItemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/id/{id}": {
            "get": {
                "description": "根据示例ID获取示例的详细信息，包括简历内容和模板样式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "根据ID获取示例详情",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "示例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ExampleDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "示例不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/recommendations": {
            "get": {
                "description": "随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，支持永久缓存",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "获取模板推荐列表（带永久缓存）",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.ExampleListItemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/recommendations/fresh": {
            "get": {
                "description": "随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，实时查询不使用缓存",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "获取模板推荐列表（不带缓存）",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.ExampleListItemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/tdk/{id}": {
            "get": {
                "description": "根据示例ID获取示例的TDK（标题、描述、关键词）信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "根据ID获取示例TDK信息",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "示例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ExampleTdkResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "示例不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/examples/use": {
            "post": {
                "description": "根据例子ID复制例子的内容创建新的简历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/示例管理"
                ],
                "summary": "使用例子创建简历",
                "parameters": [
                    {
                        "description": "使用例子请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UseExampleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UseExampleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "示例不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/membership-plans": {
            "get": {
                "description": "获取所有可见的会员套餐，按照排序字段降序排列（值越大越靠前）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/会员套餐"
                ],
                "summary": "获取所有可见的会员套餐",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.MembershipPlanListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/openapi/alipay": {
            "post": {
                "description": "处理支付宝支付回调通知，验证签名并处理订单状态",
                "consumes": [
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "API/OpenAPI"
                ],
                "summary": "支付宝支付回调",
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "fail",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/openapi/wechatpay": {
            "post": {
                "description": "处理微信支付回调通知，验证签名并处理订单状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "API/OpenAPI"
                ],
                "summary": "微信支付回调",
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "fail",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/orders": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "分页获取当前用户的订单列表，支持按支付状态筛选，按创建时间倒序排列",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/订单管理"
                ],
                "summary": "获取用户订单列表",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "example": 1,
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "example": 10,
                        "description": "每页条数，默认为10，最大100",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"1,3\"",
                        "description": "支付状态筛选：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时，支持多个状态组合查询(用逗号分隔)，不传则查询所有状态",
                        "name": "payment_status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.OrderListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/orders/alipay": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "创建支付宝订单并返回支付二维码链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/订单管理"
                ],
                "summary": "创建支付宝订单",
                "parameters": [
                    {
                        "description": "创建订单请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.OrderResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "会员套餐不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/orders/status": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据订单号查询订单状态，返回订单号、支付状态(整数值:1待支付 2支付处理中 3支付成功 4支付失败 5支付超时)和失败原因(如果有)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/订单管理"
                ],
                "summary": "查询订单状态",
                "parameters": [
                    {
                        "description": "查询订单状态请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.QueryOrderStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.OrderStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "订单不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/orders/wechat": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "创建微信支付订单并返回支付二维码链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/订单管理"
                ],
                "summary": "创建微信支付订单",
                "parameters": [
                    {
                        "description": "创建订单请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.OrderResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "会员套餐不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/positions": {
            "get": {
                "description": "获取所有职位信息，按层级结构返回，支持无限层级嵌套",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/职位管理"
                ],
                "summary": "获取所有职位信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.PositionItemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/positions/search/{keyword}": {
            "get": {
                "description": "根据关键字搜索职位和示例信息，支持按名称和slug_cn搜索，返回包含type字段区分职位(1)和示例(2)的结果，最多返回10条结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/职位管理"
                ],
                "summary": "搜索职位和示例",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "keyword",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "搜索成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.SearchResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/positions/{parent_id}/children": {
            "get": {
                "description": "根据一级分类ID获取下面的二级三级分类，按层级结构返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/职位管理"
                ],
                "summary": "根据一级分类ID获取子分类",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "一级分类ID",
                        "name": "parent_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.PositionItemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resume-drafts/{draft_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历草稿ID获取简历草稿的详细信息，包括基本信息、教育经历、工作经历等所有模块",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "获取简历草稿详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历草稿ID",
                        "name": "draft_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ResumeDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历草稿",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历草稿不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resume-scores": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的所有简历评分记录列表，包含简历名称、总体评分、创建时间等信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历评分管理"
                ],
                "summary": "获取我的所有简历评分记录",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "example": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_ResumeScoreListResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/resume-scores/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据评分记录ID获取简历评分的详细信息，包含四个维度的评分和详细评价",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历评分管理"
                ],
                "summary": "根据ID获取简历评分详情",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "评分记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-vo_ResumeScoreDetailResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "404": {
                        "description": "评分记录不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据评分记录ID删除简历评分记录，只有记录的所有者才能删除",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历评分管理"
                ],
                "summary": "删除简历评分记录",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "评分记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "404": {
                        "description": "评分记录不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response-any"
                        }
                    }
                }
            }
        },
        "/api/resumes": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的所有简历列表，只返回简历表的基本信息，按更新时间排序",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "获取所有自己的简历",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.GetAllMyResumesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/online": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "记录用户在线状态并返回当前在线人数（5分钟内活跃用户）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "记录在线用户",
                "responses": {
                    "200": {
                        "description": "记录成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "integer"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/trash": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户回收站中的所有简历列表，只返回已删除的简历基本信息，按删除时间排序",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "获取回收站简历",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.GetDeletedResumesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID获取简历的详细信息，包括基本信息、教育经历、工作经历等所有模块",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "获取简历详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ResumeDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "保存简历的所有模块内容，包括基本信息、教育经历、工作经历等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "保存简历详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "简历详情数据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SaveResumeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "保存成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.SaveResumeResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID删除简历及其所有相关数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "删除简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/apply-draft": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将草稿数据覆盖到指定简历，用草稿内容更新简历的所有模块数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "应用草稿到简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "应用草稿数据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ApplyDraftRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "应用成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ApplyDraftResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历或草稿",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历或草稿不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/basic": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID获取简历的基本信息（ID、名称、预览图、创建时间）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "获取简历基本信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.GetResumeBasicInfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/copy": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID复制简历及其所有相关数据，新简历名称会在原名称后加上\"-复制\"",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "复制简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "复制成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/download": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID生成并下载PDF文件",
                "produces": [
                    "application/pdf"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "下载简历PDF",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "PDF文件",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/name": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "修改指定简历的名称",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "修改简历名称",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "简历名称数据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateResumeNameRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UpdateResumeNameResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/permanently": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID彻底删除简历及其所有相关数据，只能删除回收站中的简历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "物理删除简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "彻底删除成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历或简历不在回收站",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/restore": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据简历ID从回收站恢复简历，只能恢复回收站中的简历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "恢复简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "恢复成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历或简历不在回收站",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/resumes/{resume_id}/share/email": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "生成简历PDF并通过邮件发送给指定邮箱地址",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/简历管理"
                ],
                "summary": "邮件分享简历",
                "parameters": [
                    {
                        "type": "string",
                        "description": "简历ID",
                        "name": "resume_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "邮件分享数据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ShareResumeByEmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分享成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ShareResumeByEmailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权访问该简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "简历不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/target-positions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的所有目标岗位列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/目标岗位管理"
                ],
                "summary": "获取用户所有目标岗位",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TargetPositionListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户创建新的目标岗位信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/目标岗位管理"
                ],
                "summary": "创建目标岗位",
                "parameters": [
                    {
                        "description": "创建目标岗位请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.CreateTargetPositionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.CreateTargetPositionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/target-positions/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据岗位ID获取目标岗位的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/目标岗位管理"
                ],
                "summary": "根据ID获取目标岗位详情",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "岗位ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TargetPositionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "目标岗位不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户更新已有的目标岗位信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/目标岗位管理"
                ],
                "summary": "更新目标岗位",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "岗位ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新目标岗位请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateTargetPositionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "目标岗位不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户删除已有的目标岗位信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/目标岗位管理"
                ],
                "summary": "删除目标岗位",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "岗位ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "目标岗位不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/templates": {
            "get": {
                "description": "分页获取模板列表，返回模板ID、名称、标签、预览图和使用人数",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/模板管理"
                ],
                "summary": "获取模板列表",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 20,
                        "description": "每页条数，默认为20",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TemplateListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/templates/use": {
            "post": {
                "description": "将指定模板应用到简历，更新简历的模板ID和样式配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/模板管理"
                ],
                "summary": "使用模板",
                "parameters": [
                    {
                        "description": "使用模板请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UseTemplateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "应用成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "403": {
                        "description": "无权限修改此简历",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "模板不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/upload/attachment": {
            "post": {
                "description": "上传附件，支持PDF、DOC、DOCX、JPG、PNG、ZIP、RAR等格式，最大20MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/文件上传"
                ],
                "summary": "上传附件",
                "parameters": [
                    {
                        "type": "file",
                        "description": "附件文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UploadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/upload/avatar": {
            "post": {
                "description": "上传用户头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/文件上传"
                ],
                "summary": "上传头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UploadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/avatar": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/bind-email": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "为当前登录用户绑定邮箱，需要先发送验证码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "绑定邮箱",
                "parameters": [
                    {
                        "description": "绑定邮箱请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BindEmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/bind-phone": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "为当前登录用户绑定手机号，需要先发送验证码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "绑定手机号",
                "parameters": [
                    {
                        "description": "绑定手机号请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BindPhoneRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/bind-wechat/qrcode": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户绑定微信的二维码，返回二维码图片URL和场景值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取绑定微信二维码",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.QrCodeLoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/bind-wechat/qrcode/status": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "检查绑定微信二维码的扫描状态，如果已扫描则执行绑定操作",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "检查绑定微信二维码状态",
                "parameters": [
                    {
                        "description": "检查绑定微信二维码状态请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.CheckBindWechatQrCodeStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "检查成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.QrCodeStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/download-coupons/count": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户可用的下载券数量",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取用户可用的下载券数量",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.AvailableCouponsCountResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/guest/info": {
            "get": {
                "description": "获取当前游客用户的基本信息，需要指纹认证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取游客用户基本信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "缺少浏览器指纹",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/info": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取当前登录用户信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/logout": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "用户退出登录，清除登录状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "退出成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/username": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的用户名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户名",
                "parameters": [
                    {
                        "description": "修改用户名请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateUsernameRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/wechat/serve": {
            "get": {
                "description": "处理微信公众号服务器配置时的验证请求",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "API/微信管理"
                ],
                "summary": "处理微信服务器验证请求",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "post": {
                "description": "接收微信公众号的消息推送，包括普通消息、事件推送等",
                "consumes": [
                    "text/xml"
                ],
                "produces": [
                    "text/xml"
                ],
                "tags": [
                    "API/微信管理"
                ],
                "summary": "接收微信服务器消息推送",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.ApplyDraftRequest": {
            "type": "object",
            "required": [
                "draft_id",
                "resume_id"
            ],
            "properties": {
                "draft_id": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                },
                "resume_id": {
                    "type": "string",
                    "minLength": 1,
                    "example": "1"
                }
            }
        },
        "dto.BatchValidatePrivilegeRequest": {
            "type": "object",
            "required": [
                "privilege_types"
            ],
            "properties": {
                "privilege_types": {
                    "description": "需要校验的权限类型列表",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/enum.PrivilegeType"
                    }
                }
            }
        },
        "dto.BindEmailRequest": {
            "type": "object",
            "required": [
                "code",
                "email"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "dto.BindPhoneRequest": {
            "type": "object",
            "required": [
                "code",
                "phone"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                }
            }
        },
        "dto.CheckBindWechatQrCodeStatusRequest": {
            "type": "object",
            "required": [
                "scene_id"
            ],
            "properties": {
                "scene_id": {
                    "type": "string",
                    "example": "bind_1234567890_123456789"
                }
            }
        },
        "dto.CreateOrderRequest": {
            "type": "object",
            "required": [
                "plan_id"
            ],
            "properties": {
                "bd_vid": {
                    "description": "百度投放ID",
                    "type": "string",
                    "example": "bd123456"
                },
                "plan_id": {
                    "description": "会员套餐ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dto.CreateTargetPositionRequest": {
            "type": "object",
            "required": [
                "company_name",
                "job_description",
                "job_source",
                "position_name"
            ],
            "properties": {
                "company_name": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "阿里巴巴"
                },
                "job_description": {
                    "type": "string",
                    "example": "负责前端页面开发..."
                },
                "job_source": {
                    "type": "string",
                    "maxLength": 50,
                    "example": "Boss直聘"
                },
                "position_name": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "前端开发工程师"
                }
            }
        },
        "dto.GenerateResumeRequest": {
            "type": "object",
            "required": [
                "prompt"
            ],
            "properties": {
                "prompt": {
                    "description": "话术内容",
                    "type": "string",
                    "maxLength": 5000,
                    "example": "我是一名软件工程师，有3年Java开发经验，熟悉Spring框架，参与过电商项目开发"
                },
                "template_id": {
                    "description": "模板ID（可选）",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dto.GenerateResumeResponse": {
            "type": "object",
            "properties": {
                "resume_id": {
                    "description": "新创建的简历ID",
                    "type": "integer",
                    "example": 123
                }
            }
        },
        "dto.LoginCodeRequest": {
            "type": "object",
            "required": [
                "code",
                "phone"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                }
            }
        },
        "dto.LoginEmailCodeRequest": {
            "type": "object",
            "required": [
                "code",
                "email"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "dto.OptimizeResumeRequest": {
            "type": "object",
            "required": [
                "resume_id"
            ],
            "properties": {
                "resume_id": {
                    "description": "简历ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dto.OptimizeResumeResponse": {
            "type": "object",
            "properties": {
                "draft_id": {
                    "description": "简历草稿ID",
                    "type": "integer",
                    "example": 456
                }
            }
        },
        "dto.PromptRequest": {
            "type": "object",
            "required": [
                "desc",
                "module",
                "prompt_type",
                "resume_id"
            ],
            "properties": {
                "desc": {
                    "description": "描述字段",
                    "type": "string",
                    "maxLength": 2000,
                    "example": "我是一名软件工程师，有3年开发经验"
                },
                "module": {
                    "description": "模块名称（枚举）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.ResumeModule"
                        }
                    ],
                    "example": "basic_info"
                },
                "prompt_type": {
                    "description": "提示词类型（枚举）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.PromptType"
                        }
                    ],
                    "example": "generate"
                },
                "resume_id": {
                    "description": "简历ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dto.QrCodeStatusRequest": {
            "type": "object",
            "required": [
                "scene_id"
            ],
            "properties": {
                "scene_id": {
                    "type": "string",
                    "example": "login_1234567890_123456"
                }
            }
        },
        "dto.QueryOrderStatusRequest": {
            "type": "object",
            "required": [
                "order_no"
            ],
            "properties": {
                "order_no": {
                    "description": "订单号",
                    "type": "string",
                    "example": "AL00011234567890"
                }
            }
        },
        "dto.SaveResumeRequest": {
            "type": "object",
            "required": [
                "basic_info",
                "completion_rate",
                "custom_modules",
                "education",
                "honors",
                "other",
                "personal_summary",
                "portfolio",
                "project",
                "research",
                "resume_id",
                "resume_style",
                "skills",
                "slogan",
                "team",
                "work"
            ],
            "properties": {
                "basic_info": {
                    "$ref": "#/definitions/models.BasicInfo"
                },
                "completion_rate": {
                    "type": "string",
                    "example": "75%"
                },
                "custom_modules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CustomModule"
                    }
                },
                "education": {
                    "$ref": "#/definitions/models.Education"
                },
                "honors": {
                    "$ref": "#/definitions/models.Honors"
                },
                "other": {
                    "$ref": "#/definitions/models.Other"
                },
                "personal_summary": {
                    "$ref": "#/definitions/models.PersonalSummary"
                },
                "portfolio": {
                    "$ref": "#/definitions/models.Portfolio"
                },
                "project": {
                    "$ref": "#/definitions/models.Project"
                },
                "research": {
                    "$ref": "#/definitions/models.Research"
                },
                "resume_id": {
                    "type": "string",
                    "minLength": 1,
                    "example": "1"
                },
                "resume_style": {
                    "$ref": "#/definitions/models.ResumeStyle"
                },
                "skills": {
                    "$ref": "#/definitions/models.Skills"
                },
                "slogan": {
                    "$ref": "#/definitions/models.Slogan"
                },
                "team": {
                    "$ref": "#/definitions/models.Team"
                },
                "work": {
                    "$ref": "#/definitions/models.Work"
                }
            }
        },
        "dto.ScoreResumeRequest": {
            "type": "object",
            "required": [
                "position_id",
                "resume_id"
            ],
            "properties": {
                "position_id": {
                    "description": "目标岗位ID（来自target_position表）",
                    "type": "integer",
                    "example": 1
                },
                "resume_id": {
                    "description": "简历ID",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dto.SendEmailCodeRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "dto.SendSMSCodeRequest": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                }
            }
        },
        "dto.ShareResumeByEmailRequest": {
            "type": "object",
            "required": [
                "email",
                "file_name",
                "resume_id"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "file_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1,
                    "example": "张三的简历"
                },
                "resume_id": {
                    "type": "string",
                    "minLength": 1,
                    "example": "1"
                }
            }
        },
        "dto.UpdateResumeNameRequest": {
            "type": "object",
            "required": [
                "resume_id",
                "resume_name"
            ],
            "properties": {
                "resume_id": {
                    "type": "string",
                    "minLength": 1,
                    "example": "1"
                },
                "resume_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1,
                    "example": "我的新简历"
                }
            }
        },
        "dto.UpdateTargetPositionRequest": {
            "type": "object",
            "required": [
                "company_name",
                "id",
                "job_description",
                "job_source",
                "position_name"
            ],
            "properties": {
                "company_name": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "阿里巴巴"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "job_description": {
                    "type": "string",
                    "example": "负责前端页面开发..."
                },
                "job_source": {
                    "type": "string",
                    "maxLength": 50,
                    "example": "Boss直聘"
                },
                "position_name": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "前端开发工程师"
                }
            }
        },
        "dto.UpdateUsernameRequest": {
            "type": "object",
            "required": [
                "username"
            ],
            "properties": {
                "username": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 2,
                    "example": "新用户名"
                }
            }
        },
        "dto.UseExampleRequest": {
            "type": "object",
            "required": [
                "example_id"
            ],
            "properties": {
                "example_id": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                }
            }
        },
        "dto.UseTemplateRequest": {
            "type": "object",
            "required": [
                "resume_id",
                "template_id"
            ],
            "properties": {
                "resume_id": {
                    "description": "简历ID",
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                },
                "template_id": {
                    "description": "模板ID",
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                }
            }
        },
        "enum.ModalType": {
            "type": "integer",
            "enum": [
                1,
                2
            ],
            "x-enum-varnames": [
                "ModalTypeLogin",
                "ModalTypeMembership"
            ]
        },
        "enum.PaymentMethod": {
            "type": "integer",
            "enum": [
                1,
                2
            ],
            "x-enum-varnames": [
                "PaymentMethodWechat",
                "PaymentMethodAlipay"
            ]
        },
        "enum.PaymentStatus": {
            "type": "integer",
            "enum": [
                1,
                2,
                3,
                4,
                5
            ],
            "x-enum-varnames": [
                "PaymentStatusPending",
                "PaymentStatusProcessing",
                "PaymentStatusSuccess",
                "PaymentStatusFailed",
                "PaymentStatusTimeout"
            ]
        },
        "enum.PrivilegeType": {
            "type": "integer",
            "enum": [
                1,
                2,
                3,
                4,
                5,
                6,
                7
            ],
            "x-enum-varnames": [
                "PrivilegeResumeDownload",
                "PrivilegeResumeCreate",
                "PrivilegeAIGenerate",
                "PrivilegeAIRewrite",
                "PrivilegeAIOptimize",
                "PrivilegeAIDiagnose",
                "PrivilegeAIOneClick"
            ]
        },
        "enum.PromptType": {
            "type": "string",
            "enum": [
                "generate",
                "continue",
                "professional",
                "concise",
                "detailed",
                "generate_resume",
                "optimize",
                "score"
            ],
            "x-enum-varnames": [
                "PromptTypeGenerate",
                "PromptTypeContinue",
                "PromptTypeProfessional",
                "PromptTypeConcise",
                "PromptTypeDetailed",
                "PromptTypeGenerateResume",
                "PromptTypeOptimize",
                "PromptTypeScore"
            ]
        },
        "enum.ResumeModule": {
            "type": "string",
            "enum": [
                "basic_info",
                "education",
                "work",
                "project",
                "research",
                "team",
                "portfolio",
                "other",
                "personal_summary",
                "honors",
                "skills",
                "custom_modules",
                "slogan",
                "resume_style"
            ],
            "x-enum-varnames": [
                "ResumeModuleBasicInfo",
                "ResumeModuleEducation",
                "ResumeModuleWork",
                "ResumeModuleProject",
                "ResumeModuleResearch",
                "ResumeModuleTeam",
                "ResumeModulePortfolio",
                "ResumeModuleOther",
                "ResumeModulePersonalSummary",
                "ResumeModuleHonors",
                "ResumeModuleSkills",
                "ResumeModuleCustomModules",
                "ResumeModuleSlogan",
                "ResumeModuleResumeStyle"
            ]
        },
        "enum.UserType": {
            "type": "integer",
            "enum": [
                1,
                2,
                3
            ],
            "x-enum-varnames": [
                "UserTypeGuest",
                "UserTypeRegular",
                "UserTypeMember"
            ]
        },
        "models.BasicInfo": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_required": {
                    "type": "boolean"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "$ref": "#/definitions/models.BasicInfoItem"
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.BasicInfoField": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "value": {}
            }
        },
        "models.BasicInfoItem": {
            "type": "object",
            "properties": {
                "avatar": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "avatar_filter": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "birth": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "birth_type": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "created_at": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "customize_fields": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "email": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "ethnicity": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "gender": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "gitee": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "github": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "height": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "intended_city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "job": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "job_status": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "marital": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "max_salary": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "origin": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "phone": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "political_affiliation": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "site": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "updated_at": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "wechat": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "weight": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.CustomModule": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CustomModuleItem"
                    }
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.CustomModuleItem": {
            "type": "object",
            "properties": {
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "role": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Education": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EducationItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.EducationItem": {
            "type": "object",
            "properties": {
                "city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "college_name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "degree": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "description": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_date": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "major": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "school_name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "school_tags": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_date": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Honors": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "$ref": "#/definitions/models.HonorsItem"
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.HonorsItem": {
            "type": "object",
            "properties": {
                "honorWallLayout": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "honorWallStyle": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "values": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Other": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OtherItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.OtherItem": {
            "type": "object",
            "properties": {
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.PersonalSummary": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "$ref": "#/definitions/models.PersonalSummaryItem"
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.PersonalSummaryItem": {
            "type": "object",
            "properties": {
                "summary": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Portfolio": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.PortfolioItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.PortfolioItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "url": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Project": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ProjectItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.ProjectItem": {
            "type": "object",
            "properties": {
                "company": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "role": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Research": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ResearchItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.ResearchItem": {
            "type": "object",
            "properties": {
                "city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "department": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "role": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.ResumeStyle": {
            "type": "object",
            "properties": {
                "avatar_layout": {
                    "description": "'left' | 'center' | 'right'",
                    "type": "string"
                },
                "badge_layout": {
                    "description": "'left' | 'right'",
                    "type": "string"
                },
                "base_info": {
                    "description": "'text' | 'icon' | 'simple'",
                    "type": "string"
                },
                "can_change_avatar_layout": {
                    "type": "boolean"
                },
                "can_change_background_style": {
                    "type": "boolean"
                },
                "can_change_base_info": {
                    "type": "boolean"
                },
                "can_change_color": {
                    "type": "boolean"
                },
                "can_change_date_align": {
                    "type": "boolean"
                },
                "can_change_date_format": {
                    "type": "boolean"
                },
                "can_change_font_family": {
                    "type": "boolean"
                },
                "can_change_font_gray": {
                    "type": "boolean"
                },
                "can_change_font_size": {
                    "type": "boolean"
                },
                "can_change_header_layout": {
                    "description": "功能开关",
                    "type": "boolean"
                },
                "can_change_line_spacing": {
                    "type": "boolean"
                },
                "can_change_module_spacing": {
                    "type": "boolean"
                },
                "can_change_page_margin": {
                    "type": "boolean"
                },
                "can_change_skills_three_columns": {
                    "type": "boolean"
                },
                "can_change_title_align": {
                    "type": "boolean"
                },
                "can_change_title_style": {
                    "type": "boolean"
                },
                "color": {
                    "type": "string"
                },
                "color_count": {
                    "type": "integer"
                },
                "date_align": {
                    "description": "'left' | 'right'",
                    "type": "string"
                },
                "date_format": {
                    "type": "string"
                },
                "font_family": {
                    "description": "字体相关",
                    "type": "string"
                },
                "font_gray": {
                    "type": "string"
                },
                "font_size": {
                    "type": "string"
                },
                "header_layout": {
                    "description": "'left' | 'center' | 'right'",
                    "type": "string"
                },
                "layout_mode": {
                    "type": "string"
                },
                "left_box_width": {
                    "type": "string"
                },
                "line_spacing": {
                    "type": "string"
                },
                "module_spacing": {
                    "description": "布局相关",
                    "type": "string"
                },
                "page_margin": {
                    "type": "string"
                },
                "paper_style": {
                    "type": "string"
                },
                "preset_colors_dual": {
                    "type": "array",
                    "items": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        }
                    }
                },
                "preset_colors_single": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "resume_color": {
                    "description": "主题色相关",
                    "type": "string"
                },
                "resume_color2": {
                    "type": "string"
                },
                "separator": {
                    "type": "string"
                },
                "title_align": {
                    "description": "'left' | 'center' | 'right' | 'justify'",
                    "type": "string"
                },
                "title_color": {
                    "type": "string"
                },
                "title_row": {
                    "type": "string"
                },
                "title_style": {
                    "type": "string"
                }
            }
        },
        "models.Skills": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "$ref": "#/definitions/models.SkillsItem"
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.SkillsItem": {
            "type": "object",
            "properties": {
                "skillLayout": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "skillStyle": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "values": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Slogan": {
            "type": "object",
            "properties": {
                "slogan": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "title": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Team": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TeamItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.TeamItem": {
            "type": "object",
            "properties": {
                "city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "department": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "name": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "role": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "models.Work": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "is_visible": {
                    "type": "boolean"
                },
                "item": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.WorkItem"
                    }
                },
                "name": {
                    "type": "string"
                },
                "support_ai": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.WorkItem": {
            "type": "object",
            "properties": {
                "city": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "company": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "company_tags": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "department": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "desc": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "end_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "id": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "job": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "job_tags": {
                    "$ref": "#/definitions/models.BasicInfoField"
                },
                "start_month": {
                    "$ref": "#/definitions/models.BasicInfoField"
                }
            }
        },
        "response.Response-any": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {},
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-dto_GenerateResumeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/dto.GenerateResumeResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-dto_OptimizeResumeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/dto.OptimizeResumeResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_BatchValidatePrivilegeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.BatchValidatePrivilegeResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_ParseFileResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.ParseFileResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_ResumeScoreDetailResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.ResumeScoreDetailResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_ResumeScoreListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.ResumeScoreListResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_ScoreResumeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.ScoreResumeResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "response.Response-vo_SimpleEnumsResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 0
                },
                "data": {
                    "$ref": "#/definitions/vo.SimpleEnumsResponse"
                },
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "vo.ApplyDraftResponse": {
            "type": "object",
            "properties": {
                "resume_id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.AvailableCouponsCountResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "description": "可用下载券数量",
                    "type": "integer",
                    "example": 5
                }
            }
        },
        "vo.BatchValidatePrivilegeResponse": {
            "type": "object",
            "properties": {
                "all_allowed": {
                    "description": "是否所有权限都通过校验",
                    "type": "boolean",
                    "example": false
                },
                "modal_description": {
                    "description": "弹窗描述（当AllAllowed为false时）",
                    "type": "string",
                    "example": "请先登录后使用此功能"
                },
                "modal_title": {
                    "description": "弹窗标题（当AllAllowed为false时）",
                    "type": "string",
                    "example": "登录提示"
                },
                "modal_type": {
                    "description": "弹窗类型（当AllAllowed为false时）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.ModalType"
                        }
                    ],
                    "example": 1
                }
            }
        },
        "vo.CategoryDetailResponse": {
            "type": "object",
            "properties": {
                "category_type": {
                    "type": "integer"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "examples": {
                    "description": "示例数据（仅职位分类时有值）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.PaginatedList-vo_ExampleListItemResponse"
                        }
                    ]
                },
                "has_data": {
                    "description": "是否查到数据",
                    "type": "boolean"
                },
                "more_recommendations": {
                    "description": "更多推荐",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ExampleListItemResponse"
                    }
                },
                "select_data": {
                    "$ref": "#/definitions/vo.CategoryItemWithChildrenResponse"
                },
                "tags": {
                    "description": "分类标签",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.CategoryTagResponse"
                    }
                }
            }
        },
        "vo.CategoryGroupResponse": {
            "type": "object",
            "properties": {
                "category_type": {
                    "type": "integer"
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.CategoryItemResponse"
                    }
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "vo.CategoryItemResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "slug_cn": {
                    "type": "string"
                },
                "slug_en": {
                    "type": "string"
                }
            }
        },
        "vo.CategoryItemWithChildrenResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.CategoryItemWithChildrenResponse"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "slug_cn": {
                    "type": "string"
                },
                "slug_en": {
                    "type": "string"
                }
            }
        },
        "vo.CategoryTagResponse": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "vo.CategoryTdkResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "专业的软件工程师简历模板，适合后端开发岗位"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "keywords": {},
                "title": {
                    "type": "string",
                    "example": "软件工程师简历模板"
                }
            }
        },
        "vo.CreateTargetPositionResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.DeletedResumeItem": {
            "type": "object",
            "properties": {
                "completion_rate": {
                    "type": "string",
                    "example": "75%"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "deleted_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "preview_image_url": {
                    "type": "string",
                    "example": "https://cdn.avrilko.com/preview/123.jpg"
                },
                "resume_name": {
                    "type": "string",
                    "example": "我的简历"
                },
                "template_id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                }
            }
        },
        "vo.DownloadCouponPlanListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "列表数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.DownloadCouponPlanResponse"
                    }
                }
            }
        },
        "vo.DownloadCouponPlanResponse": {
            "type": "object",
            "properties": {
                "actual_price": {
                    "description": "实际价格(单位:元)",
                    "type": "number",
                    "example": 9.9
                },
                "id": {
                    "description": "套餐ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "套餐名称",
                    "type": "string",
                    "example": "10次下载券"
                },
                "original_price": {
                    "description": "原价(单位:元)",
                    "type": "number",
                    "example": 19.9
                },
                "resume_limit": {
                    "description": "简历数量限制 (0表示无限制)",
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "vo.EnumItem": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "枚举值（支持int和string）"
                },
                "name": {
                    "description": "枚举名称",
                    "type": "string"
                }
            }
        },
        "vo.ErrorAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 400
                },
                "data": {
                    "description": "响应数据，通常为null"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string",
                    "example": "请求参数错误"
                }
            }
        },
        "vo.ExampleDetailResponse": {
            "type": "object",
            "properties": {
                "basic_info": {
                    "description": "简历内容字段",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.BasicInfo"
                        }
                    ]
                },
                "component_name": {
                    "type": "string",
                    "example": "ResumeTemplate1"
                },
                "created_at": {
                    "description": "时间字段",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "custom_modules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CustomModule"
                    }
                },
                "desc": {
                    "description": "描述",
                    "type": "string",
                    "example": "专业的软件工程师简历模板，适合后端开发岗位"
                },
                "education": {
                    "$ref": "#/definitions/models.Education"
                },
                "honors": {
                    "$ref": "#/definitions/models.Honors"
                },
                "hot_resume_recommendations": {
                    "description": "热门简历推荐",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.HotResumeRecommendation"
                    }
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "more_recommendations": {
                    "description": "更多推荐",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ExampleListItemResponse"
                    }
                },
                "other": {
                    "$ref": "#/definitions/models.Other"
                },
                "personal_summary": {
                    "$ref": "#/definitions/models.PersonalSummary"
                },
                "portfolio": {
                    "$ref": "#/definitions/models.Portfolio"
                },
                "project": {
                    "$ref": "#/definitions/models.Project"
                },
                "research": {
                    "$ref": "#/definitions/models.Research"
                },
                "resume_style": {
                    "$ref": "#/definitions/models.ResumeStyle"
                },
                "skills": {
                    "$ref": "#/definitions/models.Skills"
                },
                "slogan": {
                    "$ref": "#/definitions/models.Slogan"
                },
                "tags": {
                    "description": "分类标签和推荐字段",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.CategoryTagResponse"
                    }
                },
                "team": {
                    "$ref": "#/definitions/models.Team"
                },
                "template_id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "usage_count": {
                    "description": "使用人数",
                    "type": "integer",
                    "example": 1250
                },
                "work": {
                    "$ref": "#/definitions/models.Work"
                }
            }
        },
        "vo.ExampleListItemResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "示例ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "模板名称",
                    "type": "string",
                    "example": "软件工程师简历模板"
                },
                "preview_image_url": {
                    "description": "预览图链接",
                    "type": "string",
                    "example": "https://example.com/preview/1.jpg"
                },
                "tags": {
                    "description": "模板标签",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "['简洁'",
                        "'专业'",
                        "'技术']"
                    ]
                },
                "template_id": {
                    "description": "模板ID",
                    "type": "integer",
                    "example": 1
                },
                "usage_count": {
                    "description": "使用人数",
                    "type": "integer",
                    "example": 1250
                }
            }
        },
        "vo.ExampleListSwaggerResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "示例列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ExampleListItemResponse"
                    }
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "description": "每页条数",
                    "type": "integer",
                    "example": 40
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer",
                    "example": 100
                }
            }
        },
        "vo.ExampleTdkResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "专业的软件工程师简历模板，适合后端开发岗位"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "keywords": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"软件工程师\"",
                        "\"简历模板\"",
                        "\"后端开发\"]"
                    ]
                },
                "title": {
                    "type": "string",
                    "example": "软件工程师简历模板"
                }
            }
        },
        "vo.GetAllMyResumesResponse": {
            "type": "object",
            "properties": {
                "resumes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ResumeBasicItem"
                    }
                }
            }
        },
        "vo.GetDeletedResumesResponse": {
            "type": "object",
            "properties": {
                "resumes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.DeletedResumeItem"
                    }
                }
            }
        },
        "vo.GetResumeBasicInfoResponse": {
            "type": "object",
            "properties": {
                "completion_rate": {
                    "type": "string",
                    "example": "75%"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "preview_image_url": {
                    "type": "string",
                    "example": "https://cdn.avrilko.com/resume/preview/123.png"
                },
                "resume_name": {
                    "type": "string",
                    "example": "张三的简历"
                }
            }
        },
        "vo.HotResumeRecommendation": {
            "type": "object",
            "properties": {
                "link": {
                    "description": "简历链接",
                    "type": "string",
                    "example": "/jianli/123"
                },
                "name": {
                    "description": "简历名称",
                    "type": "string",
                    "example": "软件工程师简历模板"
                },
                "usage_count": {
                    "description": "使用人数",
                    "type": "integer",
                    "example": 1250
                }
            }
        },
        "vo.MembershipPlanInfo": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "套餐ID",
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "description": "套餐名称",
                    "type": "string",
                    "example": "月度会员"
                }
            }
        },
        "vo.MembershipPlanListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "列表数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.MembershipPlanResponse"
                    }
                }
            }
        },
        "vo.MembershipPlanResponse": {
            "type": "object",
            "properties": {
                "actual_price": {
                    "description": "实际价格(单位:元)",
                    "type": "number",
                    "example": 29.9
                },
                "ai_diagnose_limit": {
                    "description": "AI简历打分次数限制(0表示无限制)",
                    "type": "integer",
                    "example": 0
                },
                "ai_generate_limit": {
                    "description": "AI生成次数限制(0表示无限制)",
                    "type": "integer",
                    "example": 0
                },
                "ai_one_click_limit": {
                    "description": "AI一键生成简历次数限制(0表示无限制)",
                    "type": "integer",
                    "example": 0
                },
                "ai_optimize_limit": {
                    "description": "AI简历优化次数限制(0表示无限制)",
                    "type": "integer",
                    "example": 0
                },
                "ai_rewrite_limit": {
                    "description": "AI改写次数限制(0表示无限制)",
                    "type": "integer",
                    "example": 0
                },
                "corner_image_url": {
                    "description": "右上角图片地址",
                    "type": "string",
                    "example": "https://example.com/images/corner.png"
                },
                "description": {
                    "description": "描述信息",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "无限简历创建",
                        "高级模板使用权",
                        "AI优化建议"
                    ]
                },
                "discount_tip": {
                    "description": "优惠提示",
                    "type": "string",
                    "example": "限时优惠"
                },
                "id": {
                    "description": "套餐ID",
                    "type": "integer",
                    "example": 1
                },
                "is_default": {
                    "description": "是否默认 1:非默认 2:默认",
                    "type": "integer",
                    "example": 2
                },
                "name": {
                    "description": "套餐名称",
                    "type": "string",
                    "example": "月度会员"
                },
                "original_price": {
                    "description": "原价(单位:元)",
                    "type": "number",
                    "example": 39.9
                },
                "resume_limit": {
                    "description": "能创建简历的个数(0表示无限制)",
                    "type": "integer",
                    "example": 0
                }
            }
        },
        "vo.OrderListItem": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "订单金额",
                    "type": "number",
                    "example": 29.9
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "fail_reason": {
                    "description": "支付失败原因",
                    "type": "string",
                    "example": ""
                },
                "id": {
                    "description": "订单ID",
                    "type": "integer",
                    "example": 1
                },
                "membership_plan": {
                    "description": "套餐信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.MembershipPlanInfo"
                        }
                    ]
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string",
                    "example": "AL00011234567890"
                },
                "payment_method": {
                    "description": "支付方式：1微信支付 2支付宝",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.PaymentMethod"
                        }
                    ],
                    "example": 2
                },
                "payment_status": {
                    "description": "支付状态：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.PaymentStatus"
                        }
                    ],
                    "example": 1
                },
                "payment_status_str": {
                    "description": "支付状态文字描述",
                    "type": "string",
                    "example": "待支付"
                },
                "title": {
                    "description": "订单标题",
                    "type": "string",
                    "example": "【熊猫简历】购买会员套餐-月度会员"
                }
            }
        },
        "vo.OrderListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "订单列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.OrderListItem"
                    }
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "description": "每页条数",
                    "type": "integer",
                    "example": 10
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer",
                    "example": 100
                }
            }
        },
        "vo.OrderResponse": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "订单金额",
                    "type": "number",
                    "example": 29.9
                },
                "code_url": {
                    "description": "支付二维码链接",
                    "type": "string",
                    "example": "https://qr.alipay.com/xxx"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string",
                    "example": "RS00011234567890"
                },
                "package_id": {
                    "description": "套餐ID",
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "description": "订单标题",
                    "type": "string",
                    "example": "【熊猫简历】购买会员套餐-月度会员"
                }
            }
        },
        "vo.OrderStatusResponse": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "订单金额",
                    "type": "number",
                    "example": 29.9
                },
                "channel_name": {
                    "description": "用户渠道名称",
                    "type": "string",
                    "example": "百度推广"
                },
                "fail_reason": {
                    "description": "支付失败原因，仅当支付状态为失败或超时时有值",
                    "type": "string",
                    "example": "支付超时"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string",
                    "example": "AL00011234567890"
                },
                "payment_status": {
                    "description": "支付状态(1:待支付 2:支付处理中 3:支付成功 4:支付失败 5:支付超时)",
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.PaginatedList-vo_ExampleListItemResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "数据列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ExampleListItemResponse"
                    }
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "description": "每页条数",
                    "type": "integer",
                    "example": 10
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer",
                    "example": 100
                }
            }
        },
        "vo.ParseFileResponse": {
            "type": "object",
            "properties": {
                "content": {
                    "description": "解析后的文件内容",
                    "type": "string",
                    "example": "这是解析后的文件内容..."
                },
                "file_size": {
                    "description": "文件大小（字节）",
                    "type": "integer",
                    "example": 1024000
                },
                "file_type": {
                    "description": "文件类型",
                    "type": "string",
                    "example": "pdf"
                },
                "filename": {
                    "description": "原始文件名",
                    "type": "string",
                    "example": "document.pdf"
                }
            }
        },
        "vo.PositionItemResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.PositionItemResponse"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "slug_cn": {
                    "type": "string"
                },
                "slug_en": {
                    "type": "string"
                }
            }
        },
        "vo.QrCodeLoginResponse": {
            "type": "object",
            "properties": {
                "qr_code_url": {
                    "description": "二维码图片URL",
                    "type": "string",
                    "example": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxxxx"
                },
                "scene_id": {
                    "description": "场景值ID，前端需要保存此ID用于后续查询扫码状态",
                    "type": "string",
                    "example": "login_123456789"
                }
            }
        },
        "vo.QrCodeStatusResponse": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "二维码状态：PENDING(待扫描)、SCANNED(已扫描)、EXPIRED(已过期)",
                    "type": "string",
                    "example": "SCANNED"
                },
                "token": {
                    "description": "令牌，仅当状态为SCANNED时返回",
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "vo.ResumeBasicItem": {
            "type": "object",
            "properties": {
                "completion_rate": {
                    "type": "string",
                    "example": "75%"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "preview_image_url": {
                    "type": "string",
                    "example": "https://cdn.avrilko.com/preview/123.jpg"
                },
                "resume_name": {
                    "type": "string",
                    "example": "我的简历"
                },
                "template_id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                }
            }
        },
        "vo.ResumeDetailResponse": {
            "type": "object",
            "properties": {
                "basic_info": {
                    "description": "简历详情内容",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.BasicInfo"
                        }
                    ]
                },
                "completion_rate": {
                    "type": "string",
                    "example": "75%"
                },
                "component_name": {
                    "type": "string",
                    "example": "ResumeTemplate1"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "custom_modules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CustomModule"
                    }
                },
                "diff": {
                    "type": "string",
                    "example": "优化了工作经历描述，增强了项目经历的技术细节"
                },
                "education": {
                    "$ref": "#/definitions/models.Education"
                },
                "honors": {
                    "$ref": "#/definitions/models.Honors"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "other": {
                    "$ref": "#/definitions/models.Other"
                },
                "personal_summary": {
                    "$ref": "#/definitions/models.PersonalSummary"
                },
                "portfolio": {
                    "$ref": "#/definitions/models.Portfolio"
                },
                "preview_image_url": {
                    "type": "string",
                    "example": "https://cdn.avrilko.com/preview/123.jpg"
                },
                "project": {
                    "$ref": "#/definitions/models.Project"
                },
                "research": {
                    "$ref": "#/definitions/models.Research"
                },
                "resume_name": {
                    "type": "string",
                    "example": "我的简历"
                },
                "resume_style": {
                    "$ref": "#/definitions/models.ResumeStyle"
                },
                "skills": {
                    "$ref": "#/definitions/models.Skills"
                },
                "slogan": {
                    "$ref": "#/definitions/models.Slogan"
                },
                "team": {
                    "$ref": "#/definitions/models.Team"
                },
                "template_id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "user_id": {
                    "type": "integer",
                    "example": 1
                },
                "work": {
                    "$ref": "#/definitions/models.Work"
                }
            }
        },
        "vo.ResumeScoreDetailResponse": {
            "type": "object",
            "properties": {
                "content_relevance_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ScoreDetailItem"
                    }
                },
                "content_relevance_score": {
                    "type": "number",
                    "example": 78
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "information_completeness_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ScoreDetailItem"
                    }
                },
                "information_completeness_score": {
                    "type": "number",
                    "example": 82
                },
                "language_expression_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ScoreDetailItem"
                    }
                },
                "language_expression_score": {
                    "type": "number",
                    "example": 85.5
                },
                "overall_comment": {
                    "type": "string",
                    "example": "简历整体质量较高..."
                },
                "overall_score": {
                    "type": "number",
                    "example": 83.4
                },
                "professionalism_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ScoreDetailItem"
                    }
                },
                "professionalism_score": {
                    "type": "number",
                    "example": 88
                },
                "resume_id": {
                    "type": "integer",
                    "example": 1
                },
                "resume_name": {
                    "type": "string",
                    "example": "我的简历"
                },
                "target_position_id": {
                    "type": "integer",
                    "example": 1
                },
                "target_position_name": {
                    "type": "string",
                    "example": "前端开发工程师"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "user_id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.ResumeScoreListItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "overall_score": {
                    "type": "number",
                    "example": 83.4
                },
                "resume_id": {
                    "type": "integer",
                    "example": 1
                },
                "resume_name": {
                    "type": "string",
                    "example": "我的简历"
                }
            }
        },
        "vo.ResumeScoreListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ResumeScoreListItem"
                    }
                },
                "total": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "vo.SaveResumeResponse": {
            "type": "object",
            "properties": {
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                }
            }
        },
        "vo.ScoreDetailItem": {
            "type": "object",
            "properties": {
                "comment": {
                    "description": "评价",
                    "type": "string",
                    "example": "语言表达清晰，逻辑性强，但部分描述可以更加简洁"
                },
                "title": {
                    "description": "小标题",
                    "type": "string",
                    "example": "语言表达清晰度"
                }
            }
        },
        "vo.ScoreDimension": {
            "type": "object",
            "properties": {
                "details": {
                    "description": "详细评价项目",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ScoreDetailItem"
                    }
                },
                "score": {
                    "description": "评分 (0-100)",
                    "type": "number",
                    "example": 85.5
                }
            }
        },
        "vo.ScoreResumeResponse": {
            "type": "object",
            "properties": {
                "content_relevance": {
                    "description": "内容相关性评分",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.ScoreDimension"
                        }
                    ]
                },
                "information_completeness": {
                    "description": "信息完整性评分",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.ScoreDimension"
                        }
                    ]
                },
                "language_expression": {
                    "description": "语言与表达评分",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.ScoreDimension"
                        }
                    ]
                },
                "overall_comment": {
                    "description": "总体评价",
                    "type": "string",
                    "example": "简历整体质量较高，建议在项目经历部分增加更多量化数据"
                },
                "overall_score": {
                    "description": "总体评分",
                    "type": "number",
                    "example": 85.5
                },
                "professionalism": {
                    "description": "简历专业性评分",
                    "allOf": [
                        {
                            "$ref": "#/definitions/vo.ScoreDimension"
                        }
                    ]
                }
            }
        },
        "vo.SearchResponse": {
            "type": "object",
            "properties": {
                "estimatedTotalHits": {
                    "description": "预估总数",
                    "type": "integer"
                },
                "hits": {
                    "description": "搜索结果列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.SearchResultItem"
                    }
                },
                "limit": {
                    "description": "返回条数限制",
                    "type": "integer"
                },
                "offset": {
                    "description": "偏移量",
                    "type": "integer"
                },
                "processingTimeMs": {
                    "description": "处理时间（毫秒）",
                    "type": "integer"
                },
                "query": {
                    "description": "搜索关键词",
                    "type": "string"
                }
            }
        },
        "vo.SearchResultItem": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "文档ID，格式：position_123 或 example_456",
                    "type": "string"
                },
                "name": {
                    "description": "position的名称或者example表name",
                    "type": "string"
                },
                "slug_cn": {
                    "description": "slug_cn字段",
                    "type": "string"
                },
                "type": {
                    "description": "1为position 2为example",
                    "type": "integer"
                },
                "url": {
                    "description": "position为/jianli/{slug_cn} example为/jianli/{id}.html",
                    "type": "string"
                }
            }
        },
        "vo.ShareResumeByEmailResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "example": "简历邮件发送成功"
                }
            }
        },
        "vo.SimpleEnumsResponse": {
            "type": "object",
            "additionalProperties": {
                "type": "array",
                "items": {
                    "$ref": "#/definitions/vo.EnumItem"
                }
            }
        },
        "vo.SuccessAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 200
                },
                "data": {
                    "description": "响应数据"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "vo.TargetPositionListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.TargetPositionResponse"
                    }
                },
                "total": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "vo.TargetPositionResponse": {
            "type": "object",
            "properties": {
                "company_name": {
                    "type": "string",
                    "example": "阿里巴巴"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "job_description": {
                    "type": "string",
                    "example": "负责前端页面开发..."
                },
                "job_source": {
                    "type": "string",
                    "example": "Boss直聘"
                },
                "position_name": {
                    "type": "string",
                    "example": "前端开发工程师"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "user_id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.TemplateListItemResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "模板ID",
                    "type": "integer",
                    "example": 1
                },
                "preview_image_url": {
                    "description": "预览图链接",
                    "type": "string",
                    "example": "https://example.com/preview/1.jpg"
                },
                "tags": {
                    "description": "模板标签",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "['简洁'",
                        "'专业'",
                        "'技术']"
                    ]
                },
                "template_name": {
                    "description": "模板名称",
                    "type": "string",
                    "example": "简洁风格简历模板"
                },
                "usage_count": {
                    "description": "使用人数",
                    "type": "integer",
                    "example": 1250
                }
            }
        },
        "vo.TemplateListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "模板列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.TemplateListItemResponse"
                    }
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "description": "每页条数",
                    "type": "integer",
                    "example": 20
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer",
                    "example": 100
                }
            }
        },
        "vo.TokenResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "vo.UpdateResumeNameResponse": {
            "type": "object",
            "properties": {
                "resume_name": {
                    "type": "string",
                    "example": "我的新简历"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                }
            }
        },
        "vo.UploadResponse": {
            "type": "object",
            "properties": {
                "filename": {
                    "description": "文件名",
                    "type": "string",
                    "example": "123456789.jpg"
                },
                "url": {
                    "description": "文件URL",
                    "type": "string",
                    "example": "https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg"
                }
            }
        },
        "vo.UseExampleResponse": {
            "type": "object",
            "properties": {
                "resume_id": {
                    "type": "integer",
                    "example": 123
                }
            }
        },
        "vo.UserResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string",
                    "example": "http://example.com/avatar.jpg"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "is_logged_in": {
                    "description": "是否登录，JWT认证成功为true，指纹认证为false",
                    "type": "boolean",
                    "example": true
                },
                "open_id": {
                    "type": "string",
                    "example": "oNHwxjgrzgL9H_A2pGLSMuME-X-Q"
                },
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                },
                "user_type": {
                    "description": "用户类型 1:游客 2:普通用户 3:会员",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.UserType"
                        }
                    ],
                    "example": 2
                },
                "username": {
                    "type": "string",
                    "example": "johndoe"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8082",
	BasePath:         "/",
	Schemes:          []string{"http", "https"},
	Title:            "Resume Server API",
	Description:      "Resume Server API documentation",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
